Identity=CMakeCUDACompilerId.cu
AdditionalCompilerOptions=
AdditionalCompilerOptions=
AdditionalDependencies=
AdditionalDeps=
AdditionalLibraryDirectories=
AdditionalOptions=-v -allow-unsupported-compiler
AdditionalOptions=-v -allow-unsupported-compiler
CodeGeneration=compute_52,sm_52
CodeGeneration=compute_52,sm_52
CompileOut=C:\Dev\Camera_Editor\CMakeFiles\4.0.1\CompilerIdCUDA\CompilerIdCUDA\x64\Debug\CMakeCUDACompilerId.cu.obj
CudaRuntime=Static
CudaToolkitCustomDir=
DebugInformationFormat=ProgramDatabase
DebugInformationFormat=ProgramDatabase
Defines=;_MBCS;
Emulation=false
EnableVirtualArchInFatbin=true
ExtensibleWholeProgramCompilation=false
FastMath=false
GenerateLineInfo=false
GenerateRelocatableDeviceCode=false
GPUDebugInfo=true
GPUDebugInfo=true
HostDebugInfo=true
Include=;;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\include
Inputs=
InterleaveSourceInPTX=false
Keep=false
KeepDir=CompilerIdCUDA\x64\Debug
LinkOut=
MaxRegCount=0
NvccCompilation=compile
NvccPath=
Optimization=Od
Optimization=Od
PerformDeviceLink=
PerformDeviceLinkTimeOptimization=
PtxAsOptionV=false
RequiredIncludes=
Runtime=MDd
Runtime=MDd
RuntimeChecks=RTC1
RuntimeChecks=RTC1
SplitCompile=Default
SplitCompileCustomThreads=
TargetMachinePlatform=64
TargetMachinePlatform=64
TypeInfo=
TypeInfo=
UseHostDefines=true
UseHostInclude=true
UseHostLibraryDependencies=
UseHostLibraryDirectories=
Warning=W0
Warning=W0
