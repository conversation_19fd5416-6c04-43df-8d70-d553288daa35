/* the compile-time options used to build the SQLite3 library
 *
 * https://github.com/azadkuh/sqlite-amalgamation
 */

#ifndef SQLITE3_CONFIG_H
#define SQLITE3_CONFIG_H


#cmakedefine SQLITE_ENABLE_COLUMN_METADATA
#cmakedefine SQLITE_ENABLE_DBSTAT_VTAB
#cmakedefine SQLITE_ENABLE_FTS3
#cmakedefine SQLITE_ENABLE_FTS4
#cmakedefine SQLITE_ENABLE_FTS5
#cmakedefine SQLITE_ENABLE_GEOPOLY
#cmakedefine SQLITE_ENABLE_ICU
#cmakedefine SQLITE_ENABLE_MATH_FUNCTIONS
#cmakedefine SQLITE_ENABLE_RBU
#cmakedefine SQLITE_ENABLE_RTREE
#cmakedefine SQLITE_ENABLE_STAT4
#cmakedefine SQLITE_OMIT_DECLTYPE
#cmakedefine SQLITE_OMIT_JSON
#cmakedefine SQLITE_USE_URI

#cmakedefine SQLITE_RECOMMENDED_OPTIONS
#if defined(SQLITE_RECOMMENDED_OPTIONS)
#   define SQLITE_DQS 0
#   define SQLITE_DEFAULT_MEMSTATUS 0
#   define SQLITE_DEFAULT_WAL_SYNCHRONOUS 1
#   define SQLITE_LIKE_DOESNT_MATCH_BLOBS
#   define SQLITE_MAX_EXPR_DEPTH 1
#   define SQLITE_OMIT_DEPRECATED
#   define SQLITE_OMIT_PROGRESS_CALLBACK
#   define SQLITE_OMIT_SHARED_CACHE
#   define SQLITE_USE_ALLOCA
#endif /* SQLITE_RECOMMENDED_OPTIONS */


#endif /* SQLITE3_CONFIG_H */
