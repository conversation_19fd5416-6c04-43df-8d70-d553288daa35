#ifndef GET_TIMESTAMP_H
#define GET_TIMESTAMP_H

#include <chrono>
#include <string>
#include <sstream>
#include <iomanip>
#include <deque>

namespace Get_time {

    /**
     * @brief 获取当前时间，返回格式为YYYY-MM-DD HH:MM:SS.mmm
     *
     * @return std::string 当前时间字符串，格式为YYYY-MM-DD HH:MM:SS.mmm
     */
    inline std::string getTimeStamp() {
        // 获取当前时间
        auto now = std::chrono::system_clock::now();
        auto now_in_seconds = std::chrono::time_point_cast<std::chrono::seconds>(now);
        auto fractional_seconds = std::chrono::duration_cast<std::chrono::milliseconds>(now - now_in_seconds);

        // 将时间转换为本地时间
        std::time_t t = std::chrono::system_clock::to_time_t(now_in_seconds);
        std::tm tm = *std::localtime(&t);

        std::ostringstream oss;
        // 使用字符串流格式化时间YYYY-MM-DD HH:MM:SS.mmm
        oss << std::put_time(&tm, "%Y-%m-%d %H:%M:%S") << "." << std::setw(3) << std::setfill('0') << fractional_seconds.count();

        return oss.str();
    }

    inline double getUnixTimestamp() {
        // 获取当前时间（系统时钟）
        auto now = std::chrono::system_clock::now();

        // 转换为从1970年1月1日开始的毫秒数
        auto timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count() / 1000.0;

        return timestamp;
    }

    inline long long get_timestamp_us() {
        return std::chrono::duration_cast<std::chrono::microseconds>(
            std::chrono::system_clock::now().time_since_epoch()
        ).count();
    }
} // namespace Utils


// ================ FPS 相关类 ================
class LoopFPS {
public:
    LoopFPS(size_t windowSize = 100) : windowSize_(windowSize) {}

    // 记录帧时间
    void update() {
        auto now = std::chrono::system_clock::now();
        timePoints_.push_back(now);

        // 确保队列大小
        if (timePoints_.size() > windowSize_) {
            timePoints_.pop_front();
        }
    }

    // 计算当前 FPS
    double getFPS() const {
        if (timePoints_.size() < 2) {
            return 0.0; // 需要至少两个时间点才能计算 FPS
        }

        // 计算总时间（毫秒）
        auto totalDuration = std::chrono::duration_cast<std::chrono::milliseconds>(
            timePoints_.back() - timePoints_.front()).count();

        // 计算平均帧时间（毫秒）
        double averageFrameTime = static_cast<double>(totalDuration) / (timePoints_.size() - 1);

        // 计算 FPS
        return 1000.0 / averageFrameTime;
    }

    // 检查是否超过时间间隔
    bool checkInterval(std::chrono::milliseconds interval) {
        auto now = std::chrono::system_clock::now();

        // 如果是第一次调用，总是返回true，时间超过interval返回true
        if (lastTriggerTime_ == std::chrono::system_clock::time_point{} ||
            now - lastTriggerTime_ >= interval) {
            lastTriggerTime_ = now; // 记录第一次调用的时间
            return true;
        }

        return false;
    }

private:
    std::deque<std::chrono::system_clock::time_point> timePoints_; // 存储时间点
    size_t windowSize_; // 队列大小
    std::chrono::system_clock::time_point lastTriggerTime_; // 第一次调用返回true的时间点
};


#endif // GET_TIMESTAMP_H

