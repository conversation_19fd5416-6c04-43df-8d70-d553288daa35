#include "DataLoggingService.hpp"
#include "../third_party/sqlite/sqlite3.h"
#include "../Utils/utf8_utils.hpp"
#include <vector>
#include <chrono>
#include <thread>
#include <filesystem>

namespace Services {

DataLoggingService::DataLoggingService(const std::string& db_path) : m_dbPath(db_path) {}

DataLoggingService::~DataLoggingService() {
    if (m_is_running) {
        stop();
    }
}

void DataLoggingService::start() {
    if (m_is_running) return;
    m_is_running = true;
    m_writerThread = std::thread(&DataLoggingService::writerLoop, this);
    UTF8Utils::println("✅ 数据记录服务已启动。");
}

void DataLoggingService::stop() {
    if (!m_is_running) return;
    m_is_running = false;
    // 我们通过停止标志来停止线程
    if (m_writerThread.joinable()) {
        m_writerThread.join();
    }
    UTF8Utils::println("🛑 数据记录服务已停止。");
}

void DataLoggingService::log(DataPoint data_point) {
    if (m_is_running) {
        m_queue.push(std::move(data_point));
    }
}

bool DataLoggingService::initializeDatabase() {
    try {
        std::filesystem::path path(m_dbPath);
        if (path.has_parent_path()) {
            std::filesystem::create_directories(path.parent_path());
        }
    } catch (const std::filesystem::filesystem_error& e) {
        UTF8Utils::println("❌ 创建数据库目录失败: " + std::string(e.what()));
        return false;
    }

    if (sqlite3_open(m_dbPath.c_str(), &m_db) != SQLITE_OK) {
        UTF8Utils::println("❌ 无法打开数据库: " + std::string(sqlite3_errmsg(m_db)));
        return false;
    }

    const char* sql_create_table = R"(
        CREATE TABLE IF NOT EXISTS trajectory (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            timestamp_ms BIGINT NOT NULL,
            camera_id INTEGER NOT NULL,
            pos_x REAL NOT NULL,
            pos_y REAL NOT NULL,
            pos_z REAL NOT NULL,
            speed REAL NOT NULL
        );
    )";

    char* errmsg = nullptr;
    if (sqlite3_exec(m_db, sql_create_table, 0, 0, &errmsg) != SQLITE_OK) {
        UTF8Utils::println("❌ 创建数据表失败: " + std::string(errmsg));
        sqlite3_free(errmsg);
        sqlite3_close(m_db);
        m_db = nullptr;
        return false;
    }

    // Enable WAL mode for better write concurrency
    sqlite3_exec(m_db, "PRAGMA journal_mode=WAL;", 0, 0, 0);

    return true;
}

void DataLoggingService::writerLoop() {
    if (!initializeDatabase()) {
        m_is_running = false;
        return;
    }

    const int batch_size = 50; // 每50条数据写入一次（优化：减少批量大小）
    const auto timeout = std::chrono::milliseconds(200); // 或每隔200ms写入一次（优化：减少延迟）
    std::vector<DataPoint> batch;
    batch.reserve(batch_size);

    const char* sql_insert = "INSERT INTO trajectory (timestamp_ms, camera_id, pos_x, pos_y, pos_z, speed) VALUES (?, ?, ?, ?, ?, ?);";
    sqlite3_stmt* stmt = nullptr;

    if (sqlite3_prepare_v2(m_db, sql_insert, -1, &stmt, nullptr) != SQLITE_OK) {
        UTF8Utils::println("❌ 准备SQL语句失败: " + std::string(sqlite3_errmsg(m_db)));
        m_is_running = false;
        sqlite3_close(m_db);
        m_db = nullptr;
        return;
    }
    
    auto last_write_time = std::chrono::steady_clock::now();

    while (m_is_running) {
        DataPoint dp;
        if (m_queue.try_pop(dp)) {
            batch.push_back(std::move(dp));
        } else {
            // 如果队列为空，稍等一下
            std::this_thread::sleep_for(std::chrono::milliseconds(5)); // 减少等待时间
        }

        auto now = std::chrono::steady_clock::now();
        bool timeout_reached = (now - last_write_time) >= timeout;

        if (batch.size() >= batch_size || (!batch.empty() && timeout_reached) || (!batch.empty() && !m_is_running)) {
            
            sqlite3_exec(m_db, "BEGIN TRANSACTION;", 0, 0, 0);

            for (const auto& point : batch) {
                sqlite3_bind_int64(stmt, 1, point.timestamp_ms);
                sqlite3_bind_int(stmt, 2, point.camera_id);
                sqlite3_bind_double(stmt, 3, point.position.x);
                sqlite3_bind_double(stmt, 4, point.position.y);
                sqlite3_bind_double(stmt, 5, point.position.z);
                sqlite3_bind_double(stmt, 6, point.speed);

                if (sqlite3_step(stmt) != SQLITE_DONE) {
                     UTF8Utils::println("❌ 数据库插入失败: " + std::string(sqlite3_errmsg(m_db)));
                }
                sqlite3_reset(stmt);
            }

            sqlite3_exec(m_db, "COMMIT;", 0, 0, 0);
            batch.clear();
            last_write_time = std::chrono::steady_clock::now(); // 重置写入时间
        }
    }

    // Finalize statement and close DB
    sqlite3_finalize(stmt);
    sqlite3_close(m_db);
    m_db = nullptr;
}

} // namespace Services 