<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Camera_Editor - 双目识别系统</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="redesign.css">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Chart.js 库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.1/dist/chart.umd.js"></script>
</head>
<body>
    <div class="app-container">
        <!-- 顶部导航栏 -->
        <header class="top-navbar">
            <div class="navbar-content">
                <div class="logo-section">
                    <i class="fas fa-video"></i>
                    <h1>Camera_Editor</h1>
                    <span class="version">v2.0</span>
                </div>

                <!-- 系统状态指示器 -->
                <div class="system-status-bar">
                    <div class="status-item">
                        <div class="status-dot" id="statusDot"></div>
                        <span id="connection-status">连接中...</span>
                    </div>
                    <div class="status-item">
                        <i class="fas fa-tachometer-alt"></i>
                        <span id="fps-display">0 FPS</span>
                    </div>
                    <div class="status-item">
                        <i class="fas fa-clock"></i>
                        <span id="lastUpdate">--</span>
                    </div>
                </div>

                <!-- 快速操作按钮 -->
                <div class="quick-actions">
                    <button class="btn btn-primary" id="startBtn" title="启动系统">
                        <i class="fas fa-play"></i>
                    </button>
                    <button class="btn btn-danger" id="stopBtn" title="停止系统">
                        <i class="fas fa-stop"></i>
                    </button>
                    <button class="btn btn-warning" id="refreshBtn" title="刷新连接">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                    <button class="btn btn-info" id="fullscreen-button" title="全屏模式">
                        <i class="fas fa-expand"></i>
                    </button>
                </div>
            </div>
        </header>

        <!-- 主标签页导航 -->
        <nav class="tab-navigation">
            <div class="tab-container">
                <button class="tab-button active" data-tab="dashboard">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>主控制台</span>
                </button>
                <button class="tab-button" data-tab="recording">
                    <i class="fas fa-video"></i>
                    <span>录制中心</span>
                </button>
                <button class="tab-button" data-tab="analytics">
                    <i class="fas fa-chart-line"></i>
                    <span>数据分析</span>
                </button>
                <button class="tab-button" data-tab="settings">
                    <i class="fas fa-cog"></i>
                    <span>系统设置</span>
                </button>
                <button class="tab-button" data-tab="database">
                    <i class="fas fa-database"></i>
                    <span>数据库</span>
                </button>
            </div>
        </nav>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 主控制台标签页 -->
            <div class="tab-content active" id="dashboard-tab">
                <!-- 关键指标仪表板 -->
                <section class="dashboard-metrics">
                    <h2><i class="fas fa-chart-line"></i> 实时监控仪表板</h2>
                    <div class="metrics-grid">
                        <div class="metric-card" id="metric-ball-speed" title="基于SG滤波器计算的瞬时速度">
                            <div class="metric-icon">
                                <i class="fas fa-tachometer-alt"></i>
                            </div>
                            <div class="metric-content">
                                <div class="metric-value" id="ballSpeed">0.00 <span class="unit">m/s</span></div>
                                <div class="metric-label">实时球速</div>
                            </div>
                        </div>
                        <div class="metric-card" id="metric-ball-count">
                            <div class="metric-icon">
                                <i class="fas fa-table-tennis-paddle-ball"></i>
                            </div>
                            <div class="metric-content">
                                <div class="metric-value" id="ballCount">0</div>
                                <div class="metric-label">检测目标</div>
                            </div>
                        </div>
                        <div class="metric-card" id="metric-fps">
                            <div class="metric-icon">
                                <i class="fas fa-download"></i>
                            </div>
                            <div class="metric-content">
                                <div class="metric-value" id="dashboard-fps-display">0 FPS</div>
                                <div class="metric-label">接收帧率</div>
                            </div>
                        </div>
                        <div class="metric-card" id="metric-last-update">
                            <div class="metric-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="metric-content">
                                <div class="metric-value" id="coordLastUpdate">--</div>
                                <div class="metric-label">最后更新</div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 双摄像头画面区域 -->
                <section class="camera-display">
                    <div class="camera-grid">
                        <div class="camera-item" id="camera-item-1">
                            <div class="camera-header">
                                <h3><i class="fas fa-camera"></i> 左摄像头 (ID: 1)</h3>
                                <div class="camera-status status-disconnected" id="leftCameraStatus">
                                    <span class="status-text">等待连接</span>
                                </div>
                            </div>
                            <div class="camera-view">
                                <canvas id="leftCamera" class="video-canvas"></canvas>
                                <canvas id="leftTrajectoryCanvas" class="trajectory-canvas"></canvas>
                                <div class="no-signal" id="leftNoSignal"><i class="fas fa-power-off"></i><p>无信号</p></div>
                            </div>
                            <div class="camera-footer">
                                <div class="camera-stats">
                                    <div class="stat-item" title="后端图像处理和AI推理的实际帧率">
                                        <i class="fas fa-microchip"></i>
                                        <span class="stat-value" id="fps-cam1">-- FPS</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="camera-item" id="camera-item-2">
                            <div class="camera-header">
                                <h3><i class="fas fa-camera"></i> 右摄像头 (ID: 2)</h3>
                                <div class="camera-status status-disconnected" id="rightCameraStatus">
                                    <span class="status-text">等待连接</span>
                                </div>
                            </div>
                            <div class="camera-view">
                                <canvas id="rightCamera" class="video-canvas"></canvas>
                                <canvas id="rightTrajectoryCanvas" class="trajectory-canvas"></canvas>
                                <div class="no-signal" id="rightNoSignal"><i class="fas fa-power-off"></i><p>无信号</p></div>
                            </div>
                            <div class="camera-footer">
                                <div class="camera-stats">
                                    <div class="stat-item" title="后端图像处理和AI推理的实际帧率">
                                        <i class="fas fa-microchip"></i>
                                        <span class="stat-value" id="fps-cam2">-- FPS</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 系统状态面板 -->
                <section class="system-status-panel">
                    <h2><i class="fas fa-heartbeat"></i> 系统状态</h2>
                    <div class="status-grid">
                        <div class="status-card" id="camera-status-card">
                            <div class="status-icon"><i class="fas fa-camera"></i></div>
                            <div class="status-info">
                                <div class="status-title">相机系统</div>
                                <div class="status-value" id="cameraSystemStatus">未启动</div>
                            </div>
                        </div>
                        <div class="status-card" id="stream-status-card">
                            <div class="status-icon"><i class="fas fa-satellite-dish"></i></div>
                            <div class="status-info">
                                <div class="status-title">视频流</div>
                                <div class="status-value" id="streamStatus">已停止</div>
                            </div>
                        </div>
                        <div class="status-card" id="recording-status-card">
                            <div class="status-icon"><i class="fas fa-record-vinyl"></i></div>
                            <div class="status-info">
                                <div class="status-title">全局录制</div>
                                <div class="status-value" id="recordingStatus">未录制</div>
                            </div>
                        </div>
                        <div class="status-card" id="calibration-status-card">
                            <div class="status-icon"><i class="fas fa-crosshairs"></i></div>
                            <div class="status-info">
                                <div class="status-title">相机标定</div>
                                <div class="status-value" id="calibrationStatus">空闲</div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>

            <!-- 录制中心标签页 -->
            <div class="tab-content" id="recording-tab">
                <div class="recording-layout">
                    <!-- 录制模式选择 -->
                    <section class="recording-modes">
                        <h2><i class="fas fa-video"></i> 录制模式</h2>
                        <div class="mode-grid">
                            <div class="mode-card" id="global-recording-card">
                                <div class="mode-header">
                                    <i class="fas fa-globe"></i>
                                    <h3>全局录制</h3>
                                </div>
                                <div class="mode-content">
                                    <p>同时录制双摄像头画面</p>
                                    <button class="btn btn-record-all" id="recordAllBtn">
                                        <i class="fas fa-video"></i> 开始全局录制
                                    </button>
                                </div>
                                <div class="mode-status">
                                    <div class="status-dot" id="globalRecordingStatus"></div>
                                    <span id="globalRecordingStatusText">未录制</span>
                                </div>
                            </div>

                            <div class="mode-card" id="individual-recording-card">
                                <div class="mode-header">
                                    <i class="fas fa-camera"></i>
                                    <h3>单摄像头录制</h3>
                                </div>
                                <div class="mode-content">
                                    <p>独立控制每个摄像头录制</p>
                                    <div class="individual-controls">
                                        <div class="camera-control">
                                            <span>左摄像头</span>
                                            <button class="btn btn-record" id="startRec1">
                                                <i class="fas fa-circle"></i> 录制
                                            </button>
                                            <button class="btn btn-stop-rec" id="stopRec1" style="display:none;">
                                                <i class="fas fa-square"></i> 停止
                                            </button>
                                            <div class="rec-status" id="recStatus1">
                                                <div class="rec-dot"></div>
                                                <span>未录制</span>
                                            </div>
                                        </div>
                                        <div class="camera-control">
                                            <span>右摄像头</span>
                                            <button class="btn btn-record" id="startRec2">
                                                <i class="fas fa-circle"></i> 录制
                                            </button>
                                            <button class="btn btn-stop-rec" id="stopRec2" style="display:none;">
                                                <i class="fas fa-square"></i> 停止
                                            </button>
                                            <div class="rec-status" id="recStatus2">
                                                <div class="rec-dot"></div>
                                                <span>未录制</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mode-card" id="highlight-recording-card">
                                <div class="mode-header">
                                    <i class="fas fa-star"></i>
                                    <h3>精彩片段录制</h3>
                                </div>
                                <div class="mode-content">
                                    <p>智能检测高速球并自动剪辑</p>
                                    <div class="highlight-controls">
                                        <button class="btn btn-highlight-start" id="highlightStartBtn">
                                            <i class="fas fa-record-vinyl"></i> 开始精彩录制
                                        </button>
                                        <button class="btn btn-highlight-stop" id="highlightStopBtn" style="display: none;">
                                            <i class="fas fa-cut"></i> 停止并生成精彩片段
                                        </button>
                                    </div>
                                    <div class="highlight-settings">
                                        <label for="speedThreshold">球速阈值 (m/s)</label>
                                        <div class="slider-container">
                                            <input type="range" id="speedThreshold" min="5" max="30" value="15" step="0.5">
                                            <span id="speedThresholdValue">15.0</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="mode-status">
                                    <div class="highlight-status">
                                        <div class="status-item">
                                            <span class="label">状态:</span>
                                            <span class="value" id="highlightStatus">未开始</span>
                                        </div>
                                        <div class="status-item">
                                            <span class="label">时长:</span>
                                            <span class="value" id="highlightDuration">0s</span>
                                        </div>
                                        <div class="status-item">
                                            <span class="label">精彩时刻:</span>
                                            <span class="value" id="highlightMoments">0</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mode-card" id="detection-recording-card">
                                <div class="mode-header">
                                    <i class="fas fa-square-check"></i>
                                    <h3>检测框录制</h3>
                                </div>
                                <div class="mode-content">
                                    <p>录制带YOLO检测框的视频</p>
                                    <div class="detection-controls">
                                        <div class="control-group">
                                            <label class="switch">
                                                <input type="checkbox" id="detectionRecordingToggle">
                                                <span class="slider round"></span>
                                            </label>
                                            <span class="control-label">启用检测框录制</span>
                                        </div>
                                        <div class="control-group">
                                            <label for="recordingModeSelect">录制模式:</label>
                                            <select id="recordingModeSelect" class="mode-select">
                                                <option value="RAW_MODE">原始视频</option>
                                                <option value="DETECTION_MODE">带检测框</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="mode-status">
                                    <div class="status-dot" id="detectionRecordingStatus"></div>
                                    <span id="detectionRecordingStatusText">未启用</span>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- 录制文件管理 -->
                    <section class="recording-files">
                        <h2><i class="fas fa-folder"></i> 录制文件管理</h2>
                        <div class="file-browser">
                            <div class="file-list" id="recordingFileList">
                                <div class="no-files">
                                    <i class="fas fa-folder-open"></i>
                                    <p>暂无录制文件</p>
                                </div>
                            </div>
                        </div>
                    </section>
                </div>
            </div>

            <!-- 数据分析标签页 -->
            <div class="tab-content" id="analytics-tab">
                <!-- 数据可视化仪表板 -->
                <section class="data-visualization-dashboard">
                    <div class="panel-header">
                        <h2><i class="fas fa-chart-bar"></i> 数据可视化分析</h2>
                        <div class="visualization-controls">
                            <button class="btn btn-small" id="refreshChartsBtn"><i class="fas fa-sync-alt"></i> 刷新图表</button>
                            <button class="btn btn-small" id="exportDataBtn"><i class="fas fa-download"></i> 导出数据</button>
                            <button class="btn btn-small" id="filterToggleBtn"><i class="fas fa-filter"></i> 筛选器</button>
                        </div>
                    </div>
                    <div class="visualization-grid">
                        <!-- 交互式速度分析 - 主要重点组件 -->
                        <div class="chart-container featured-chart">
                            <div class="chart-header">
                                <h3><i class="fas fa-expand-arrows-alt"></i> 交互式速度分析 <span class="featured-badge">重点分析</span></h3>
                                <div class="chart-controls">
                                    <div class="interactive-chart-controls">
                                        <button class="btn btn-small" id="zoomInBtn" title="放大">
                                            <i class="fas fa-search-plus"></i>
                                        </button>
                                        <button class="btn btn-small" id="zoomOutBtn" title="缩小">
                                            <i class="fas fa-search-minus"></i>
                                        </button>
                                        <button class="btn btn-small" id="resetViewBtn" title="重置视图">
                                            <i class="fas fa-home"></i>
                                        </button>
                                        <button class="btn btn-small" id="refreshInteractiveBtn" title="手动刷新数据">
                                            <i class="fas fa-sync-alt"></i>
                                        </button>
                                        <div class="camera-toggles">
                                            <label class="camera-toggle">
                                                <input type="checkbox" id="leftCameraToggle" checked>
                                                <span class="toggle-label left-camera">左摄像头</span>
                                            </label>
                                            <label class="camera-toggle" style="display: none;">
                                                <input type="checkbox" id="rightCameraToggle">
                                                <span class="toggle-label right-camera">右摄像头</span>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="chart-status" id="interactiveChartStatus">准备中...</div>
                                </div>
                            </div>
                            <div class="chart-wrapper interactive-chart-wrapper featured-wrapper">
                                <canvas id="speedTimeInteractiveChart"></canvas>
                                <div class="chart-instructions">
                                    <div class="instruction-item">
                                        <i class="fas fa-mouse"></i>
                                        <span>滚轮缩放 | 拖拽平移</span>
                                    </div>
                                    <div class="instruction-item">
                                        <i class="fas fa-keyboard"></i>
                                        <span>方向键平移 | +/- 缩放 | 0 重置</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 运动轨迹热力图 -->
                        <div class="chart-container">
                            <div class="chart-header">
                                <h3><i class="fas fa-fire"></i> 运动热力图</h3>
                                <div class="chart-status" id="heatmapStatus">准备中...</div>
                            </div>
                            <div class="chart-wrapper">
                                <canvas id="trajectoryHeatmapChart"></canvas>
                            </div>
                        </div>

                        <!-- 运动频率分析 -->
                        <div class="chart-container">
                            <div class="chart-header">
                                <h3><i class="fas fa-wave-square"></i> 运动频率分析</h3>
                                <div class="chart-status" id="frequencyStatus">分析中...</div>
                            </div>
                            <div class="chart-wrapper">
                                <canvas id="movementFrequencyChart"></canvas>
                            </div>
                        </div>

                        <!-- 统计摘要 -->
                        <div class="chart-container">
                            <div class="chart-header">
                                <h3><i class="fas fa-calculator"></i> 统计摘要</h3>
                                <div class="chart-status" id="statsStatus">计算中...</div>
                            </div>
                            <div class="stats-wrapper">
                                <div class="stat-item">
                                    <div class="stat-label">平均速度</div>
                                    <div class="stat-value" id="avgSpeed">-- m/s</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-label">最大速度</div>
                                    <div class="stat-value" id="maxSpeed">-- m/s</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-label">数据点数</div>
                                    <div class="stat-value" id="dataPointCount">--</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-label">活跃时长</div>
                                    <div class="stat-value" id="activeDuration">-- 分钟</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 三维轨迹可视化 -->
                <section class="trajectory-viewer">
                    <div class="panel-header">
                        <h2><i class="fas fa-cube"></i> 三维轨迹可视化</h2>
                        <div id="trajectory-status" class="status-message">等待轨迹数据...</div>
                    </div>
                    <div id="trajectory-visualizer-container" class="trajectory-canvas-container">
                        <!-- 3D Canvas will be inserted here by Three.js -->
                    </div>
                </section>

                <!-- 坐标数据表格 -->
                <section class="coordinates-display">
                    <div class="panel-header">
                        <h2><i class="fas fa-table"></i> 坐标数据</h2>
                        <div class="coordinate-status" id="coordStatus">
                            <div class="status-dot status-disconnected" id="coordStatusDot"></div>
                            <span id="coordStatusText">等待数据</span>
                        </div>
                    </div>
                    <div class="coordinates-content">
                        <div class="coordinates-table-container">
                            <table class="coordinates-table">
                                <thead>
                                    <tr>
                                        <th><i class="fas fa-hashtag"></i> ID</th>
                                        <th><i class="fas fa-arrows-alt-h"></i> X (m)</th>
                                        <th><i class="fas fa-arrows-alt-v"></i> Y (m)</th>
                                        <th><i class="fas fa-arrow-up"></i> Z (m)</th>
                                        <th><i class="fas fa-percentage"></i> 置信度</th>
                                        <th><i class="fas fa-clock"></i> 时间</th>
                                    </tr>
                                </thead>
                                <tbody id="coordinatesTableBody">
                                    <!-- Coordinate data will be populated by JS -->
                                    <tr class="no-data-row">
                                        <td colspan="6">
                                            <i class="fas fa-info-circle"></i>
                                            暂无三维坐标数据
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </section>
            </div>

            <!-- 系统设置标签页 -->
            <div class="tab-content" id="settings-tab">
                <div class="settings-layout">
                    <!-- 相机标定 -->
                    <section class="calibration-panel">
                        <div class="panel-header">
                            <h2><i class="fas fa-crosshairs"></i> 相机标定控制</h2>
                            <div class="calibration-status-indicator">
                                <div class="status-dot" id="calibrationStatusDot"></div>
                                <span id="calibrationStatusText">空闲</span>
                            </div>
                        </div>
                        <div class="calibration-content">
                            <div class="calibration-instructions">
                                <h3><i class="fas fa-info-circle"></i> 标定说明</h3>
                                <ol>
                                    <li>确保双目摄像头已正常启动并显示画面</li>
                                    <li>将8×11棋盘格标定板放置在摄像头视野内</li>
                                    <li>确保标定板完整、清晰、无遮挡</li>
                                    <li>点击"开始标定"按钮，系统将自动检测并标定</li>
                                </ol>
                            </div>
                            <div class="calibration-controls">
                                <button class="btn btn-calibration-start" id="startCalibrationBtn">
                                    <i class="fas fa-play"></i> 开始标定
                                </button>
                                <button class="btn btn-calibration-stop" id="stopCalibrationBtn" style="display: none;">
                                    <i class="fas fa-stop"></i> 停止标定
                                </button>
                                <button class="btn btn-calibration" id="calibrationBtn">
                                    <i class="fas fa-crosshairs"></i> 快速标定
                                </button>
                            </div>
                            <div class="calibration-progress" id="calibrationProgress" style="display: none;">
                                <div class="progress-bar">
                                    <div class="progress-fill" id="calibrationProgressFill"></div>
                                </div>
                                <div class="progress-text" id="calibrationProgressText">检测标定板中...</div>
                            </div>
                            <div class="calibration-results" id="calibrationResults" style="display: none;">
                                <h3><i class="fas fa-check-circle"></i> 标定结果</h3>
                                <div class="result-item">
                                    <span class="result-label">状态:</span>
                                    <span class="result-value" id="calibrationResultStatus">--</span>
                                </div>
                                <div class="result-item">
                                    <span class="result-label">平均误差:</span>
                                    <span class="result-value" id="calibrationResultError">-- mm</span>
                                </div>
                                <div class="result-item">
                                    <span class="result-label">检测角点:</span>
                                    <span class="result-value" id="calibrationResultCorners">--</span>
                                </div>
                                <div class="result-item">
                                    <span class="result-label">完成时间:</span>
                                    <span class="result-value" id="calibrationResultTime">--</span>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- AI参数设置 -->
                    <section class="ai-settings">
                        <h2><i class="fas fa-brain"></i> AI检测参数</h2>
                        <div class="settings-grid">
                            <div class="setting-group">
                                <label for="confidenceThreshold"><i class="fas fa-bullseye"></i> AI检测阈值</label>
                                <div class="slider-container">
                                    <input type="range" id="confidenceThreshold" name="confidenceThreshold" min="0.1" max="0.9" value="0.4" step="0.05">
                                    <span id="thresholdValue">0.40</span>
                                </div>
                                <p class="setting-description">调整AI检测的置信度阈值，值越高检测越严格</p>
                            </div>
                        </div>
                    </section>

                    <!-- 系统配置 -->
                    <section class="system-config">
                        <h2><i class="fas fa-cogs"></i> 系统配置</h2>
                        <div class="config-grid">
                            <div class="config-group">
                                <h3>摄像头设置</h3>
                                <div class="config-item">
                                    <label>目标帧率</label>
                                    <select class="config-select">
                                        <option value="210">210 FPS</option>
                                        <option value="160">160 FPS</option>
                                        <option value="120">120 FPS</option>
                                        <option value="60">60 FPS</option>
                                    </select>
                                </div>
                                <div class="config-item">
                                    <label>分辨率</label>
                                    <select class="config-select">
                                        <option value="1920x1080">1920x1080</option>
                                        <option value="1280x720">1280x720</option>
                                        <option value="640x480">640x480</option>
                                    </select>
                                </div>
                            </div>

                            <div class="config-group">
                                <h3>数据存储</h3>
                                <div class="config-item">
                                    <label>数据保存路径</label>
                                    <input type="text" class="config-input" value="./Data/" readonly>
                                </div>
                                <div class="config-item">
                                    <label>自动清理旧数据</label>
                                    <label class="switch">
                                        <input type="checkbox" checked>
                                        <span class="slider round"></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- 连接管理 -->
                    <section class="connection-management">
                        <h2><i class="fas fa-wifi"></i> 连接管理</h2>
                        <div class="connection-controls">
                            <button class="btn" id="reconnect-button">
                                <i class="fas fa-wifi"></i> 重新连接WebSocket
                            </button>
                            <button class="btn btn-warning" id="resetSystemBtn">
                                <i class="fas fa-undo"></i> 重置系统
                            </button>
                        </div>
                        <div class="connection-info">
                            <div class="info-item">
                                <span class="info-label">WebSocket状态:</span>
                                <span class="info-value" id="wsConnectionStatus">未连接</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">服务器地址:</span>
                                <span class="info-value" id="serverAddress">--</span>
                            </div>
                        </div>
                    </section>
                </div>
            </div>

            <!-- 数据库标签页 -->
            <div class="tab-content" id="database-tab">
                <!-- 数据库浏览器 -->
                <section class="db-explorer">
                    <div class="panel-header">
                        <h2><i class="fas fa-database"></i> 数据库查询</h2>
                        <div id="db-status-message" class="status-message"></div>
                    </div>
                    <div class="db-explorer-panel">
                        <div class="query-section">
                            <div class="query-header">
                                <h3>SQL查询编辑器</h3>
                                <div class="query-actions">
                                    <button class="btn btn-small" id="clearQueryBtn"><i class="fas fa-eraser"></i> 清空</button>
                                    <button class="btn btn-small" id="saveQueryBtn"><i class="fas fa-save"></i> 保存</button>
                                    <button class="btn btn-small" id="loadQueryBtn"><i class="fas fa-folder-open"></i> 加载</button>
                                </div>
                            </div>
                            <textarea id="sql-query-input" class="sql-input" rows="5" placeholder="输入 SQL 查询语句...">SELECT id, timestamp_ms, pos_x, pos_y, pos_z, speed FROM trajectory ORDER BY id DESC LIMIT 10;</textarea>
                            <div class="query-controls">
                                <button id="execute-sql-btn" class="btn btn-primary"><i class="fas fa-play"></i> 执行查询</button>
                                <button id="export-results-btn" class="btn btn-secondary"><i class="fas fa-download"></i> 导出结果</button>
                            </div>
                        </div>
                        <div id="db-results-container" class="results-container">
                            <div class="no-results">
                                <i class="fas fa-search"></i>
                                <p>执行查询以查看结果</p>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 数据表浏览 -->
                <section class="table-browser">
                    <h2><i class="fas fa-table"></i> 数据表浏览</h2>
                    <div class="table-list">
                        <div class="table-item" data-table="trajectory">
                            <div class="table-icon"><i class="fas fa-route"></i></div>
                            <div class="table-info">
                                <h3>trajectory</h3>
                                <p>球的运动轨迹数据</p>
                            </div>
                            <button class="btn btn-small browse-table-btn" data-table="trajectory">
                                <i class="fas fa-eye"></i> 浏览
                            </button>
                        </div>
                        <div class="table-item" data-table="highlights">
                            <div class="table-icon"><i class="fas fa-star"></i></div>
                            <div class="table-info">
                                <h3>highlights</h3>
                                <p>精彩片段记录</p>
                            </div>
                            <button class="btn btn-small browse-table-btn" data-table="highlights">
                                <i class="fas fa-eye"></i> 浏览
                            </button>
                        </div>
                        <div class="table-item" data-table="recordings">
                            <div class="table-icon"><i class="fas fa-video"></i></div>
                            <div class="table-info">
                                <h3>recordings</h3>
                                <p>录制文件记录</p>
                            </div>
                            <button class="btn btn-small browse-table-btn" data-table="recordings">
                                <i class="fas fa-eye"></i> 浏览
                            </button>
                        </div>
                    </div>
                </section>

                <!-- 数据统计 -->
                <section class="data-statistics">
                    <h2><i class="fas fa-chart-pie"></i> 数据统计</h2>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon"><i class="fas fa-database"></i></div>
                            <div class="stat-content">
                                <div class="stat-value" id="totalRecords">--</div>
                                <div class="stat-label">总记录数</div>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon"><i class="fas fa-calendar"></i></div>
                            <div class="stat-content">
                                <div class="stat-value" id="dataDateRange">--</div>
                                <div class="stat-label">数据时间范围</div>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon"><i class="fas fa-hdd"></i></div>
                            <div class="stat-content">
                                <div class="stat-value" id="databaseSize">--</div>
                                <div class="stat-label">数据库大小</div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>

            <!-- 数据筛选器面板 (共享) -->
            <section class="data-filter-panel" id="dataFilterPanel" style="display: none;">
                <div class="panel-header">
                    <h2><i class="fas fa-filter"></i> 数据筛选器</h2>
                    <button class="btn btn-small" id="applyFiltersBtn"><i class="fas fa-check"></i> 应用筛选</button>
                </div>
                <div class="filter-grid">
                    <!-- 时间范围筛选 -->
                    <div class="filter-group">
                        <label class="filter-label">时间范围</label>
                        <div class="filter-controls">
                            <input type="datetime-local" id="startTimeFilter" class="filter-input">
                            <span class="filter-separator">至</span>
                            <input type="datetime-local" id="endTimeFilter" class="filter-input">
                        </div>
                    </div>

                    <!-- 速度范围筛选 -->
                    <div class="filter-group">
                        <label class="filter-label">速度范围 (m/s)</label>
                        <div class="filter-controls">
                            <input type="number" id="minSpeedFilter" class="filter-input" placeholder="最小速度" min="0" step="0.1">
                            <span class="filter-separator">至</span>
                            <input type="number" id="maxSpeedFilter" class="filter-input" placeholder="最大速度" min="0" step="0.1">
                        </div>
                    </div>

                    <!-- 摄像头筛选 -->
                    <div class="filter-group">
                        <label class="filter-label">摄像头</label>
                        <div class="filter-controls">
                            <select id="cameraFilter" class="filter-select">
                                <option value="">全部摄像头</option>
                                <option value="1">摄像头 1</option>
                                <option value="2">摄像头 2</option>
                            </select>
                        </div>
                    </div>

                    <!-- 位置范围筛选 -->
                    <div class="filter-group">
                        <label class="filter-label">X坐标范围 (m)</label>
                        <div class="filter-controls">
                            <input type="number" id="minXFilter" class="filter-input" placeholder="最小X" step="0.01">
                            <span class="filter-separator">至</span>
                            <input type="number" id="maxXFilter" class="filter-input" placeholder="最大X" step="0.01">
                        </div>
                    </div>

                    <div class="filter-group">
                        <label class="filter-label">Y坐标范围 (m)</label>
                        <div class="filter-controls">
                            <input type="number" id="minYFilter" class="filter-input" placeholder="最小Y" step="0.01">
                            <span class="filter-separator">至</span>
                            <input type="number" id="maxYFilter" class="filter-input" placeholder="最大Y" step="0.01">
                        </div>
                    </div>

                    <div class="filter-group">
                        <label class="filter-label">Z坐标范围 (m)</label>
                        <div class="filter-controls">
                            <input type="number" id="minZFilter" class="filter-input" placeholder="最小Z" step="0.01">
                            <span class="filter-separator">至</span>
                            <input type="number" id="maxZFilter" class="filter-input" placeholder="最大Z" step="0.01">
                        </div>
                    </div>

                    <!-- 重置按钮 -->
                    <div class="filter-group">
                        <button class="btn btn-secondary" id="resetFiltersBtn"><i class="fas fa-undo"></i> 重置筛选</button>
                    </div>
                </div>
            </section>
        </main>

        <!-- 底部状态栏 -->
        <footer class="bottom-status-bar">
            <div class="status-bar-content">
                <div class="status-info">
                    <span class="status-item">
                        <i class="fas fa-copyright"></i>
                        Camera_Editor v2.0 - 乒乓球自动裁判系统
                    </span>
                    <span class="status-item">
                        <i class="fas fa-server"></i>
                        服务器: <span id="serverStatus">运行中</span>
                    </span>
                </div>
                <div class="performance-info">
                    <span class="status-item">
                        <i class="fas fa-memory"></i>
                        内存: <span id="memoryUsage">--</span>
                    </span>
                    <span class="status-item">
                        <i class="fas fa-microchip"></i>
                        CPU: <span id="cpuUsage">--</span>
                    </span>
                </div>
            </div>
        </footer>
    </div>

    <!-- 模态放大显示 -->
    <div id="modal" class="modal">
        <span id="modalClose" class="close-btn">&times;</span>
        <canvas id="modalCanvas" class="modal-content"></canvas>
    </div>

    <!-- 通知弹窗 -->
    <div id="notification-container"></div>

    <script type="module" src="simple-video.js"></script>
    <!-- 轨迹修复测试脚本 (仅在调试模式下加载) -->
    <script>
        // 检查是否启用调试模式
        if (window.location.search.includes('debug=trajectory') || window.location.search.includes('test=trajectory')) {
            const testScript = document.createElement('script');
            testScript.src = 'trajectory-fix-test.js';
            testScript.defer = true;
            document.head.appendChild(testScript);
            console.log('🧪 轨迹测试模式已启用');
        }
    </script>
</body>
</html>