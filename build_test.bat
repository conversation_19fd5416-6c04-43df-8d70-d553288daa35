@echo off
echo 🔧 开始重新编译Camera_Editor项目...

REM 清理旧的构建文件
if exist build rmdir /s /q build
mkdir build
cd build

echo 📦 运行CMake配置...
cmake .. -G "Visual Studio 17 2022" -A x64

echo 🔨 开始编译...
cmake --build . --config Debug --parallel

if %ERRORLEVEL% EQU 0 (
    echo ✅ 编译成功完成！
    echo.
    echo 🚀 可执行文件位置: build\Debug\Camera_Editor.exe
    echo.
    pause
) else (
    echo ❌ 编译失败，请检查错误信息
    echo.
    pause
) 