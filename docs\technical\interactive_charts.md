# 交互式速度-时间图表技术文档

## 📋 概述

交互式速度-时间图表是Camera_Editor项目中的高级数据可视化组件，支持缩放、平移、数据标签等交互功能，专门用于分析双摄像头系统的速度数据变化趋势。

## 🎯 核心功能

### 1. 交互功能
- **鼠标滚轮缩放**: 0.1x - 10x 缩放范围
- **鼠标拖拽平移**: 支持水平和垂直方向平移
- **按钮控制**: 放大、缩小、重置视图按钮
- **键盘操作**: 方向键平移、+/- 缩放、0 重置
- **数据切换**: 左右摄像头数据独立显示控制

### 2. 数据特性
- **双数据源**: 区分左右摄像头数据（camera_id = 1/2）
- **实时更新**: 支持历史数据和实时数据显示
- **智能采样**: 自动采样大数据集（最大1000点）
- **时间精度**: 根据缩放级别自适应时间标签精度

### 3. 视觉效果
- **颜色区分**: 左摄像头蓝色，右摄像头红色
- **工具提示**: 鼠标悬停显示精确数值和时间戳
- **操作指引**: 右上角显示交互操作说明
- **状态反馈**: 实时显示缩放级别和平移状态

## 🏗️ 技术架构

### 类结构
```javascript
class SpeedTimeInteractiveChart {
    constructor(canvasId, options = {})
    
    // 核心方法
    initChart()           // 初始化Chart.js图表
    bindEvents()          // 绑定交互事件
    updateData()          // 更新数据
    processData()         // 处理数据
    
    // 交互处理
    handleZoom(event)     // 处理缩放
    handlePan(event)      // 处理平移
    handleKeyboard(event) // 处理键盘
    
    // 视图控制
    updateChartZoom()     // 更新缩放视图
    updateChartPan()      // 更新平移视图
    resetView()           // 重置视图
    
    // 数据管理
    fetchLeftCameraData() // 获取左摄像头数据
    fetchRightCameraData()// 获取右摄像头数据
    sampleData()          // 数据采样
}
```

### 初始化配置
```javascript
// 位置: Web/frontend/simple-video.js
const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
        intersect: false,
        mode: 'index'
    },
    scales: {
        x: {
            type: 'time',
            time: {
                unit: 'second',
                displayFormats: {
                    second: 'HH:mm:ss',
                    minute: 'HH:mm',
                    hour: 'HH:mm'
                }
            },
            title: {
                display: true,
                text: '时间',
                color: 'rgba(0, 212, 255, 1)'
            }
        },
        y: {
            title: {
                display: true,
                text: '速度 (m/s)',
                color: 'rgba(0, 212, 255, 1)'
            },
            min: 0
        }
    },
    plugins: {
        legend: {
            display: true,
            position: 'top'
        },
        tooltip: {
            mode: 'index',
            intersect: false,
            callbacks: {
                title: function(context) {
                    return new Date(context[0].parsed.x).toLocaleString();
                },
                label: function(context) {
                    return `${context.dataset.label}: ${context.parsed.y.toFixed(2)} m/s`;
                }
            }
        }
    }
};
```

## 🖱️ 交互功能实现

### 1. 鼠标滚轮缩放
```javascript
handleZoom(event) {
    event.preventDefault();
    
    const rect = this.canvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    
    // 计算缩放中心点
    const canvasPosition = Chart.helpers.getRelativePosition(event, this.chart);
    const dataX = this.chart.scales.x.getValueForPixel(canvasPosition.x);
    const dataY = this.chart.scales.y.getValueForPixel(canvasPosition.y);
    
    // 计算缩放因子
    const zoomFactor = event.deltaY > 0 ? 0.9 : 1.1;
    this.zoomLevel = Math.max(0.1, Math.min(10, this.zoomLevel * zoomFactor));
    
    // 更新视图
    this.updateChartZoom(dataX, dataY);
    this.updateStatus();
}
```

### 2. 鼠标拖拽平移
```javascript
handlePan(event) {
    if (!this.isDragging) return;
    
    const rect = this.canvas.getBoundingClientRect();
    const currentX = event.clientX - rect.left;
    const currentY = event.clientY - rect.top;
    
    // 计算移动距离
    const deltaX = currentX - this.lastMouseX;
    const deltaY = currentY - this.lastMouseY;
    
    // 转换为数据坐标系的偏移
    const xScale = this.chart.scales.x;
    const yScale = this.chart.scales.y;
    
    const dataOffsetX = (xScale.max - xScale.min) * (deltaX / this.canvas.width);
    const dataOffsetY = (yScale.max - yScale.min) * (deltaY / this.canvas.height);
    
    // 更新偏移量（注意Y轴方向）
    this.panOffsetX -= dataOffsetX;
    this.panOffsetY += dataOffsetY;
    
    // 更新视图
    this.updateChartPan();
    
    // 更新鼠标位置
    this.lastMouseX = currentX;
    this.lastMouseY = currentY;
}
```

### 3. 键盘操作
```javascript
handleKeyboard(event) {
    const step = 20; // 像素步长
    
    switch(event.key) {
        case 'ArrowLeft':
            this.panOffsetX -= this.getDataStep(step, 'x');
            this.updateChartPan();
            break;
        case 'ArrowRight':
            this.panOffsetX += this.getDataStep(step, 'x');
            this.updateChartPan();
            break;
        case 'ArrowUp':
            this.panOffsetY += this.getDataStep(step, 'y');
            this.updateChartPan();
            break;
        case 'ArrowDown':
            this.panOffsetY -= this.getDataStep(step, 'y');
            this.updateChartPan();
            break;
        case '+':
        case '=':
            this.zoomLevel = Math.min(10, this.zoomLevel * 1.2);
            this.updateChartZoom();
            break;
        case '-':
            this.zoomLevel = Math.max(0.1, this.zoomLevel * 0.8);
            this.updateChartZoom();
            break;
        case '0':
            this.resetView();
            break;
    }
    
    this.updateStatus();
}
```

## 📊 双摄像头数据处理

### 1. 数据查询
```javascript
async fetchCameraData(cameraId, timeRange = '1hour') {
    const timeRanges = {
        '1min': 60 * 1000,
        '5min': 5 * 60 * 1000,
        '30min': 30 * 60 * 1000,
        '1hour': 60 * 60 * 1000
    };
    
    const timeLimit = timeRanges[timeRange] || timeRanges['1hour'];
    const currentTime = Date.now();
    const startTime = currentTime - timeLimit;
    
    const query = `
        SELECT timestamp_ms, speed 
        FROM trajectory 
        WHERE camera_id = ${cameraId} 
        AND timestamp_ms > ${startTime}
        ORDER BY timestamp_ms ASC
        LIMIT 1000
    `;
    
    try {
        const response = await fetch('/api/db/query', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ query })
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return await response.json();
    } catch (error) {
        console.error(`Failed to fetch camera ${cameraId} data:`, error);
        return [];
    }
}
```

### 2. 数据处理和采样
```javascript
processData(rawData, cameraId) {
    // 数据清洗
    const cleanData = rawData
        .filter(point => point.speed > 0 && point.speed < 100) // 过滤异常值
        .map(point => ({
            x: point.timestamp_ms,
            y: point.speed,
            cameraId: cameraId
        }));
    
    // 智能采样
    return this.sampleData(cleanData, 1000);
}

sampleData(data, maxPoints) {
    if (data.length <= maxPoints) {
        return data;
    }
    
    const step = Math.floor(data.length / maxPoints);
    const sampled = [];
    
    for (let i = 0; i < data.length; i += step) {
        // 取窗口内的平均值
        const window = data.slice(i, Math.min(i + step, data.length));
        const avgSpeed = window.reduce((sum, p) => sum + p.y, 0) / window.length;
        
        sampled.push({
            x: window[Math.floor(window.length / 2)].x, // 中位时间
            y: avgSpeed,
            cameraId: data[i].cameraId
        });
    }
    
    return sampled;
}
```

### 3. 数据集配置
```javascript
createDatasets(leftData, rightData) {
    const datasets = [];
    
    if (this.showLeftCamera && leftData.length > 0) {
        datasets.push({
            label: '左摄像头',
            data: leftData,
            borderColor: 'rgba(54, 162, 235, 1)',
            backgroundColor: 'rgba(54, 162, 235, 0.1)',
            borderWidth: 2,
            pointRadius: 2,
            pointHoverRadius: 4,
            tension: 0.1
        });
    }
    
    if (this.showRightCamera && rightData.length > 0) {
        datasets.push({
            label: '右摄像头',
            data: rightData,
            borderColor: 'rgba(255, 99, 132, 1)',
            backgroundColor: 'rgba(255, 99, 132, 0.1)',
            borderWidth: 2,
            pointRadius: 2,
            pointHoverRadius: 4,
            tension: 0.1
        });
    }
    
    return datasets;
}
```

## 🎨 视图控制

### 1. 缩放控制
```javascript
updateChartZoom(centerX = null, centerY = null) {
    const xScale = this.chart.scales.x;
    const yScale = this.chart.scales.y;
    
    // 计算新的显示范围
    const baseRangeX = this.originalRangeX || (xScale.max - xScale.min);
    const baseRangeY = this.originalRangeY || (yScale.max - yScale.min);
    
    const newRangeX = baseRangeX / this.zoomLevel;
    const newRangeY = baseRangeY / this.zoomLevel;
    
    // 如果指定了中心点，围绕中心点缩放
    if (centerX !== null && centerY !== null) {
        const newMinX = centerX - newRangeX / 2;
        const newMaxX = centerX + newRangeX / 2;
        const newMinY = centerY - newRangeY / 2;
        const newMaxY = centerY + newRangeY / 2;
        
        xScale.options.min = newMinX;
        xScale.options.max = newMaxX;
        yScale.options.min = Math.max(0, newMinY);
        yScale.options.max = newMaxY;
    } else {
        // 保持当前中心点
        const currentCenterX = (xScale.min + xScale.max) / 2;
        const currentCenterY = (yScale.min + yScale.max) / 2;
        
        xScale.options.min = currentCenterX - newRangeX / 2;
        xScale.options.max = currentCenterX + newRangeX / 2;
        yScale.options.min = Math.max(0, currentCenterY - newRangeY / 2);
        yScale.options.max = currentCenterY + newRangeY / 2;
    }
    
    // 更新时间轴精度
    this.updateTimeAxisPrecision();
    
    this.chart.update('none');
}
```

### 2. 时间轴精度自适应
```javascript
updateTimeAxisPrecision() {
    const xScale = this.chart.scales.x;
    const timeRange = xScale.max - xScale.min;
    
    // 根据时间范围调整显示精度
    if (timeRange < 60 * 1000) { // 小于1分钟
        xScale.options.time.unit = 'second';
        xScale.options.time.displayFormats.second = 'HH:mm:ss';
    } else if (timeRange < 60 * 60 * 1000) { // 小于1小时
        xScale.options.time.unit = 'minute';
        xScale.options.time.displayFormats.minute = 'HH:mm';
    } else {
        xScale.options.time.unit = 'hour';
        xScale.options.time.displayFormats.hour = 'MM-DD HH:mm';
    }
}
```

### 3. 状态反馈
```javascript
updateStatus() {
    const statusElement = document.getElementById('chart-status');
    if (statusElement) {
        statusElement.innerHTML = `
            <div class="status-item">
                <span class="status-label">缩放级别:</span>
                <span class="status-value">${this.zoomLevel.toFixed(1)}x</span>
            </div>
            <div class="status-item">
                <span class="status-label">数据点数:</span>
                <span class="status-value">${this.getTotalDataPoints()}</span>
            </div>
            <div class="status-item">
                <span class="status-label">更新时间:</span>
                <span class="status-value">${new Date().toLocaleTimeString()}</span>
            </div>
        `;
    }
}
```

## 🎛️ 用户界面集成

### 1. 控制面板
```html
<!-- 位置: Web/frontend/index.html -->
<div class="interactive-chart-controls">
    <div class="zoom-controls">
        <button id="zoom-in" class="control-btn" title="放大">🔍+</button>
        <button id="zoom-out" class="control-btn" title="缩小">🔍-</button>
        <button id="reset-view" class="control-btn" title="重置视图">🏠</button>
    </div>
    
    <div class="camera-controls">
        <label class="camera-toggle">
            <input type="checkbox" id="show-left-camera" checked>
            <span class="camera-label left-camera">左摄像头</span>
        </label>
        <label class="camera-toggle">
            <input type="checkbox" id="show-right-camera" checked>
            <span class="camera-label right-camera">右摄像头</span>
        </label>
    </div>
    
    <div class="chart-status" id="chart-status"></div>
</div>
```

### 2. 样式设计
```css
/* 位置: Web/frontend/style.css */
.interactive-chart-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: rgba(0, 20, 40, 0.8);
    border-radius: 5px;
    margin-bottom: 10px;
}

.control-btn {
    background: linear-gradient(135deg, rgba(0, 212, 255, 0.2), rgba(0, 150, 200, 0.2));
    border: 1px solid rgba(0, 212, 255, 0.5);
    color: rgba(0, 212, 255, 1);
    padding: 8px 12px;
    margin: 0 2px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.control-btn:hover {
    background: linear-gradient(135deg, rgba(0, 212, 255, 0.4), rgba(0, 150, 200, 0.4));
    transform: translateY(-1px);
}

.camera-toggle {
    display: flex;
    align-items: center;
    margin: 0 10px;
    cursor: pointer;
}

.camera-label.left-camera {
    color: rgba(54, 162, 235, 1);
}

.camera-label.right-camera {
    color: rgba(255, 99, 132, 1);
}
```

## 🧪 测试和验证

### 1. 功能测试
```javascript
// 测试交互功能
describe('Interactive Chart', () => {
    let chart;
    
    beforeEach(() => {
        chart = new SpeedTimeInteractiveChart('test-canvas');
    });
    
    test('Zoom functionality', () => {
        const initialZoom = chart.zoomLevel;
        chart.handleZoom({ deltaY: -100, preventDefault: () => {} });
        expect(chart.zoomLevel).toBeGreaterThan(initialZoom);
    });
    
    test('Pan functionality', () => {
        chart.isDragging = true;
        chart.lastMouseX = 100;
        chart.lastMouseY = 100;
        
        const initialOffsetX = chart.panOffsetX;
        chart.handlePan({ clientX: 150, clientY: 100 });
        expect(chart.panOffsetX).not.toBe(initialOffsetX);
    });
});
```

### 2. 性能测试
```javascript
// 性能基准测试
function benchmarkInteractiveChart() {
    const testData = generateTestData(1000);
    const startTime = performance.now();
    
    chart.updateData(testData);
    
    const endTime = performance.now();
    console.log(`Chart update took ${endTime - startTime} milliseconds`);
    
    return endTime - startTime;
}
```

---

**相关文档**:
- [数据可视化技术实现说明](data_visualization.md)
- [数据可视化系统测试指南](testing_guides.md)
- [AI助手项目理解文档](../AI_PROJECT_CONTEXT.md)
