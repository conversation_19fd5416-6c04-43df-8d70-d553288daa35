@echo off
setlocal enabledelayedexpansion
chcp 65001 >nul 2>&1
echo ===== 双目视觉检测Web服务器启动脚本 =====
echo.

REM 确保从Web目录运行
cd /d "%~dp0"
echo 当前工作目录: %CD%
echo.

REM 检查前端文件
if exist "frontend\index.html" (
    echo ✓ 前端文件检查通过
) else (
    echo ✗ 前端文件未找到！
    echo 请确保frontend文件夹存在且包含index.html
    pause
    exit /b 1
)

REM 检查可执行文件是否存在
set EXE_PATH=""
if exist "backend\build\camera_web_server.exe" (
    set EXE_PATH=backend\build\camera_web_server.exe
    echo ✓ 找到可执行文件: !EXE_PATH!
    goto :server_found
)

if exist "backend\build_clean\camera_web_server_clean.exe" (
    set EXE_PATH=backend\build_clean\camera_web_server_clean.exe
    echo ✓ 找到可执行文件: !EXE_PATH! (旧版本)
    goto :server_found
)

REM 如果到这里说明没有找到可执行文件
echo ✗ 服务器可执行文件未找到！
echo 请先编译项目:
echo   cd backend
echo   build.bat
echo.
pause
exit /b 1

:server_found
echo ✓ 所有文件检查通过
echo.
echo 启动Web服务器...
echo 前端界面将在 http://localhost:8080 可用
echo 按 Ctrl+C 停止服务器
echo.

REM 启动服务器（确保从Web目录运行）
"!EXE_PATH!"

echo.
echo 服务器已停止
pause 