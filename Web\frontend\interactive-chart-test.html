<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>交互式速度图表测试</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.1/dist/chart.umd.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: #ffffff;
            margin: 0;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .chart-container {
            background: rgba(0, 0, 0, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .chart-controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .btn {
            background: rgba(0, 255, 170, 0.2);
            border: 1px solid rgba(0, 255, 170, 0.5);
            color: #ffffff;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn:hover {
            background: rgba(0, 255, 170, 0.3);
        }
        
        .camera-toggle {
            display: flex;
            align-items: center;
            gap: 6px;
            margin-left: 10px;
        }
        
        .toggle-label.left-camera {
            color: rgba(54, 162, 235, 1);
        }
        
        .toggle-label.right-camera {
            color: rgba(255, 99, 132, 1);
        }
        
        .chart-wrapper {
            position: relative;
            height: 400px;
        }
        
        .chart-instructions {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.7);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            padding: 8px 12px;
            font-size: 12px;
            color: #a0a0a0;
            pointer-events: none;
            z-index: 10;
        }
        
        .instruction-item {
            display: flex;
            align-items: center;
            gap: 6px;
            margin-bottom: 4px;
        }
        
        .instruction-item:last-child {
            margin-bottom: 0;
        }
        
        .status {
            color: #00ffaa;
            font-size: 14px;
        }
        
        canvas {
            cursor: grab;
        }
        
        canvas:active {
            cursor: grabbing;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>交互式速度-时间图表测试</h1>
        
        <div class="chart-container">
            <div class="chart-header">
                <h3>🔍 交互式速度分析</h3>
                <div class="chart-controls">
                    <button class="btn" id="zoomInBtn">放大</button>
                    <button class="btn" id="zoomOutBtn">缩小</button>
                    <button class="btn" id="resetViewBtn">重置</button>
                    <button class="btn" id="generateDataBtn">生成测试数据</button>
                    
                    <label class="camera-toggle">
                        <input type="checkbox" id="leftCameraToggle" checked>
                        <span class="toggle-label left-camera">左摄像头</span>
                    </label>
                    <label class="camera-toggle">
                        <input type="checkbox" id="rightCameraToggle" checked>
                        <span class="toggle-label right-camera">右摄像头</span>
                    </label>
                    
                    <div class="status" id="chartStatus">准备中...</div>
                </div>
            </div>
            <div class="chart-wrapper">
                <canvas id="speedTimeInteractiveChart"></canvas>
                <div class="chart-instructions">
                    <div class="instruction-item">
                        <span>🖱️ 滚轮缩放 | 拖拽平移</span>
                    </div>
                    <div class="instruction-item">
                        <span>⌨️ 方向键平移 | +/- 缩放 | 0 重置</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟API响应
        function mockApiResponse(query) {
            const rows = [];
            const currentTime = Date.now();
            const startTime = currentTime - (5 * 60 * 1000); // 5分钟前
            
            // 生成模拟数据
            for (let i = 0; i < 100; i++) {
                const timestamp = startTime + (i * 3000); // 每3秒一个数据点
                const speed = Math.random() * 20 + 5; // 5-25 m/s
                rows.push([timestamp, speed]);
            }
            
            return Promise.resolve({
                ok: true,
                json: () => Promise.resolve({ rows })
            });
        }

        // 重写fetch函数用于测试
        const originalFetch = window.fetch;
        window.fetch = function(url, options) {
            if (url === '/api/db/query') {
                return mockApiResponse(JSON.parse(options.body).query);
            }
            return originalFetch.apply(this, arguments);
        };

        // 等待DOM加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🚀 初始化交互式图表测试页面');
            
            try {
                // 这里需要包含SpeedTimeInteractiveChart类的定义
                // 由于这是测试页面，我们需要简化版本
                initTestChart();
            } catch (error) {
                console.error('❌ 初始化失败:', error);
                document.getElementById('chartStatus').textContent = '初始化失败';
            }
        });

        function initTestChart() {
            console.log('🚀 开始初始化测试图表');

            // 创建简化版的交互式图表
            const testChart = new TestInteractiveChart('speedTimeInteractiveChart');

            // 绑定控制按钮
            document.getElementById('generateDataBtn').addEventListener('click', () => {
                testChart.generateTestData();
            });

            document.getElementById('zoomInBtn').addEventListener('click', () => {
                testChart.zoomIn();
            });

            document.getElementById('zoomOutBtn').addEventListener('click', () => {
                testChart.zoomOut();
            });

            document.getElementById('resetViewBtn').addEventListener('click', () => {
                testChart.resetView();
            });

            document.getElementById('chartStatus').textContent = '已初始化';
            console.log('✅ 测试图表初始化完成');
        }

        // 简化版的交互式图表类
        class TestInteractiveChart {
            constructor(canvasId) {
                this.canvas = document.getElementById(canvasId);
                this.zoomLevel = 1.0;
                this.panOffset = { x: 0, y: 0 };
                this.isDragging = false;
                this.lastMousePos = { x: 0, y: 0 };
                this.leftCameraData = [];
                this.rightCameraData = [];

                this.initChart();
                this.bindEvents();
            }

            initChart() {
                const ctx = this.canvas.getContext('2d');

                this.chart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        datasets: [
                            {
                                label: '左摄像头速度',
                                data: [],
                                borderColor: 'rgba(54, 162, 235, 1)',
                                backgroundColor: 'rgba(54, 162, 235, 0.1)',
                                borderWidth: 2,
                                fill: false,
                                tension: 0.1,
                                pointRadius: 0,
                                pointHoverRadius: 6
                            },
                            {
                                label: '右摄像头速度',
                                data: [],
                                borderColor: 'rgba(255, 99, 132, 1)',
                                backgroundColor: 'rgba(255, 99, 132, 0.1)',
                                borderWidth: 2,
                                fill: false,
                                tension: 0.1,
                                pointRadius: 0,
                                pointHoverRadius: 6
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                labels: {
                                    color: '#ffffff'
                                }
                            },
                            tooltip: {
                                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                                titleColor: '#ffffff',
                                bodyColor: '#ffffff',
                                borderColor: '#ffffff',
                                borderWidth: 1
                            }
                        },
                        scales: {
                            x: {
                                type: 'linear',
                                title: {
                                    display: true,
                                    text: '时间',
                                    color: '#a0a0a0'
                                },
                                ticks: {
                                    color: '#a0a0a0'
                                },
                                grid: {
                                    color: 'rgba(255, 255, 255, 0.1)'
                                }
                            },
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: '速度 (m/s)',
                                    color: '#a0a0a0'
                                },
                                ticks: {
                                    color: '#a0a0a0'
                                },
                                grid: {
                                    color: 'rgba(255, 255, 255, 0.1)'
                                }
                            }
                        }
                    }
                });
            }

            bindEvents() {
                // 鼠标滚轮缩放
                this.canvas.addEventListener('wheel', (e) => {
                    e.preventDefault();
                    this.handleZoom(e);
                });

                // 鼠标拖拽平移
                this.canvas.addEventListener('mousedown', (e) => {
                    this.isDragging = true;
                    this.lastMousePos = { x: e.clientX, y: e.clientY };
                    this.canvas.style.cursor = 'grabbing';
                });

                this.canvas.addEventListener('mousemove', (e) => {
                    if (this.isDragging) {
                        this.handlePan(e);
                    }
                });

                this.canvas.addEventListener('mouseup', () => {
                    this.isDragging = false;
                    this.canvas.style.cursor = 'grab';
                });

                this.canvas.addEventListener('mouseleave', () => {
                    this.isDragging = false;
                    this.canvas.style.cursor = 'grab';
                });
            }

            handlePan(event) {
                const deltaX = event.clientX - this.lastMousePos.x;
                const deltaY = event.clientY - this.lastMousePos.y;

                // 修正平移方向
                this.panOffset.x -= deltaX;
                this.panOffset.y += deltaY;

                this.lastMousePos = { x: event.clientX, y: event.clientY };

                console.log(`🖱️ 拖拽平移: deltaX=${deltaX}, deltaY=${deltaY}, panOffset=(${this.panOffset.x.toFixed(1)}, ${this.panOffset.y.toFixed(1)})`);
                this.updateZoom();
            }

            handleZoom(event) {
                const zoomFactor = event.deltaY > 0 ? 0.9 : 1.1;
                const oldZoom = this.zoomLevel;
                this.zoomLevel *= zoomFactor;
                this.zoomLevel = Math.max(0.1, Math.min(10, this.zoomLevel));

                console.log(`🖱️ 滚轮缩放: ${oldZoom.toFixed(2)} -> ${this.zoomLevel.toFixed(2)}`);
                this.updateZoom();
            }

            zoomIn() {
                const oldZoom = this.zoomLevel;
                this.zoomLevel *= 1.2;
                this.zoomLevel = Math.min(10, this.zoomLevel);
                console.log(`🔍+ 放大: ${oldZoom.toFixed(2)} -> ${this.zoomLevel.toFixed(2)}`);
                this.updateZoom();
            }

            zoomOut() {
                const oldZoom = this.zoomLevel;
                this.zoomLevel *= 0.8;
                this.zoomLevel = Math.max(0.1, this.zoomLevel);
                console.log(`🔍- 缩小: ${oldZoom.toFixed(2)} -> ${this.zoomLevel.toFixed(2)}`);
                this.updateZoom();
            }

            resetView() {
                this.zoomLevel = 1.0;
                this.panOffset = { x: 0, y: 0 };

                delete this.chart.options.scales.x.min;
                delete this.chart.options.scales.x.max;
                delete this.chart.options.scales.y.min;
                this.chart.options.scales.y.max = undefined;
                this.chart.options.scales.y.beginAtZero = true;

                this.chart.update('none');
                document.getElementById('chartStatus').textContent = '视图已重置';
                console.log('🏠 视图已重置');
            }

            updateZoom() {
                const allData = [...this.leftCameraData, ...this.rightCameraData];
                if (allData.length === 0) {
                    console.log('❌ 没有数据可缩放');
                    return;
                }

                const timeValues = allData.map(point => point.x);
                const speedValues = allData.map(point => point.y);

                const minTime = Math.min(...timeValues);
                const maxTime = Math.max(...timeValues);
                const minSpeed = Math.min(...speedValues);
                const maxSpeed = Math.max(...speedValues);

                const timeRange = maxTime - minTime;
                const speedRange = maxSpeed - minSpeed;

                const zoomedTimeRange = timeRange / this.zoomLevel;
                const zoomedSpeedRange = speedRange / this.zoomLevel;

                // 考虑平移偏移
                const timePanFactor = this.panOffset.x / this.canvas.width;
                const speedPanFactor = this.panOffset.y / this.canvas.height;

                const centerTime = minTime + timeRange * 0.5 + timeRange * timePanFactor * 0.3;
                const centerSpeed = minSpeed + speedRange * 0.5 + speedRange * speedPanFactor * 0.3;

                this.chart.options.scales.x.min = centerTime - zoomedTimeRange * 0.5;
                this.chart.options.scales.x.max = centerTime + zoomedTimeRange * 0.5;
                this.chart.options.scales.y.min = Math.max(0, centerSpeed - zoomedSpeedRange * 0.5);
                this.chart.options.scales.y.max = centerSpeed + zoomedSpeedRange * 0.5;

                this.chart.update('none');
                document.getElementById('chartStatus').textContent = `缩放: ${this.zoomLevel.toFixed(2)}x, 平移: (${this.panOffset.x.toFixed(0)}, ${this.panOffset.y.toFixed(0)})`;
            }

            generateTestData() {
                const currentTime = Date.now();
                const startTime = currentTime - (5 * 60 * 1000);

                this.leftCameraData = [];
                this.rightCameraData = [];

                for (let i = 0; i < 50; i++) {
                    const timestamp = startTime + (i * 6000);
                    this.leftCameraData.push({
                        x: timestamp,
                        y: Math.random() * 15 + 5
                    });
                    this.rightCameraData.push({
                        x: timestamp,
                        y: Math.random() * 20 + 3
                    });
                }

                this.chart.data.datasets[0].data = this.leftCameraData;
                this.chart.data.datasets[1].data = this.rightCameraData;
                this.chart.update();

                document.getElementById('chartStatus').textContent = '数据已生成';
                console.log('📊 测试数据已生成');
            }
        }


    </script>
</body>
</html>
