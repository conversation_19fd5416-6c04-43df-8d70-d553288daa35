#include "ErrorManagementInit.hpp"
#include "ErrorManager.hpp"
#include "SharedData.hpp"
#include <iostream>
#include <thread>

namespace ErrorManagement {

// 静态成员初始化
std::shared_ptr<SharedData> ErrorManagementInitializer::shared_data_ = nullptr;

bool ErrorManagementInitializer::initialize(std::shared_ptr<SharedData> shared_data, 
                                           ErrorSeverity log_level) {
    shared_data_ = shared_data;
    
    try {
        // 初始化错误管理器
        ErrorManager::getInstance().initialize("C:/Dev/Camera_Editor/Data/error.log", log_level);
        
        // 注册恢复处理器
        registerRecoveryHandlers(shared_data);
        
        // 配置210FPS优化
        configure210FPSOptimizations();
        
        ErrorManager::getInstance().log(ErrorSeverity::INFO_LEVEL, ErrorCategory::SYSTEM_ERROR,
                                      "Error Management System initialized successfully");
        
        return true;
    } catch (const std::exception& ex) {
        std::cerr << "Failed to initialize Error Management System: " << ex.what() << std::endl;
        return false;
    }
}

void ErrorManagementInitializer::registerRecoveryHandlers(std::shared_ptr<SharedData> shared_data) {
    auto& error_manager = ErrorManager::getInstance();
    
    // 相机错误恢复
    error_manager.registerRecoveryHandler(ErrorCategory::CAMERA_ERROR, 
        []() { return recoverCameraError(); });
    
    // AI推理错误恢复
    error_manager.registerRecoveryHandler(ErrorCategory::INFERENCE_ERROR,
        []() { return recoverInferenceError(); });
    
    // 三维重建错误恢复
    error_manager.registerRecoveryHandler(ErrorCategory::RECONSTRUCTION_ERROR,
        []() { return recoverReconstructionError(); });
    
    // 录制错误恢复
    error_manager.registerRecoveryHandler(ErrorCategory::RECORDING_ERROR,
        []() { return recoverRecordingError(); });
    
    // 数据库错误恢复
    error_manager.registerRecoveryHandler(ErrorCategory::DATABASE_ERROR,
        []() { return recoverDatabaseError(); });
    
    // 网络错误恢复
    error_manager.registerRecoveryHandler(ErrorCategory::NETWORK_ERROR,
        []() { return recoverNetworkError(); });
}

void ErrorManagementInitializer::configure210FPSOptimizations() {
    auto& error_manager = ErrorManager::getInstance();
    
    // 启用快速路径错误处理
    error_manager.enableFastPath(true);
    
    // 为高性能模式配置最小日志级别
    // 在210FPS模式下，只记录ERROR及以上级别的错误
}

void ErrorManagementInitializer::shutdown() {
    if (shared_data_) {
        ErrorManager::getInstance().log(ErrorSeverity::INFO_LEVEL, ErrorCategory::SYSTEM_ERROR,
                                      "Shutting down Error Management System");
    }
    
    ErrorManager::getInstance().shutdown();
    shared_data_.reset();
}

// 具体的恢复处理器实现
bool ErrorManagementInitializer::recoverCameraError() {
    // 相机错误恢复逻辑
    try {
        // 1. 重置相机连接
        // 2. 重新初始化相机设备
        // 3. 恢复相机配置
        
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        
        if (shared_data_) {
            shared_data_->setSystemStatus("Camera recovery attempted");
        }
        
        return true; // 假设恢复成功
    } catch (...) {
        return false;
    }
}

bool ErrorManagementInitializer::recoverInferenceError() {
    // AI推理错误恢复逻辑
    try {
        // 1. 重新加载模型
        // 2. 重置GPU状态
        // 3. 清理推理缓存
        
        std::this_thread::sleep_for(std::chrono::milliseconds(50));
        
        if (shared_data_) {
            shared_data_->setSystemStatus("AI inference recovery attempted");
        }
        
        return true;
    } catch (...) {
        return false;
    }
}

bool ErrorManagementInitializer::recoverReconstructionError() {
    // 三维重建错误恢复逻辑
    try {
        // 1. 重置标定参数
        // 2. 清理重建缓存
        // 3. 重新启动重建算法
        
        std::this_thread::sleep_for(std::chrono::milliseconds(30));
        
        if (shared_data_) {
            shared_data_->setSystemStatus("3D reconstruction recovery attempted");
        }
        
        return true;
    } catch (...) {
        return false;
    }
}

bool ErrorManagementInitializer::recoverRecordingError() {
    // 录制错误恢复逻辑
    try {
        // 1. 停止当前录制
        // 2. 清理录制缓冲区
        // 3. 重新启动录制服务
        
        std::this_thread::sleep_for(std::chrono::milliseconds(200));
        
        if (shared_data_) {
            shared_data_->setSystemStatus("Recording service recovery attempted");
        }
        
        return true;
    } catch (...) {
        return false;
    }
}

bool ErrorManagementInitializer::recoverDatabaseError() {
    // 数据库错误恢复逻辑
    try {
        // 1. 关闭数据库连接
        // 2. 重新连接数据库
        // 3. 验证数据库完整性
        
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        
        if (shared_data_) {
            shared_data_->setSystemStatus("Database recovery attempted");
        }
        
        return true;
    } catch (...) {
        return false;
    }
}

bool ErrorManagementInitializer::recoverNetworkError() {
    // 网络错误恢复逻辑
    try {
        // 1. 重置网络连接
        // 2. 重新启动Web服务
        // 3. 恢复WebSocket连接
        
        std::this_thread::sleep_for(std::chrono::milliseconds(150));
        
        if (shared_data_) {
            shared_data_->setSystemStatus("Network service recovery attempted");
        }
        
        return true;
    } catch (...) {
        return false;
    }
}

// === ErrorConfigPresets 实现 ===
namespace ErrorConfigPresets {

void setDevelopmentMode() {
    auto& error_manager = ErrorManager::getInstance();
    error_manager.enableFastPath(false); // 开发模式不使用快速路径
    // 开发模式记录所有级别的日志
}

void setProductionMode() {
    auto& error_manager = ErrorManager::getInstance();
    error_manager.enableFastPath(true); // 生产模式启用快速路径
    // 生产模式只记录WARNING及以上级别
}

void setHighPerformanceMode() {
    auto& error_manager = ErrorManager::getInstance();
    error_manager.enableFastPath(true); // 高性能模式启用快速路径
    // 高性能模式只记录ERROR及以上级别
}

void setDebuggingMode(ErrorCategory focus_category) {
    auto& error_manager = ErrorManager::getInstance();
    error_manager.enableFastPath(false); // 调试模式不使用快速路径
    // 调试模式针对特定类别记录详细日志
}

} // namespace ErrorConfigPresets

// === SystemHealthMonitor 实现 ===
SystemHealthMonitor::SystemHealthMonitor(std::shared_ptr<SharedData> shared_data)
    : shared_data_(shared_data) {
}

SystemHealthMonitor::~SystemHealthMonitor() {
    stop();
}

void SystemHealthMonitor::start() {
    should_stop_ = false;
    monitor_thread_ = std::thread(&SystemHealthMonitor::monitorLoop, this);
}

void SystemHealthMonitor::stop() {
    should_stop_ = true;
    if (monitor_thread_.joinable()) {
        monitor_thread_.join();
    }
}

void SystemHealthMonitor::monitorLoop() {
    while (!should_stop_) {
        try {
            checkSystemHealth();
            reportHealthStatus();
            
            std::this_thread::sleep_for(check_interval_);
        } catch (const std::exception& ex) {
            ErrorManager::getInstance().log(ErrorSeverity::ERROR_LEVEL, 
                                          ErrorCategory::SYSTEM_ERROR,
                                          "Health monitor error: " + std::string(ex.what()));
        }
    }
}

void SystemHealthMonitor::checkSystemHealth() {
    auto& error_manager = ErrorManager::getInstance();
    
    // 检查错误统计
    bool is_healthy = error_manager.isSystemHealthy();
    
    if (!is_healthy) {
        error_manager.log(ErrorSeverity::WARNING_LEVEL, ErrorCategory::SYSTEM_ERROR,
                         "System health check failed - high error rate detected");
    }
}

void SystemHealthMonitor::reportHealthStatus() {
    if (shared_data_) {
        auto& error_manager = ErrorManager::getInstance();
        bool is_healthy = error_manager.isSystemHealthy();
        
        std::string status = is_healthy ? "System healthy" : "System health issues detected";
        shared_data_->setSystemStatus(status);
    }
}

// === ErrorReportGenerator 实现 ===
ErrorReportGenerator::SystemReport ErrorReportGenerator::generateReport(std::shared_ptr<SharedData> shared_data) {
    SystemReport report;
    report.report_time = std::chrono::system_clock::now();
    
    auto& error_manager = ErrorManager::getInstance();
    auto& statistics = error_manager.getStatistics();
    
    report.uptime = statistics.getUptime();
    report.system_healthy = error_manager.isSystemHealthy();
    
    // 收集错误统计
    for (int i = 0; i <= static_cast<int>(ErrorCategory::UNKNOWN_ERROR); ++i) {
        ErrorCategory category = static_cast<ErrorCategory>(i);
        report.error_counts[category] = statistics.getErrorCount(category);
    }
    
    for (int i = 0; i <= static_cast<int>(ErrorSeverity::FATAL_LEVEL); ++i) {
        ErrorSeverity severity = static_cast<ErrorSeverity>(i);
        report.severity_counts[severity] = statistics.getSeverityCount(severity);
    }
    
    // 计算性能指标
    report.average_fps = 0.0; // 需要从SharedData获取
    report.total_frames_processed = 0; // 需要从SharedData获取
    report.failed_operations = report.severity_counts[ErrorSeverity::ERROR_LEVEL] + 
                              report.severity_counts[ErrorSeverity::CRITICAL_LEVEL] +
                              report.severity_counts[ErrorSeverity::FATAL_LEVEL];
    
    int total_operations = report.total_frames_processed + report.failed_operations;
    report.success_rate = total_operations > 0 ? 
        (1.0 - static_cast<double>(report.failed_operations) / total_operations) * 100.0 : 100.0;
    
    return report;
}

bool ErrorReportGenerator::saveReportToFile(const SystemReport& report, const std::string& file_path) {
    try {
        std::ofstream file(file_path);
        if (!file.is_open()) {
            return false;
        }
        
        file << formatReportAsText(report);
        return true;
    } catch (...) {
        return false;
    }
}

std::string ErrorReportGenerator::formatReportAsJson(const SystemReport& report) {
    std::ostringstream json;
    json << "{\n";
    json << "  \"report_time\": \"" << std::chrono::duration_cast<std::chrono::seconds>(
        report.report_time.time_since_epoch()).count() << "\",\n";
    json << "  \"uptime_seconds\": " << report.uptime.count() << ",\n";
    json << "  \"system_healthy\": " << (report.system_healthy ? "true" : "false") << ",\n";
    json << "  \"success_rate\": " << report.success_rate << ",\n";
    json << "  \"total_frames_processed\": " << report.total_frames_processed << ",\n";
    json << "  \"failed_operations\": " << report.failed_operations << "\n";
    json << "}";
    return json.str();
}

std::string ErrorReportGenerator::formatReportAsText(const SystemReport& report) {
    std::ostringstream text;
    text << "=== Camera_Editor System Error Report ===\n";
    text << "Report generated at: " << std::chrono::duration_cast<std::chrono::seconds>(
        report.report_time.time_since_epoch()).count() << "\n";
    text << "System uptime: " << report.uptime.count() << " seconds\n";
    text << "System healthy: " << (report.system_healthy ? "Yes" : "No") << "\n";
    text << "Success rate: " << report.success_rate << "%\n";
    text << "Total frames processed: " << report.total_frames_processed << "\n";
    text << "Failed operations: " << report.failed_operations << "\n";
    text << "\n=== Error Statistics ===\n";
    
    for (const auto& [category, count] : report.error_counts) {
        if (count > 0) {
            text << "Category " << static_cast<int>(category) << ": " << count << " errors\n";
        }
    }
    
    text << "\n=== Severity Statistics ===\n";
    for (const auto& [severity, count] : report.severity_counts) {
        if (count > 0) {
            text << "Severity " << static_cast<int>(severity) << ": " << count << " errors\n";
        }
    }
    
    return text.str();
}

} // namespace ErrorManagement