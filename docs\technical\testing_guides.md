# Camera_Editor 测试指南合集

## 📋 概述

本文档提供Camera_Editor项目的完整测试指南，包括数据可视化系统测试、交互式图表测试、系统集成测试等。

## 🧪 数据可视化系统测试指南

### 测试环境准备

#### 1. 测试数据准备
```sql
-- 创建测试数据表
CREATE TABLE IF NOT EXISTS trajectory_test (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    timestamp_ms INTEGER NOT NULL,
    pos_x REAL NOT NULL,
    pos_y REAL NOT NULL,
    pos_z REAL NOT NULL,
    speed REAL NOT NULL,
    camera_id INTEGER NOT NULL
);

-- 插入测试数据
INSERT INTO trajectory_test (timestamp_ms, pos_x, pos_y, pos_z, speed, camera_id)
VALUES 
    (1703001000000, 0.5, 1.0, 0.3, 15.2, 1),
    (1703001001000, 0.6, 1.1, 0.32, 16.8, 1),
    (1703001002000, 0.7, 1.2, 0.35, 18.5, 2),
    -- ... 更多测试数据
;
```

#### 2. 测试环境配置
```javascript
// 测试配置文件: test/config.js
const testConfig = {
    apiEndpoint: 'http://localhost:8080/api/db/query',
    testDataSize: 1000,
    performanceThreshold: {
        chartUpdate: 100, // ms
        dataFetch: 500,   // ms
        rendering: 50     // ms
    },
    browsers: ['Chrome', 'Firefox', 'Edge'],
    viewports: [
        { width: 1920, height: 1080 },
        { width: 1366, height: 768 },
        { width: 768, height: 1024 }
    ]
};
```

### 功能测试

#### 1. 图表初始化测试
```javascript
describe('Chart Initialization', () => {
    let chartManager;
    
    beforeEach(() => {
        // 创建测试DOM环境
        document.body.innerHTML = `
            <canvas id="speedDistributionChart"></canvas>
            <canvas id="speedTimeSeriesChart"></canvas>
            <canvas id="interactiveChart"></canvas>
        `;
        
        chartManager = new DataVisualizationManager();
    });
    
    afterEach(() => {
        chartManager.destroyCharts();
        document.body.innerHTML = '';
    });
    
    test('应该成功初始化所有图表', () => {
        chartManager.initializeCharts();
        
        expect(chartManager.charts.speedDistribution).toBeDefined();
        expect(chartManager.charts.speedTimeSeries).toBeDefined();
        expect(chartManager.charts.interactive).toBeDefined();
    });
    
    test('图表应该有正确的配置', () => {
        chartManager.initializeCharts();
        
        const chart = chartManager.charts.speedDistribution;
        expect(chart.config.type).toBe('bar');
        expect(chart.config.data.labels).toHaveLength(6);
    });
});
```

#### 2. 数据获取测试
```javascript
describe('Data Fetching', () => {
    test('应该成功获取速度分布数据', async () => {
        const data = await chartManager.fetchSpeedDistributionData();
        
        expect(Array.isArray(data)).toBe(true);
        expect(data.length).toBeGreaterThan(0);
        
        // 验证数据结构
        data.forEach(item => {
            expect(item).toHaveProperty('speed_range');
            expect(item).toHaveProperty('count');
            expect(typeof item.count).toBe('number');
        });
    });
    
    test('应该正确处理空数据', async () => {
        // 模拟空数据响应
        jest.spyOn(window, 'fetch').mockResolvedValue({
            ok: true,
            json: () => Promise.resolve([])
        });
        
        const data = await chartManager.fetchSpeedDistributionData();
        expect(data).toEqual([]);
    });
    
    test('应该正确处理网络错误', async () => {
        jest.spyOn(window, 'fetch').mockRejectedValue(new Error('Network error'));
        
        await expect(chartManager.fetchSpeedDistributionData())
            .rejects.toThrow('Network error');
    });
});
```

#### 3. 图表更新测试
```javascript
describe('Chart Updates', () => {
    test('应该正确更新速度分布图', async () => {
        chartManager.initializeCharts();
        
        const testData = [
            { speed_range: '0-5', count: 10 },
            { speed_range: '5-10', count: 15 },
            { speed_range: '10-15', count: 8 }
        ];
        
        await chartManager.updateSpeedDistributionChart(testData);
        
        const chart = chartManager.charts.speedDistribution;
        expect(chart.data.datasets[0].data).toEqual([10, 15, 8, 0, 0, 0]);
    });
    
    test('应该正确更新时间序列图', async () => {
        chartManager.initializeCharts();
        
        const testData = [
            { timestamp_ms: 1703001000000, speed: 15.2 },
            { timestamp_ms: 1703001001000, speed: 16.8 }
        ];
        
        await chartManager.updateSpeedTimeSeriesChart(testData);
        
        const chart = chartManager.charts.speedTimeSeries;
        expect(chart.data.datasets[0].data).toHaveLength(2);
    });
});
```

### 交互式图表测试

#### 1. 缩放功能测试
```javascript
describe('Interactive Chart Zoom', () => {
    let interactiveChart;
    
    beforeEach(() => {
        document.body.innerHTML = '<canvas id="interactiveChart"></canvas>';
        interactiveChart = new SpeedTimeInteractiveChart('interactiveChart');
    });
    
    test('鼠标滚轮缩放应该正常工作', () => {
        const initialZoom = interactiveChart.zoomLevel;
        
        // 模拟向上滚动（放大）
        const zoomEvent = {
            deltaY: -100,
            preventDefault: jest.fn(),
            clientX: 100,
            clientY: 100
        };
        
        interactiveChart.handleZoom(zoomEvent);
        
        expect(interactiveChart.zoomLevel).toBeGreaterThan(initialZoom);
        expect(zoomEvent.preventDefault).toHaveBeenCalled();
    });
    
    test('缩放应该有合理的范围限制', () => {
        // 测试最大缩放
        interactiveChart.zoomLevel = 9;
        interactiveChart.handleZoom({ deltaY: -100, preventDefault: () => {} });
        expect(interactiveChart.zoomLevel).toBeLessThanOrEqual(10);
        
        // 测试最小缩放
        interactiveChart.zoomLevel = 0.2;
        interactiveChart.handleZoom({ deltaY: 100, preventDefault: () => {} });
        expect(interactiveChart.zoomLevel).toBeGreaterThanOrEqual(0.1);
    });
});
```

#### 2. 平移功能测试
```javascript
describe('Interactive Chart Pan', () => {
    test('鼠标拖拽平移应该正常工作', () => {
        interactiveChart.isDragging = true;
        interactiveChart.lastMouseX = 100;
        interactiveChart.lastMouseY = 100;
        
        const initialOffsetX = interactiveChart.panOffsetX;
        
        const panEvent = {
            clientX: 150,
            clientY: 100
        };
        
        interactiveChart.handlePan(panEvent);
        
        expect(interactiveChart.panOffsetX).not.toBe(initialOffsetX);
        expect(interactiveChart.lastMouseX).toBe(150);
    });
    
    test('键盘平移应该正常工作', () => {
        const initialOffsetX = interactiveChart.panOffsetX;
        
        interactiveChart.handleKeyboard({ key: 'ArrowRight' });
        
        expect(interactiveChart.panOffsetX).toBeGreaterThan(initialOffsetX);
    });
});
```

### 性能测试

#### 1. 渲染性能测试
```javascript
describe('Performance Tests', () => {
    test('大数据量渲染性能', async () => {
        const largeDataset = generateTestData(5000);
        
        const startTime = performance.now();
        await chartManager.updateSpeedTimeSeriesChart(largeDataset);
        const endTime = performance.now();
        
        const renderTime = endTime - startTime;
        expect(renderTime).toBeLessThan(testConfig.performanceThreshold.rendering);
    });
    
    test('图表更新性能', async () => {
        chartManager.initializeCharts();
        
        const startTime = performance.now();
        await chartManager.updateAllCharts();
        const endTime = performance.now();
        
        const updateTime = endTime - startTime;
        expect(updateTime).toBeLessThan(testConfig.performanceThreshold.chartUpdate);
    });
    
    test('数据获取性能', async () => {
        const startTime = performance.now();
        await chartManager.fetchSpeedDistributionData();
        const endTime = performance.now();
        
        const fetchTime = endTime - startTime;
        expect(fetchTime).toBeLessThan(testConfig.performanceThreshold.dataFetch);
    });
});
```

#### 2. 内存泄漏测试
```javascript
describe('Memory Leak Tests', () => {
    test('图表销毁应该释放内存', () => {
        chartManager.initializeCharts();
        
        const initialChartCount = Object.keys(chartManager.charts).length;
        expect(initialChartCount).toBeGreaterThan(0);
        
        chartManager.destroyCharts();
        
        expect(Object.keys(chartManager.charts)).toHaveLength(0);
        expect(chartManager.dataUpdateInterval).toBeNull();
    });
    
    test('重复初始化不应该造成内存泄漏', () => {
        for (let i = 0; i < 10; i++) {
            chartManager.initializeCharts();
            chartManager.destroyCharts();
        }
        
        // 验证没有残留的定时器或事件监听器
        expect(chartManager.dataUpdateInterval).toBeNull();
    });
});
```

### 兼容性测试

#### 1. 浏览器兼容性测试
```javascript
describe('Browser Compatibility', () => {
    const browsers = ['Chrome', 'Firefox', 'Safari', 'Edge'];
    
    browsers.forEach(browser => {
        test(`应该在${browser}中正常工作`, async () => {
            // 使用Selenium或Puppeteer进行跨浏览器测试
            const driver = await createWebDriver(browser);
            
            try {
                await driver.get('http://localhost:8080');
                
                // 等待图表加载
                await driver.wait(until.elementLocated(By.id('speedDistributionChart')), 5000);
                
                // 验证图表是否正确渲染
                const chartElement = await driver.findElement(By.id('speedDistributionChart'));
                expect(await chartElement.isDisplayed()).toBe(true);
                
            } finally {
                await driver.quit();
            }
        });
    });
});
```

#### 2. 响应式设计测试
```javascript
describe('Responsive Design', () => {
    testConfig.viewports.forEach(viewport => {
        test(`应该在${viewport.width}x${viewport.height}分辨率下正常显示`, async () => {
            // 设置视口大小
            await page.setViewport(viewport);
            await page.goto('http://localhost:8080');
            
            // 等待图表加载
            await page.waitForSelector('#speedDistributionChart');
            
            // 截图对比
            const screenshot = await page.screenshot();
            expect(screenshot).toMatchImageSnapshot({
                threshold: 0.1,
                customSnapshotIdentifier: `viewport-${viewport.width}x${viewport.height}`
            });
        });
    });
});
```

## 🔧 系统集成测试

### 1. 端到端测试
```javascript
describe('End-to-End Tests', () => {
    test('完整的数据可视化流程', async () => {
        // 1. 启动系统
        await page.goto('http://localhost:8080');
        
        // 2. 等待数据加载
        await page.waitForSelector('.data-visualization-container');
        
        // 3. 验证图表显示
        const charts = await page.$$('.chart-container');
        expect(charts.length).toBeGreaterThan(0);
        
        // 4. 测试数据刷新
        await page.click('#refresh-charts');
        await page.waitForTimeout(1000);
        
        // 5. 验证数据更新
        const updatedData = await page.evaluate(() => {
            return window.chartManager.getLatestData();
        });
        expect(updatedData).toBeDefined();
    });
    
    test('交互式图表完整流程', async () => {
        await page.goto('http://localhost:8080');
        
        // 测试缩放
        await page.hover('#interactiveChart');
        await page.mouse.wheel({ deltaY: -100 });
        
        // 测试平移
        await page.mouse.move(100, 100);
        await page.mouse.down();
        await page.mouse.move(150, 100);
        await page.mouse.up();
        
        // 测试重置
        await page.click('#reset-view');
        
        // 验证操作结果
        const zoomLevel = await page.evaluate(() => {
            return window.interactiveChart.zoomLevel;
        });
        expect(zoomLevel).toBe(1);
    });
});
```

### 2. API集成测试
```javascript
describe('API Integration', () => {
    test('数据库查询API', async () => {
        const response = await fetch('http://localhost:8080/api/db/query', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                query: 'SELECT COUNT(*) as count FROM trajectory'
            })
        });
        
        expect(response.status).toBe(200);
        
        const data = await response.json();
        expect(data).toHaveProperty('count');
        expect(typeof data.count).toBe('number');
    });
    
    test('WebSocket连接', (done) => {
        const ws = new WebSocket('ws://localhost:8080/ws');
        
        ws.onopen = () => {
            expect(ws.readyState).toBe(WebSocket.OPEN);
            ws.close();
        };
        
        ws.onclose = () => {
            done();
        };
        
        ws.onerror = (error) => {
            done(error);
        };
    });
});
```

## 📊 测试报告和覆盖率

### 1. 测试执行
```bash
# 运行所有测试
npm test

# 运行特定测试套件
npm test -- --testNamePattern="Data Visualization"

# 生成覆盖率报告
npm run test:coverage

# 运行性能测试
npm run test:performance
```

### 2. 覆盖率要求
```javascript
// jest.config.js
module.exports = {
    collectCoverageFrom: [
        'Web/frontend/**/*.js',
        '!Web/frontend/test/**',
        '!Web/frontend/node_modules/**'
    ],
    coverageThreshold: {
        global: {
            branches: 80,
            functions: 80,
            lines: 80,
            statements: 80
        }
    }
};
```

### 3. 测试报告格式
```javascript
// 生成详细的测试报告
const testResults = {
    summary: {
        total: 45,
        passed: 43,
        failed: 2,
        skipped: 0,
        coverage: {
            lines: 85.2,
            functions: 88.7,
            branches: 82.1
        }
    },
    performance: {
        chartUpdate: '45ms (✓)',
        dataFetch: '120ms (✓)',
        rendering: '35ms (✓)'
    },
    compatibility: {
        Chrome: '✓',
        Firefox: '✓',
        Safari: '✓',
        Edge: '✓'
    }
};
```

## 🚀 持续集成测试

### 1. GitHub Actions配置
```yaml
# .github/workflows/test.yml
name: Test Suite

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Setup Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '16'
    
    - name: Install dependencies
      run: npm install
    
    - name: Run tests
      run: npm run test:ci
    
    - name: Upload coverage
      uses: codecov/codecov-action@v1
```

### 2. 自动化测试脚本
```bash
#!/bin/bash
# scripts/run-tests.sh

echo "Starting Camera_Editor test suite..."

# 启动测试服务器
npm run start:test &
SERVER_PID=$!

# 等待服务器启动
sleep 5

# 运行测试
npm run test:all

# 清理
kill $SERVER_PID

echo "Test suite completed."
```

---

**相关文档**:
- [数据可视化技术实现说明](data_visualization.md)
- [交互式速度图表技术文档](interactive_charts.md)
- [AI助手项目理解文档](../AI_PROJECT_CONTEXT.md)
