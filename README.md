# Camera_Editor - 乒乓球自动裁判系统

> **项目简介**: 基于C++20的全自动乒乓球裁判系统，集成双目视觉、AI推理和Web界面  
> **项目状态**: 生产就绪，持续优化中  
> **最后更新**: 2025-06-26

## 🎯 项目特色

- **🎥 双目高速视觉**: 210+ FPS双摄像头实时处理
- **🤖 AI智能检测**: YOLOv11 + TensorRT毫米级精度追踪
- **🌐 现代化Web界面**: 实时数据可视化和交互式分析
- **📊 数据可视化**: Chart.js集成的多维度数据分析系统
- **🎬 智能录制**: 210 FPS高质量视频录制和精彩片段自动剪辑
- **📈 性能监控**: 实时系统性能监控和数据统计

## 🚀 快速开始

### 系统要求

| 组件 | 要求 | 推荐 |
|------|------|------|
| **操作系统** | Windows 10/11 | Windows 11 |
| **开发环境** | Visual Studio 2022 | Visual Studio 2022 Community |
| **GPU** | NVIDIA GPU (支持CUDA) | RTX 3060或更高 |
| **内存** | 8GB RAM | 16GB RAM |
| **摄像头** | 双目高速摄像头 | 海康威视工业相机 |

### 环境配置

#### 1. 依赖库安装
```bash
# CUDA Toolkit
CUDA_PATH=C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.8

# OpenCV
OpenCV_DIR=C:\opencv\build

# TensorRT
TensorRT_DIR=C:\TensorRT-8.6.1.6
```

#### 2. 项目构建
```bash
# 克隆项目
git clone https://github.com/BryanXuecn/Camera_Editor.git
cd Camera_Editor

# 使用CMake生成项目文件
mkdir build && cd build
cmake .. -G "Visual Studio 17 2022" -A x64

# 编译项目
cmake --build . --config Release

# 运行程序
./Camera_Editor.exe
```

### 首次使用

1. **连接摄像头**: 确保双目摄像头正确连接并被系统识别
2. **启动程序**: 运行编译后的Camera_Editor.exe
3. **访问Web界面**: 打开浏览器访问 `http://localhost:8080`
4. **相机标定**: 点击"相机标定"按钮，使用8×11棋盘格进行自动标定
5. **开始监控**: 标定完成后即可开始实时乒乓球追踪和分析

## 📊 系统性能

| 性能指标 | 当前值 | 说明 |
|---------|--------|------|
| **处理帧率** | 210+ FPS | 双摄像头并行处理 |
| **检测精度** | 毫米级 | 三维坐标精度 |
| **AI推理频率** | 52.5 次/秒 | YOLOv11 + TensorRT |
| **数据记录频率** | 209+ 条/秒 | SQLite异步写入 |
| **端到端延迟** | < 50ms | 实时响应 |
| **录制能力** | 210 FPS | H.264 NVENC硬件加速 |

## 🏗️ 系统架构

### 核心设计原则
- **服务化解耦**: 所有服务通过SharedData数据总线通信
- **多线程并行**: 每个服务运行在独立线程中
- **CMake管理**: CMake是唯一的项目文件管理来源

### 主要组件
```
📁 项目结构
├── Main/                   # 应用程序入口和生命周期管理
├── Services/               # 核心服务层
│   ├── CameraService       # 相机硬件控制
│   ├── InferenceService    # AI推理服务
│   ├── WebServerService    # Web服务器
│   ├── RecordingService    # 视频录制服务
│   └── DataLoggingService  # 数据持久化服务
├── Utils/                  # 工具和数据总线
├── Deploy/                 # AI模型部署
├── Web/                    # 前端界面
└── Data/                   # 业务数据
```

## 🔧 开发指南

### 添加新功能
1. **遵循服务化架构**: 新功能应作为独立服务实现
2. **使用SharedData通信**: 禁止服务间直接调用
3. **更新CMakeLists.txt**: 添加新文件到CMake配置
4. **使用绝对路径**: 避免相对路径导致的文件找不到问题

### 代码规范
- **命名约定**: 遵循现有的命名规范
- **线程安全**: 所有跨线程操作必须通过SharedData
- **错误处理**: 添加适当的异常处理和日志记录
- **性能优化**: 避免不必要的数据拷贝，合理使用cv::Mat

## 📚 文档导航

### 核心文档
- **[AI助手项目理解文档](docs/AI_PROJECT_CONTEXT.md)** - AI助手和开发者的完整项目上下文
- **[开发进度管理文档](docs/开发进度管理文档.md)** - 项目开发状态和历史记录

### 技术文档
- **[数据可视化技术实现说明](docs/technical/data_visualization.md)** - 可视化系统技术细节
- **[交互式速度图表技术文档](docs/technical/interactive_charts.md)** - 交互式组件技术文档
- **[测试指南合集](docs/technical/testing_guides.md)** - 完整的测试方法和标准

### 快速参考
- **技术栈**: C++20 + CMake + TensorRT + OpenCV + Crow + SQLite
- **架构模式**: 服务化设计 + SharedData数据总线
- **开发环境**: Visual Studio 2022 + Windows 10/11 + NVIDIA GPU

## 🐛 常见问题

### 编译问题
- **CMake配置错误**: 确保所有依赖库路径正确配置
- **CUDA版本不匹配**: 检查CUDA版本与TensorRT兼容性
- **OpenCV链接错误**: 验证OpenCV库路径和版本

### 运行时问题
- **相机连接失败**: 检查相机驱动和USB连接
- **模型加载失败**: 确认TensorRT引擎文件路径正确
- **Web界面无法访问**: 检查防火墙设置和端口占用

### 性能问题
- **帧率低**: 检查GPU使用率和内存占用
- **延迟高**: 优化数据处理流程和线程调度
- **内存泄漏**: 检查cv::Mat使用和资源释放

## 🤝 贡献指南

1. **Fork项目**: 创建项目的分支
2. **创建功能分支**: `git checkout -b feature/新功能名称`
3. **提交更改**: `git commit -am '添加新功能'`
4. **推送分支**: `git push origin feature/新功能名称`
5. **创建Pull Request**: 提交代码审查请求

### 代码审查要点
- [ ] 遵循服务化架构原则
- [ ] 通过SharedData进行服务间通信
- [ ] 使用绝对路径配置
- [ ] 添加适当的错误处理
- [ ] 包含必要的测试用例

## 📞 联系方式

- **项目维护者**: Bryan Xue
- **邮箱**: <EMAIL>
- **GitHub**: https://github.com/BryanXuecn/Camera_Editor

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

---

**⭐ 如果这个项目对您有帮助，请给我们一个Star！**
