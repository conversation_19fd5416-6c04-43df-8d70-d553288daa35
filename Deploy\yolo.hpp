#pragma once
#include "Deploy/model.hpp"
#include "Deploy/option.hpp"
#include "Deploy/result.hpp"

#include <opencv2/opencv.hpp>

const static char* kInputTensorName = "images";
const static char* kOutputTensorName = "output";

// 注意：相机ID常量已移至CameraService类中管理
// #define idLeftCamera  "*********"
// #define idRightCamera "*********"

const static int kNumClass = 80;
const static int kBatchSize = 1;

//const static int kInputH = 700;
//const static int kInputW = 700;
//const static int kInputH = 960;
//const static int kInputW = 960;
const static int kInputH = 900;
const static int kInputW = 900;

const static float kNmsThresh = 0.45f;
const static float kConfThresh = 0.9f;

const static int kMaxInputImageSize = 3000 * 3000;
const static int kMaxNumOutputBbox = 1000;

class Yolo {
    std::unique_ptr<deploy::DetectModel> model;
    std::vector<std::string> labels;

    // Private constructor for cloning, takes a unique_ptr from the model's clone() method
    Yolo(std::unique_ptr<deploy::DetectModel> model, const std::vector<std::string>& labels);

public:
    class Detection {
    public:
        float conf = 0;
        float left = 0;
        float top = 0;
        float right = 0; 
        float bottom = 0;
        int class_id = 0;
        float bbox[4] = {0};
        Detection(float conf = 0, float left = 0, float top = 0, float right = 0, float bottom = 0);
        cv::Point2f center() const;
    };
    
    Yolo(std::string modelPath, std::string class_file);
    ~Yolo();

    // Clone method
    std::unique_ptr<Yolo> clone() const;

    std::map<std::string, std::vector<Detection>> inference(cv::Mat img);
    std::map<std::string, std::vector<Detection>> inference(cv::Mat img, float confThreshold);

    cv::Mat drawBox(cv::Mat img, std::map<std::string, std::vector<Detection>>* pData);
};