<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据可视化测试</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Chart.js 库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.1/dist/chart.umd.js"></script>
</head>
<body>
    <div class="container">
        <!-- 头部导航 -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-chart-bar"></i>
                    <h1>数据可视化测试</h1>
                </div>
                <div class="status-indicator">
                    <div class="status-dot online"></div>
                    <span>测试模式</span>
                </div>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 数据可视化仪表板 -->
            <section class="data-visualization-dashboard">
                <div class="panel-header">
                    <h2><i class="fas fa-chart-bar"></i> 数据可视化分析</h2>
                    <div class="visualization-controls">
                        <button class="btn btn-small" id="refreshChartsBtn"><i class="fas fa-sync-alt"></i> 刷新图表</button>
                        <button class="btn btn-small" id="exportDataBtn"><i class="fas fa-download"></i> 导出数据</button>
                    </div>
                </div>
                <div class="visualization-grid">
                    <!-- 球速分布图 -->
                    <div class="chart-container">
                        <div class="chart-header">
                            <h3><i class="fas fa-tachometer-alt"></i> 球速分布</h3>
                            <div class="chart-status" id="speedChartStatus">测试模式</div>
                        </div>
                        <div class="chart-wrapper">
                            <canvas id="speedDistributionChart"></canvas>
                        </div>
                    </div>
                    
                    <!-- 时间序列图 -->
                    <div class="chart-container">
                        <div class="chart-header">
                            <h3><i class="fas fa-chart-line"></i> 速度时间序列</h3>
                            <div class="chart-controls">
                                <select id="timeRangeSelect" class="chart-select">
                                    <option value="60">最近1分钟</option>
                                    <option value="300">最近5分钟</option>
                                    <option value="1800">最近30分钟</option>
                                    <option value="3600">最近1小时</option>
                                </select>
                            </div>
                        </div>
                        <div class="chart-wrapper">
                            <canvas id="speedTimeSeriesChart"></canvas>
                        </div>
                    </div>
                    
                    <!-- 运动轨迹热力图 -->
                    <div class="chart-container">
                        <div class="chart-header">
                            <h3><i class="fas fa-fire"></i> 运动热力图</h3>
                            <div class="chart-status" id="heatmapStatus">测试模式</div>
                        </div>
                        <div class="chart-wrapper">
                            <canvas id="trajectoryHeatmapChart"></canvas>
                        </div>
                    </div>
                    
                    <!-- 统计摘要 -->
                    <div class="chart-container">
                        <div class="chart-header">
                            <h3><i class="fas fa-calculator"></i> 统计摘要</h3>
                            <div class="chart-status" id="statsStatus">测试模式</div>
                        </div>
                        <div class="stats-wrapper">
                            <div class="stat-item">
                                <div class="stat-label">平均速度</div>
                                <div class="stat-value" id="avgSpeed">12.5 m/s</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-label">最大速度</div>
                                <div class="stat-value" id="maxSpeed">28.3 m/s</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-label">数据点数</div>
                                <div class="stat-value" id="dataPointCount">1,234</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-label">活跃时长</div>
                                <div class="stat-value" id="activeDuration">15 分钟</div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- 底部信息 -->
        <footer class="footer">
            <div class="footer-content">
                <div class="footer-info">
                    <p><i class="fas fa-flask"></i> 数据可视化功能测试页面</p>
                    <p><i class="fas fa-clock"></i> 测试时间: <span id="testTime">--</span></p>
                </div>
            </div>
        </footer>
    </div>

    <!-- 通知弹窗 -->
    <div id="notification-container"></div>

    <script>
        // 简化的测试脚本
        class VisualizationTest {
            constructor() {
                this.charts = {};
                this.init();
            }

            init() {
                console.log('🧪 初始化数据可视化测试');
                this.updateTestTime();
                this.initTestCharts();
                this.setupTestHandlers();
            }

            updateTestTime() {
                const testTimeElement = document.getElementById('testTime');
                if (testTimeElement) {
                    testTimeElement.textContent = new Date().toLocaleTimeString();
                }
            }

            initTestCharts() {
                this.initSpeedDistributionChart();
                this.initSpeedTimeSeriesChart();
                this.initTrajectoryHeatmapChart();
            }

            initSpeedDistributionChart() {
                const canvas = document.getElementById('speedDistributionChart');
                if (!canvas) return;

                const ctx = canvas.getContext('2d');
                
                // 模拟数据
                const testData = [15, 25, 35, 20, 8, 3];

                this.charts.speedDistribution = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: ['0-5', '5-10', '10-15', '15-20', '20-25', '25+'],
                        datasets: [{
                            label: '球速分布 (m/s)',
                            data: testData,
                            backgroundColor: 'rgba(0, 212, 255, 0.6)',
                            borderColor: 'rgba(0, 212, 255, 1)',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                labels: {
                                    color: '#ffffff'
                                }
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    color: '#a0a0a0'
                                },
                                grid: {
                                    color: 'rgba(0, 212, 255, 0.1)'
                                }
                            },
                            x: {
                                ticks: {
                                    color: '#a0a0a0'
                                },
                                grid: {
                                    color: 'rgba(0, 212, 255, 0.1)'
                                }
                            }
                        }
                    }
                });
            }

            initSpeedTimeSeriesChart() {
                const canvas = document.getElementById('speedTimeSeriesChart');
                if (!canvas) return;

                const ctx = canvas.getContext('2d');
                
                // 生成模拟时间序列数据
                const now = new Date();
                const labels = [];
                const data = [];
                
                for (let i = 20; i >= 0; i--) {
                    const time = new Date(now.getTime() - i * 15000); // 每15秒一个点
                    labels.push(time.toLocaleTimeString());
                    data.push(Math.random() * 25 + 5); // 5-30 m/s 随机速度
                }

                this.charts.speedTimeSeries = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: '球速 (m/s)',
                            data: data,
                            borderColor: 'rgba(0, 255, 170, 1)',
                            backgroundColor: 'rgba(0, 255, 170, 0.1)',
                            borderWidth: 2,
                            fill: true,
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                labels: {
                                    color: '#ffffff'
                                }
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    color: '#a0a0a0'
                                },
                                grid: {
                                    color: 'rgba(0, 255, 170, 0.1)'
                                }
                            },
                            x: {
                                ticks: {
                                    color: '#a0a0a0',
                                    maxTicksLimit: 10
                                },
                                grid: {
                                    color: 'rgba(0, 255, 170, 0.1)'
                                }
                            }
                        }
                    }
                });
            }

            initTrajectoryHeatmapChart() {
                const canvas = document.getElementById('trajectoryHeatmapChart');
                if (!canvas) return;

                const ctx = canvas.getContext('2d');
                
                // 生成模拟轨迹数据
                const scatterData = [];
                for (let i = 0; i < 100; i++) {
                    scatterData.push({
                        x: Math.random() * 2.74, // 球桌长度
                        y: Math.random() * 1.525  // 球桌宽度
                    });
                }

                this.charts.trajectoryHeatmap = new Chart(ctx, {
                    type: 'scatter',
                    data: {
                        datasets: [{
                            label: '轨迹点',
                            data: scatterData,
                            backgroundColor: 'rgba(255, 107, 53, 0.6)',
                            borderColor: 'rgba(255, 107, 53, 1)',
                            pointRadius: 3
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                labels: {
                                    color: '#ffffff'
                                }
                            }
                        },
                        scales: {
                            y: {
                                title: {
                                    display: true,
                                    text: 'Y 坐标 (m)',
                                    color: '#a0a0a0'
                                },
                                ticks: {
                                    color: '#a0a0a0'
                                },
                                grid: {
                                    color: 'rgba(255, 107, 53, 0.1)'
                                }
                            },
                            x: {
                                title: {
                                    display: true,
                                    text: 'X 坐标 (m)',
                                    color: '#a0a0a0'
                                },
                                ticks: {
                                    color: '#a0a0a0'
                                },
                                grid: {
                                    color: 'rgba(255, 107, 53, 0.1)'
                                }
                            }
                        }
                    }
                });
            }

            setupTestHandlers() {
                const refreshBtn = document.getElementById('refreshChartsBtn');
                if (refreshBtn) {
                    refreshBtn.addEventListener('click', () => {
                        console.log('🔄 刷新测试图表');
                        this.refreshTestCharts();
                    });
                }

                const exportBtn = document.getElementById('exportDataBtn');
                if (exportBtn) {
                    exportBtn.addEventListener('click', () => {
                        console.log('📥 测试数据导出');
                        this.showNotification('测试模式：数据导出功能正常', 'success');
                    });
                }
            }

            refreshTestCharts() {
                // 更新图表数据
                Object.values(this.charts).forEach(chart => {
                    if (chart && chart.update) {
                        chart.update();
                    }
                });
                this.showNotification('图表刷新完成', 'success');
            }

            showNotification(message, type = 'info') {
                console.log(`📢 ${type.toUpperCase()}: ${message}`);
                // 简单的通知实现
                const container = document.getElementById('notification-container');
                if (container) {
                    const notification = document.createElement('div');
                    notification.className = `notification ${type}`;
                    notification.textContent = message;
                    notification.style.cssText = `
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        padding: 12px 20px;
                        background: rgba(0, 212, 255, 0.9);
                        color: white;
                        border-radius: 4px;
                        z-index: 1000;
                        animation: slideIn 0.3s ease;
                    `;
                    container.appendChild(notification);
                    
                    setTimeout(() => {
                        notification.remove();
                    }, 3000);
                }
            }
        }

        // 启动测试
        document.addEventListener('DOMContentLoaded', () => {
            new VisualizationTest();
        });
    </script>
</body>
</html>
