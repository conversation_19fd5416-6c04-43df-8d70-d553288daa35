#include "Deploy/yolo.hpp"
#include <filesystem>
#include <fstream>
namespace fs = std::filesystem;
// ���ļ������ɱ�ǩ
std::vector<std::string> generate_labels(const std::string& label_file) {
    std::ifstream file(label_file);
    if (!file.is_open()) {
        throw std::runtime_error("Failed to open labels file: " + label_file);
    }

    std::vector<std::string> labels;
    std::string              label;
    while (std::getline(file, label)) {
        labels.emplace_back(label);
    }
    return labels;
}
Yolo::Yolo(std::string engine_path, std::string label_path)
    :model()
{
    if (!fs::exists(engine_path)) {
        throw std::runtime_error("Engine path does not exist: " + engine_path);
    }
    if (!fs::exists(label_path)) {
        throw std::runtime_error("Label path does not exist: " + label_path);
    }

    labels = generate_labels(label_path);

    deploy::InferOption option;
    option.enableSwapRB();
    // option.setNormalizeParams({0.485, 0.456, 0.406}, {0.229, 0.224, 0.225}); // PP-YOLOE��PP-YOLOE+

    // if (!fs::is_regular_file(input_path)) {
    //     option.enablePerformanceReport();
    // }
    model = std::make_unique<deploy::DetectModel>(engine_path, option);

}

// Private constructor for cloning
Yolo::Yolo(std::unique_ptr<deploy::DetectModel> model, const std::vector<std::string>& labels)
    : model(std::move(model)), labels(labels) {}

// Clone method implementation
std::unique_ptr<Yolo> Yolo::clone() const {
    return std::unique_ptr<Yolo>(new Yolo(model->clone(), this->labels));
}

Yolo::~Yolo()
{
}

std::map<std::string, std::vector<Yolo::Detection>> Yolo::inference(cv::Mat img) {
    return inference(img, kConfThresh);
}

std::map<std::string, std::vector<Yolo::Detection>> Yolo::inference(cv::Mat img, float confThreshold) {
    std::map<std::string, std::vector<Yolo::Detection>> ret;
    deploy::Image image(img.data, img.cols, img.rows);
    auto start = std::chrono::steady_clock::now();
    
    // 使用predict方法而不是infer方法
    auto result = model->predict(image);
    
    auto end = std::chrono::steady_clock::now();
    double duration = std::chrono::duration<double, std::milli>(end - start).count();
    //std::cout << "YOLO Inference Time: " << duration << " ms" << std::endl;

    for (int i = 0; i < result.num; i++) {
        int cls = result.classes[i];
        if (cls >= this->labels.size() || cls < 0) continue;
        auto box = result.boxes[i];
        float score = result.scores[i];
        const auto& label = this->labels[cls];
        if (score > confThreshold) {
            Yolo::Detection detection(score, box.left, box.top, box.right, box.bottom);
            detection.class_id = cls; // 设置class_id
            ret[label].push_back(detection);
        }
    }

    return ret;
}

cv::Mat Yolo::drawBox(cv::Mat img, std::map<std::string, std::vector<Detection>>* pData)
{
    static int cnt = 0;
    cv::Mat imgShow;
    img.copyTo(imgShow);
    for (auto it = pData->begin(); it != pData->end(); it++)
    {
        std::string name = it->first;
        for (auto iit = it->second.begin(); iit != it->second.end(); iit++)
        {
            cv::Rect rect = cv::Rect(iit->left, iit->top, iit->right - iit->left, iit->bottom - iit->top);
            cv::circle(imgShow, cv::Point(int((iit->right + iit->left) / 2 ), int((iit->bottom + iit->top) / 2 )), 2, cv::Scalar(255, 255, 255));

            cv::rectangle(imgShow, rect, cv::Scalar(0xFF, 0xFF, 0), 2);
            cv::putText(imgShow, name + std::to_string(iit->conf), cv::Point(rect.x, rect.y - 1), cv::FONT_HERSHEY_PLAIN, 1.2, cv::Scalar(0xFF, 0xFF, 0), 2);
#if SAVE_DETECT_IMG==1
            //cv::imwrite(std::string("../Data/pic/detect/") + std::to_string(cnt) + std::string(".jpg"), imgShow(rect));
#endif
            cnt++;
            //cv::resize(imgShow, imgShow, cv::Size(1000, 1000));
        }

    }

    return imgShow;
}

Yolo::Detection::Detection(float conf, float left, float top, float right, float bottom)
    : conf(conf), left(left), top(top), right(right), bottom(bottom) {
    // 初始化bbox数组，与left, top, right, bottom对应
    bbox[0] = left;
    bbox[1] = top;
    bbox[2] = right;
    bbox[3] = bottom;
}

cv::Point2f Yolo::Detection::center() const
{
    return cv::Point2f((this->right+this->left)/2,(this->bottom+this->top)/2);
}
