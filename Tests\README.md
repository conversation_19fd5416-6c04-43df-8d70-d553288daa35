# 录制功能测试套件

本测试套件用于验证Camera_Editor项目中视频录制功能的帧重复bug修复效果。

## 🎯 测试目标

1. **验证帧重复bug修复**：确保录制的视频不再出现每两帧重复的问题
2. **验证210 FPS录制**：确认系统能够稳定录制210 FPS视频
3. **验证双摄像头同步**：确保左右摄像头帧同步满足三维重建需求（1-2帧误差）

## 📁 文件说明

### 测试程序
- `recording_test.cpp` - C++录制功能测试程序
- `CMakeLists.txt` - 测试程序编译配置
- `verify_fix.py` - Python视频分析脚本

### 使用方法

#### 1. 编译测试程序

```bash
# 在Tests目录下
mkdir build
cd build
cmake ..
make
```

#### 2. 运行录制测试

```bash
# 运行C++测试程序
./bin/recording_test
```

测试程序将：
- 模拟210 FPS的帧推送
- 启动双摄像头录制
- 测试5秒钟的录制过程
- 分析双摄像头同步性能
- 生成测试录制文件

#### 3. 验证修复效果

```bash
# 使用Python脚本分析录制文件
python verify_fix.py --video-dir "C:/Dev/Camera_Editor/Data/recordings" --latest

# 或者分析所有录制文件
python verify_fix.py --video-dir "C:/Dev/Camera_Editor/Data/recordings"

# 调整相似度阈值
python verify_fix.py --threshold 0.98 --latest
```

## 📊 测试结果解读

### 帧重复检测结果

- **重复率 < 5%**：✅ 修复效果良好
- **重复率 5-15%**：⚠️ 部分改善，需要进一步优化
- **重复率 > 15%**：❌ 修复效果不佳，需要重新检查

### 双摄像头同步结果

- **最大时间差 ≤ 10ms**：✅ 同步优秀
- **最大时间差 10-20ms**：⚠️ 同步良好
- **最大时间差 > 20ms**：❌ 同步需要改进

### 录制帧率结果

- **实际帧率 ≥ 200 FPS**：✅ 210 FPS录制成功
- **实际帧率 180-200 FPS**：⚠️ 接近目标，可接受
- **实际帧率 < 180 FPS**：❌ 性能不足，需要优化

## 🔧 故障排除

### 常见问题

1. **编译错误**
   - 确保OpenCV已正确安装
   - 检查CMake配置路径
   - 确认C++20支持

2. **录制文件未生成**
   - 检查录制目录权限
   - 确认FFmpeg可用
   - 检查硬件编码器支持

3. **Python脚本错误**
   - 安装必要依赖：`pip install opencv-python numpy`
   - 确认视频文件路径正确

### 性能调优建议

1. **如果录制帧率不足**：
   - 检查存储设备性能（建议SSD）
   - 调整FFmpeg编码参数
   - 确认GPU硬件编码可用

2. **如果同步性能不佳**：
   - 调整MAX_SYNC_TOLERANCE参数
   - 检查系统时钟精度
   - 优化线程调度

3. **如果仍有帧重复**：
   - 检查时间戳精度
   - 调整帧选择算法
   - 验证队列处理逻辑

## 📈 性能基准

### 预期性能指标

| 指标 | 目标值 | 最低要求 |
|------|--------|----------|
| 录制帧率 | 210 FPS | 200 FPS |
| 帧重复率 | < 2% | < 5% |
| 双摄同步误差 | < 5ms | < 10ms |
| CPU使用率 | < 50% | < 70% |
| 内存使用 | < 2GB | < 4GB |

### 测试环境要求

- **CPU**: Intel i7或AMD Ryzen 7以上
- **GPU**: 支持NVENC的NVIDIA显卡
- **内存**: 16GB以上
- **存储**: SSD，写入速度 > 500MB/s
- **操作系统**: Windows 10/11

## 🚀 自动化测试

可以将测试集成到CI/CD流程中：

```bash
# 自动化测试脚本示例
#!/bin/bash
echo "开始录制功能自动化测试..."

# 编译测试程序
cd Tests && mkdir -p build && cd build
cmake .. && make

# 运行测试
./bin/recording_test

# 验证结果
cd .. && python verify_fix.py --latest

echo "测试完成！"
```

## 📝 测试报告

测试完成后，建议记录以下信息：

1. **测试环境**：硬件配置、软件版本
2. **测试结果**：各项性能指标
3. **问题发现**：异常情况和错误日志
4. **改进建议**：性能优化方向

---

**注意**：首次运行测试前，请确保摄像头硬件已正确连接并初始化。
