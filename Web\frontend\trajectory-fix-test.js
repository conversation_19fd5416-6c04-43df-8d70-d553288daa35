/**
 * 轨迹修复测试脚本
 * 用于验证高亮圆点卡顿问题的修复效果
 */

// 测试配置
const TEST_CONFIG = {
    // 模拟球的移动和停止
    ballMovement: {
        movePhase: 2000,    // 移动阶段持续时间（毫秒）
        staticPhase: 3000,  // 静止阶段持续时间（毫秒）
        moveSpeed: 0.1,     // 移动速度
        centerX: 320,       // 中心X坐标
        centerY: 180,       // 中心Y坐标
        radius: 100         // 移动半径
    },
    
    // 测试场景
    scenarios: [
        {
            name: "连续移动测试",
            description: "球连续移动，验证轨迹正常显示和淡出",
            duration: 5000,
            ballStatic: false
        },
        {
            name: "移动后停止测试", 
            description: "球移动后停止，验证静止点显示",
            duration: 8000,
            ballStatic: true,
            staticDelay: 3000
        },
        {
            name: "间歇性移动测试",
            description: "球间歇性移动和停止，验证状态切换",
            duration: 10000,
            ballStatic: true,
            staticDelay: 2000,
            moveInterval: 1000
        }
    ]
};

class TrajectoryFixTester {
    constructor() {
        this.isRunning = false;
        this.currentScenario = null;
        this.testStartTime = 0;
        this.ballPosition = { x: 320, y: 180 };
        this.ballVelocity = { x: 0, y: 0 };
        this.isMoving = true;
        this.ballExists = true;
        this.lastMoveTime = 0;
        this.lastBallsDataLength = 0;
        
        this.setupTestUI();
        this.bindEvents();
    }
    
    setupTestUI() {
        // 创建测试控制面板
        const testPanel = document.createElement('div');
        testPanel.id = 'trajectory-test-panel';
        testPanel.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            width: 300px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            z-index: 10000;
            border: 1px solid #333;
        `;
        
        testPanel.innerHTML = `
            <h3 style="margin: 0 0 10px 0; color: #00ff88;">轨迹修复测试</h3>
            <div id="test-status">等待测试...</div>
            <div id="test-progress" style="margin: 10px 0;"></div>
            <div id="ball-status" style="margin: 10px 0;"></div>
            <div style="margin-top: 15px;">
                <button id="start-test-btn" style="margin-right: 5px;">开始测试</button>
                <button id="stop-test-btn">停止测试</button>
            </div>
            <div style="margin-top: 10px;">
                <select id="scenario-select" style="width: 100%;">
                    ${TEST_CONFIG.scenarios.map((s, i) => 
                        `<option value="${i}">${s.name}</option>`
                    ).join('')}
                </select>
            </div>
            <div id="test-log" style="margin-top: 10px; max-height: 200px; overflow-y: auto; background: #111; padding: 5px; border-radius: 3px;"></div>
        `;
        
        document.body.appendChild(testPanel);
    }
    
    bindEvents() {
        document.getElementById('start-test-btn').onclick = () => this.startTest();
        document.getElementById('stop-test-btn').onclick = () => this.stopTest();
        
        // 添加调试快捷键
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 't') {
                e.preventDefault();
                this.toggleDebugMode();
            }
        });
    }
    
    startTest() {
        if (this.isRunning) return;
        
        const scenarioIndex = parseInt(document.getElementById('scenario-select').value);
        this.currentScenario = TEST_CONFIG.scenarios[scenarioIndex];
        
        this.log(`开始测试: ${this.currentScenario.name}`);
        this.log(`描述: ${this.currentScenario.description}`);
        
        this.isRunning = true;
        this.testStartTime = Date.now();
        this.lastMoveTime = this.testStartTime;
        
        // 重置球的状态
        this.ballPosition = {
            x: TEST_CONFIG.ballMovement.centerX,
            y: TEST_CONFIG.ballMovement.centerY
        };
        this.isMoving = true;
        this.ballExists = true;
        
        this.runTestLoop();
    }
    
    stopTest() {
        this.isRunning = false;
        this.log('测试已停止');
        this.updateStatus('测试已停止');
    }
    
    runTestLoop() {
        if (!this.isRunning) return;
        
        const currentTime = Date.now();
        const elapsed = currentTime - this.testStartTime;
        
        // 检查测试是否完成
        if (elapsed >= this.currentScenario.duration) {
            this.completeTest();
            return;
        }
        
        // 更新球的状态和位置
        this.updateBallState(currentTime, elapsed);
        
        // 模拟发送轨迹数据
        this.simulateTrajectoryUpdate();
        
        // 更新UI
        this.updateTestUI(elapsed);
        
        // 继续测试循环
        requestAnimationFrame(() => this.runTestLoop());
    }
    
    updateBallState(currentTime, elapsed) {
        const scenario = this.currentScenario;
        
        if (scenario.name === "连续移动测试") {
            // 连续移动
            this.isMoving = true;
            this.updateBallPosition(currentTime);
            
        } else if (scenario.name === "移动后停止测试") {
            // 移动一段时间后停止，然后模拟球消失
            if (elapsed < scenario.staticDelay) {
                this.isMoving = true;
                this.updateBallPosition(currentTime);
            } else if (elapsed < scenario.staticDelay + 3000) {
                // 停止3秒，显示静止点
                this.isMoving = false;
            } else {
                // 3秒后模拟球消失
                this.isMoving = false;
                this.ballExists = false; // 标记球消失
            }
            
        } else if (scenario.name === "间歇性移动测试") {
            // 间歇性移动
            const cycleTime = scenario.staticDelay + scenario.moveInterval;
            const cyclePosition = elapsed % cycleTime;
            
            if (cyclePosition < scenario.moveInterval) {
                this.isMoving = true;
                this.updateBallPosition(currentTime);
            } else {
                this.isMoving = false;
            }
        }
    }
    
    updateBallPosition(currentTime) {
        if (!this.isMoving) return;
        
        const config = TEST_CONFIG.ballMovement;
        const angle = (currentTime - this.testStartTime) * config.moveSpeed * 0.001;
        
        this.ballPosition.x = config.centerX + Math.cos(angle) * config.radius;
        this.ballPosition.y = config.centerY + Math.sin(angle) * config.radius;
    }
    
    simulateTrajectoryUpdate() {
        // 模拟3D坐标更新
        const ball3D = {
            id: 1,
            x: (this.ballPosition.x - 320) * 0.01, // 转换为米
            y: (this.ballPosition.y - 180) * 0.01,
            z: 0.1,
            confidence: 0.9,
            timestamp: Date.now()
        };

        // 如果存在SimpleCameraDisplay实例，更新其状态
        if (window.app && window.app.update3DCoordinates) {
            // 根据球的状态发送数据：球存在时发送球数据，消失时发送空数组
            const ballsData = (this.ballExists !== false) ? [ball3D] : [];
            window.app.update3DCoordinates({
                balls: ballsData
            });

            // 记录状态变化
            if (ballsData.length === 0 && this.lastBallsDataLength > 0) {
                this.log(`🎾 模拟球消失`);
            } else if (ballsData.length > 0 && this.lastBallsDataLength === 0) {
                this.log(`🎾 模拟球出现`);
            }
            this.lastBallsDataLength = ballsData.length;
        }

        // 模拟2D轨迹更新（仅在移动时）
        if (this.isMoving && window.app && window.app.handleTrajectory2DUpdate) {
            window.app.handleTrajectory2DUpdate({
                camera_id: 1,
                trajectory: [{ x: this.ballPosition.x, y: this.ballPosition.y }],
                original_width: 640,
                original_height: 360
            });

            window.app.handleTrajectory2DUpdate({
                camera_id: 2,
                trajectory: [{ x: this.ballPosition.x + 20, y: this.ballPosition.y + 10 }],
                original_width: 640,
                original_height: 360
            });
        }
    }
    
    updateTestUI(elapsed) {
        const progress = (elapsed / this.currentScenario.duration * 100).toFixed(1);
        
        this.updateStatus(`运行中: ${this.currentScenario.name}`);
        document.getElementById('test-progress').textContent = `进度: ${progress}%`;

        let ballStatusText;
        if (this.ballExists === false) {
            ballStatusText = '球状态: 消失 | 位置: N/A';
        } else {
            ballStatusText = `球状态: ${this.isMoving ? '移动' : '静止'} | 位置: (${this.ballPosition.x.toFixed(1)}, ${this.ballPosition.y.toFixed(1)})`;
        }
        document.getElementById('ball-status').textContent = ballStatusText;
    }
    
    completeTest() {
        this.isRunning = false;
        this.log(`测试完成: ${this.currentScenario.name}`);
        this.updateStatus('测试完成');
        
        // 输出轨迹状态报告
        if (window.app && window.app.logTrajectoryStatus) {
            this.log('=== 最终轨迹状态 ===');
            window.app.logTrajectoryStatus();
        }
    }
    
    toggleDebugMode() {
        if (window.app && window.app.logTrajectoryStatus) {
            window.app.logTrajectoryStatus();
        }
    }
    
    log(message) {
        const logContainer = document.getElementById('test-log');
        const timestamp = new Date().toLocaleTimeString();
        logContainer.innerHTML += `<div>[${timestamp}] ${message}</div>`;
        logContainer.scrollTop = logContainer.scrollHeight;
    }
    
    updateStatus(status) {
        document.getElementById('test-status').textContent = status;
    }
}

// 自动初始化测试器
document.addEventListener('DOMContentLoaded', () => {
    // 等待主应用加载完成
    setTimeout(() => {
        window.trajectoryTester = new TrajectoryFixTester();
        console.log('轨迹修复测试器已加载。按 Ctrl+T 查看轨迹状态。');
    }, 1000);
});

// 导出测试器类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TrajectoryFixTester;
}
