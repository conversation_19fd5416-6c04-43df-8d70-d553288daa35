# 🎯 动态ROI机制技术实现指南

> **文档用途**: 指导Camera_Editor项目中动态ROI功能的开发实现，解决快球检测问题
> **创建日期**: 2025-07-02
> **技术目标**: 提高AI推理效率，解决推理2794次但3D重建仅24次的问题

##  技术方案概述

### 问题分析
- **当前问题**: AI推理2794次，3D重建仅24次，成功率0.86%
- **根本原因**: 全画面AI推理效率低，快球检测时左右摄像头匹配失败
- **解决思路**: 通过动态ROI减少无效推理，集中计算资源于球可能出现的区域

### 核心原理
```
传统方式: 全画面推理 → 大量无效检测 → 立体匹配失败
动态ROI: 预测区域 → 精准推理 → 提高匹配成功率
```

## 🔧 核心算法实现

### 1. 简化预测算法

#### 基于速度矢量的线性预测
```cpp
// Utils/DynamicROIPredictor.hpp
class DynamicROIPredictor {
private:
    struct PredictionState {
        cv::Point3f position;      // 当前3D位置
        cv::Point3f velocity;      // 速度矢量
        std::chrono::high_resolution_clock::time_point timestamp;
    };

    std::deque<PredictionState> m_history;
    const size_t MAX_HISTORY = 5;  // 保持最近5个状态

public:
    // 简单线性预测
    cv::Point3f predictNextPosition(double dt) {
        if (m_history.size() < 2) return cv::Point3f(0, 0, 0);

        auto& current = m_history.back();

        // 线性预测：P_next = P_current + V * dt
        cv::Point3f predicted = current.position + current.velocity * dt;

        // 考虑重力影响（乒乓球抛物线轨迹）
        predicted.z -= 0.5f * 9.81f * dt * dt;  // 重力加速度

        return predicted;
    }

    // 更新历史状态
    void updateState(const cv::Point3f& position, double dt) {
        auto now = std::chrono::high_resolution_clock::now();

        PredictionState state;
        state.position = position;
        state.timestamp = now;

        // 计算速度矢量
        if (!m_history.empty()) {
            auto& prev = m_history.back();
            state.velocity = (position - prev.position) / dt;
        }

        m_history.push_back(state);
        if (m_history.size() > MAX_HISTORY) {
            m_history.pop_front();
        }
    }
};
```

### 2. 多级ROI策略

#### ROI层级定义
```cpp
// Utils/ROIManager.hpp
struct ROILevels {
    cv::Rect search_roi;    // 搜索区域 (400x400像素)
    cv::Rect coarse_roi;    // 粗检测区域 (200x200像素)  
    cv::Rect fine_roi;      // 精检测区域 (100x100像素)
    float confidence_threshold_coarse = 0.3f;
    float confidence_threshold_fine = 0.6f;
};

class ROIManager {
public:
    // 生成多级ROI
    ROILevels generateROILevels(const cv::Point2f& predicted_center, 
                               const cv::Size& frame_size) {
        ROILevels levels;
        
        // 确保ROI在画面范围内
        auto clampROI = [&](cv::Rect& roi) {
            roi.x = std::max(0, std::min(roi.x, frame_size.width - roi.width));
            roi.y = std::max(0, std::min(roi.y, frame_size.height - roi.height));
        };
        
        // 搜索区域（球丢失时使用）
        levels.search_roi = cv::Rect(predicted_center.x - 200, predicted_center.y - 200, 400, 400);
        clampROI(levels.search_roi);
        
        // 粗检测区域
        levels.coarse_roi = cv::Rect(predicted_center.x - 100, predicted_center.y - 100, 200, 200);
        clampROI(levels.coarse_roi);
        
        // 精检测区域
        levels.fine_roi = cv::Rect(predicted_center.x - 50, predicted_center.y - 50, 100, 100);
        clampROI(levels.fine_roi);
        
        return levels;
    }
};
```

### 3. 复用现有DualEye几何转换（优化架构）

#### 利用现有StereoReconstructionService的几何能力
```cpp
// Services/StereoReconstructionService.hpp 扩展ROI功能
class StereoReconstructionService {
private:
    // 现有成员保持不变
    std::unique_ptr<DUE::C_DualEye> m_dualEye;
    std::unique_ptr<DynamicROIPredictor> m_roiPredictor;  // 新增

public:
    // 新增：复用现有几何转换能力的ROI生成方法
    cv::Point2f worldToPixelForROI(const MU::Point3f& world_point, int camera_id) {
        // 直接复用DualEye的现有转换能力
        // DualEye已经包含完整的相机参数和转换逻辑

        // 创建临时的像素数据用于转换
        std::vector<cv::Point2f> left_points, right_points;
        left_points.push_back(cv::Point2f(0, 0));   // 占位符
        right_points.push_back(cv::Point2f(0, 0));  // 占位符

        // 使用DualEye的逆向转换能力
        // 注意：这里需要DualEye提供世界坐标到像素坐标的转换
        // 如果DualEye没有直接提供，我们可以使用其内部的相机参数

        if (camera_id == 1) {
            // 左摄像头：使用DualEye的左相机参数
            return projectWorldToLeftCamera(world_point);
        } else {
            // 右摄像头：使用DualEye的右相机参数
            return projectWorldToRightCamera(world_point);
        }
    }

    // 新增：为特定摄像头生成ROI（复用现有架构）
    cv::Rect generateROIForCamera(const MU::Point3f& predicted_3d_pos,
                                 int camera_id,
                                 int roi_size = 150) {
        // 使用现有的几何转换能力
        cv::Point2f pixel_center = worldToPixelForROI(predicted_3d_pos, camera_id);

        // 生成ROI矩形
        cv::Rect roi(
            static_cast<int>(pixel_center.x - roi_size/2),
            static_cast<int>(pixel_center.y - roi_size/2),
            roi_size, roi_size
        );

        // 边界检查（复用现有的图像尺寸信息）
        roi = clampROIToImageBounds(roi);

        return roi;
    }

    // 新增：双目ROI预测和分发（扩展现有速度计算逻辑）
    void updateROIPredictions(const BallPosition3D& latest_position) {
        if (!m_roiPredictor) return;

        // 更新轨迹预测
        double dt = 0.01;  // 10ms间隔
        m_roiPredictor->updateState(latest_position.world_position, dt);

        // 预测下一帧3D位置
        auto predicted_3d = m_roiPredictor->predictNextPosition(dt);

        // 转换为MU::Point3f格式（与现有代码兼容）
        MU::Point3f predicted_mu_point(predicted_3d.x, predicted_3d.y, predicted_3d.z);

        // 为左右摄像头生成ROI
        cv::Rect left_roi = generateROIForCamera(predicted_mu_point, 1);
        cv::Rect right_roi = generateROIForCamera(predicted_mu_point, 2);

        // 验证ROI有效性（使用现有的边界检查逻辑）
        bool left_valid = isROIValid(left_roi, 1);
        bool right_valid = isROIValid(right_roi, 2);

        // 发布到SharedData
        if (left_valid) {
            m_sharedData->setROIPrediction(1, left_roi, 0.8f);
        }
        if (right_valid) {
            m_sharedData->setROIPrediction(2, right_roi, 0.8f);
        }

        // 更新立体匹配状态
        m_sharedData->setStereoMatchable(left_valid && right_valid);
    }

private:
    // 辅助方法：使用DualEye内部参数进行投影
    cv::Point2f projectWorldToLeftCamera(const MU::Point3f& world_point) {
        // 访问DualEye的左相机参数
        // 注意：可能需要在DualEye中添加getter方法
        return cv::Point2f(0, 0);  // 占位符，需要具体实现
    }

    cv::Point2f projectWorldToRightCamera(const MU::Point3f& world_point) {
        // 访问DualEye的右相机参数
        return cv::Point2f(0, 0);  // 占位符，需要具体实现
    }

    cv::Rect clampROIToImageBounds(const cv::Rect& roi) {
        // 复用现有的图像尺寸信息
        const cv::Size image_size(1440, 1080);  // 从DualEye获取

        cv::Rect clamped = roi;
        clamped.x = std::max(0, std::min(clamped.x, image_size.width - clamped.width));
        clamped.y = std::max(0, std::min(clamped.y, image_size.height - clamped.height));

        return clamped;
    }

    bool isROIValid(const cv::Rect& roi, int camera_id) {
        // 基本有效性检查
        return roi.area() > 100 && roi.x >= 0 && roi.y >= 0;
    }
};
```

## 🎯 **优化架构设计总结**

### 核心设计原则
1. **最大化复用现有代码**：充分利用DualEye的几何转换能力，避免重复实现
2. **最小化新增组件**：仅添加必要的ROI预测器，其他功能通过扩展现有服务实现
3. **渐进式集成**：通过扩展现有StereoReconstructionService，降低集成风险
4. **保持架构一致性**：遵循Camera_Editor的服务化架构和SharedData通信模式

### 关键技术决策
```cpp
// 决策1：复用DualEye而非重新实现几何转换
❌ 原方案：创建StereoGeometryHandler重新实现坐标转换
✅ 优化方案：扩展DualEye添加worldToLeftPixel/worldToRightPixel接口

// 决策2：扩展现有服务而非创建新服务
❌ 原方案：创建独立的ROI管理服务
✅ 优化方案：在StereoReconstructionService中添加ROI预测功能

// 决策3：简化可见性处理
❌ 原方案：复杂的StereoVisibilityHandler类
✅ 优化方案：使用DualEye的isPointVisibleInCamera方法

// 决策4：最小化文件数量
❌ 原方案：6个新增文件
✅ 优化方案：1个新增文件 + 4个现有文件修改
```

### 实现优先级
| 优先级 | 实现内容 | 预计时间 | 依赖关系 |
|--------|----------|----------|----------|
| **P1** | DualEye接口扩展 | 1天 | 无依赖 |
| **P2** | DynamicROIPredictor实现 | 1天 | 无依赖 |
| **P3** | StereoReconstructionService扩展 | 1天 | 依赖P1、P2 |
| **P4** | InferenceService ROI集成 | 1天 | 依赖P3 |
| **P5** | 测试和调优 | 1-2天 | 依赖P1-P4 |

## 🔍 现有DualEye系统接口分析

### 可直接复用的DualEye接口

#### 1. 现有DualEye类结构分析
```cpp
// Camera/dualEye.hpp - 现有接口分析
class C_DualEye {
public:
    // ✅ 已有：3D世界坐标计算
    MU::Point3f calP3inWorld(C_PixData data);
    std::vector<MU::Point3f> calP3inWorld(std::vector<cv::Point2f> left, std::vector<cv::Point2f> right);

    // ✅ 已有：立体匹配
    std::vector<C_PixData> classifyMultiple(...);

    // ❌ 缺失：世界坐标到像素坐标的逆向转换
    // ❌ 缺失：相机参数的公开访问接口

private:
    // 🔒 私有成员（需要添加getter方法）
    cv::Mat cameraMatrixLeft, cameraMatrixRight;
    cv::Mat distCoeffsLeft, distCoeffsRight;
    cv::Mat Rl, Rr, Pl, Pr, Q;  // 立体校正参数
    cv::Mat Rcw, Tcw;           // 外参
};
```

#### 2. 需要在DualEye中新增的接口
```cpp
// Camera/dualEye.hpp - 建议新增的公开接口
class C_DualEye {
public:
    // 新增：世界坐标到像素坐标转换（ROI核心需求）
    cv::Point2f worldToLeftPixel(const MU::Point3f& world_point);
    cv::Point2f worldToRightPixel(const MU::Point3f& world_point);

    // 新增：相机参数访问器（用于ROI计算）
    const cv::Mat& getCameraMatrixLeft() const { return cameraMatrixLeft; }
    const cv::Mat& getCameraMatrixRight() const { return cameraMatrixRight; }
    const cv::Mat& getDistCoeffsLeft() const { return distCoeffsLeft; }
    const cv::Mat& getDistCoeffsRight() const { return distCoeffsRight; }

    // 新增：图像尺寸访问器
    cv::Size getImageSize() const { return imageSize; }

    // 新增：验证点是否在相机视野内
    bool isPointVisibleInLeftCamera(const MU::Point3f& world_point);
    bool isPointVisibleInRightCamera(const MU::Point3f& world_point);
};
```

#### 3. DualEye扩展实现（最小化修改）
```cpp
// Camera/dualEye.cpp - 新增方法实现
cv::Point2f DUE::C_DualEye::worldToLeftPixel(const MU::Point3f& world_point) {
    // 1. 世界坐标转换为相机坐标
    cv::Mat world_mat = (cv::Mat_<double>(3, 1) << world_point.x, world_point.y, world_point.z);
    cv::Mat camera_point = this->Rcw * world_mat + this->Tcw;

    // 2. 投影到左相机图像平面
    std::vector<cv::Point3f> object_points = {
        cv::Point3f(camera_point.at<double>(0), camera_point.at<double>(1), camera_point.at<double>(2))
    };
    std::vector<cv::Point2f> image_points;

    cv::projectPoints(object_points,
                     this->Rl,                    // 左相机旋转
                     cv::Mat::zeros(3, 1, CV_64F), // 零平移（已在Rcw/Tcw中处理）
                     this->cameraMatrixLeft,       // 左相机内参
                     this->distCoeffsLeft,         // 左相机畸变
                     image_points);

    return image_points[0];
}

cv::Point2f DUE::C_DualEye::worldToRightPixel(const MU::Point3f& world_point) {
    // 类似实现，使用右相机参数
    cv::Mat world_mat = (cv::Mat_<double>(3, 1) << world_point.x, world_point.y, world_point.z);
    cv::Mat camera_point = this->Rcw * world_mat + this->Tcw;

    std::vector<cv::Point3f> object_points = {
        cv::Point3f(camera_point.at<double>(0), camera_point.at<double>(1), camera_point.at<double>(2))
    };
    std::vector<cv::Point2f> image_points;

    cv::projectPoints(object_points,
                     this->Rr,                    // 右相机旋转
                     cv::Mat::zeros(3, 1, CV_64F),
                     this->cameraMatrixRight,     // 右相机内参
                     this->distCoeffsRight,       // 右相机畸变
                     image_points);

    return image_points[0];
}

bool DUE::C_DualEye::isPointVisibleInLeftCamera(const MU::Point3f& world_point) {
    cv::Point2f pixel = worldToLeftPixel(world_point);
    return (pixel.x >= 0 && pixel.x < imageSize.width &&
            pixel.y >= 0 && pixel.y < imageSize.height);
}

bool DUE::C_DualEye::isPointVisibleInRightCamera(const MU::Point3f& world_point) {
    cv::Point2f pixel = worldToRightPixel(world_point);
    return (pixel.x >= 0 && pixel.x < imageSize.width &&
            pixel.y >= 0 && pixel.y < imageSize.height);
}
```

#### 3. 立体匹配有效性保证
```cpp
// 确保左右ROI都能支持有效的立体匹配
class StereoMatchingValidator {
public:
    bool validateROIForStereoMatching(const cv::Rect& left_roi,
                                     const cv::Rect& right_roi,
                                     const cv::Point3f& world_point) {
        // 1. 检查ROI大小是否合理
        if (left_roi.area() < 100 || right_roi.area() < 100) {
            return false;  // ROI太小，无法有效检测
        }

        // 2. 检查ROI是否包含预测的球位置
        cv::Point2f left_projection = m_geometryHandler->worldToPixel(world_point, 1);
        cv::Point2f right_projection = m_geometryHandler->worldToPixel(world_point, 2);

        bool left_contains = left_roi.contains(left_projection);
        bool right_contains = right_roi.contains(right_projection);

        // 3. 检查对极约束是否满足
        float y_diff = std::abs(left_projection.y - right_projection.y);
        const float MAX_Y_DIFF = 30.0f;  // 与现有阈值保持一致

        bool epipolar_valid = (y_diff <= MAX_Y_DIFF);

        return left_contains && right_contains && epipolar_valid;
    }

    // 自适应ROI大小调整
    std::pair<cv::Rect, cv::Rect> adjustROIForStereoMatching(
        const cv::Point3f& world_point,
        const cv::Size& frame_size) {

        cv::Point2f left_pixel = m_geometryHandler->worldToPixel(world_point, 1);
        cv::Point2f right_pixel = m_geometryHandler->worldToPixel(world_point, 2);

        // 基于对极约束调整ROI大小
        float y_diff = std::abs(left_pixel.y - right_pixel.y);
        int adaptive_roi_size = 100 + static_cast<int>(y_diff * 2);  // 动态调整
        adaptive_roi_size = std::min(adaptive_roi_size, 200);  // 限制最大尺寸

        cv::Rect left_roi = generateROI(left_pixel, adaptive_roi_size, frame_size);
        cv::Rect right_roi = generateROI(right_pixel, adaptive_roi_size, frame_size);

        return {left_roi, right_roi};
    }
};
```

## 🏗️ 系统集成方案

### 1. SharedData扩展

```cpp
// Utils/SharedData.hpp 新增双目视觉ROI接口
class SharedData {
public:
    // 双目ROI预测数据
    struct StereoROIData {
        cv::Rect left_roi;
        cv::Rect right_roi;
        float left_confidence;
        float right_confidence;
        bool stereo_matchable;  // 是否可进行立体匹配
        std::chrono::high_resolution_clock::time_point timestamp;
    };

    // ROI预测接口
    void setROIPrediction(int camera_id, const cv::Rect& roi, float confidence);
    cv::Rect getLatestROI(int camera_id);

    // 双目ROI专用接口
    void setStereoROI(const StereoROIData& stereo_roi);
    StereoROIData getLatestStereoROI() const;

    // 立体匹配状态
    void setStereoMatchable(bool matchable);
    bool isStereoMatchable() const;

    // ROI处理模式
    void setROIProcessingMode(bool enabled);
    bool isROIProcessingEnabled() const;

    // ROI有效性验证
    bool isROIValid(int camera_id) const;

private:
    mutable std::mutex m_roiMutex;
    std::map<int, cv::Rect> m_predictedROIs;
    std::map<int, float> m_roiConfidences;
    StereoROIData m_latestStereoROI;
    std::atomic<bool> m_roiEnabled{false};
    std::atomic<bool> m_stereoMatchable{true};
};
```

### 2. InferenceService集成

```cpp
// Services/InferenceService.cpp 修改推理逻辑
class InferenceService {
private:
    std::unique_ptr<ROIManager> m_roiManager;
    std::unique_ptr<DynamicROIPredictor> m_roiPredictor;

public:
    // 支持ROI的推理方法
    std::map<std::string, std::vector<Yolo::Detection>>
    inferenceWithROI(const cv::Mat& frame, int camera_id) {

        // 检查是否启用ROI模式
        if (!m_sharedData->isROIProcessingEnabled()) {
            return m_yolo->inference(frame);  // 传统全画面推理
        }

        // 获取预测的ROI
        cv::Rect predicted_roi = m_sharedData->getLatestROI(camera_id);

        if (predicted_roi.area() > 0) {
            // ROI推理
            cv::Mat roi_frame = frame(predicted_roi);
            auto detections = m_yolo->inference(roi_frame, 0.3f);  // 降低阈值

            // 将ROI坐标转换回全画面坐标
            adjustDetectionCoordinates(detections, predicted_roi);
            return detections;
        }

        return m_yolo->inference(frame);  // 回退到全画面推理
    }

    // 坐标转换方法（将ROI内的检测结果转换为全画面坐标）
    void adjustDetectionCoordinates(std::map<std::string, std::vector<Yolo::Detection>>& detections,
                                   const cv::Rect& roi) {
        for (auto& [class_name, detection_list] : detections) {
            for (auto& detection : detection_list) {
                // 将ROI内的坐标转换为全画面坐标
                detection.left += roi.x;
                detection.top += roi.y;
                detection.right += roi.x;
                detection.bottom += roi.y;
            }
        }
    }
};
```

### 3. StereoReconstructionService集成

```cpp
// Services/StereoReconstructionService.cpp - 扩展现有服务
class StereoReconstructionService {
private:
    // 现有成员保持不变
    std::unique_ptr<DUE::C_DualEye> m_dualEye;

    // 新增：仅添加必要的ROI预测器
    std::unique_ptr<DynamicROIPredictor> m_roiPredictor;
    bool m_roiEnabled = false;

public:
    // 新增：ROI功能初始化
    void initializeROIFeatures() {
        m_roiPredictor = std::make_unique<DynamicROIPredictor>();
        m_roiEnabled = true;
        DEBUG_ROI("ROI预测功能已启用");
    }

    // 修改：扩展现有的速度计算方法
    void calculateAndStoreSpeed(const BallPosition3D& latest_position) {
        // === 原有速度计算逻辑保持不变 ===
        // ... 现有的SG滤波器速度计算代码 ...

        // === 新增：ROI预测逻辑 ===
        if (m_roiEnabled && m_roiPredictor) {
            updateROIPredictionsUsingDualEye(latest_position);
        }
    }

private:
    // 新增：使用DualEye接口的ROI预测方法
    void updateROIPredictionsUsingDualEye(const BallPosition3D& latest_position) {
        double dt = 0.01;  // 10ms间隔

        // 更新轨迹预测
        cv::Point3f cv_position(latest_position.world_position.x,
                               latest_position.world_position.y,
                               latest_position.world_position.z);
        m_roiPredictor->updateState(cv_position, dt);

        // 预测下一帧3D位置
        cv::Point3f predicted_3d = m_roiPredictor->predictNextPosition(dt);

        // 转换为MU::Point3f（与DualEye接口兼容）
        MU::Point3f predicted_mu(predicted_3d.x, predicted_3d.y, predicted_3d.z);

        // 使用DualEye的现有projectWorldPoint方法
        auto projected_points = m_dualEye->projectWorldPoint(predicted_mu);
        cv::Point2f left_pixel = projected_points.first;
        cv::Point2f right_pixel = projected_points.second;

        // 检查可见性
        cv::Size image_size = m_dualEye->imageSize;
        bool left_visible = (left_pixel.x >= 0 && left_pixel.x < image_size.width &&
                            left_pixel.y >= 0 && left_pixel.y < image_size.height);
        bool right_visible = (right_pixel.x >= 0 && right_pixel.x < image_size.width &&
                             right_pixel.y >= 0 && right_pixel.y < image_size.height);

        // 生成左摄像头ROI
        if (left_visible) {
            cv::Rect left_roi = generateROIAroundPoint(left_pixel, 150);
            m_sharedData->setROIPrediction(1, left_roi, 0.8f);
            DEBUG_ROI("左摄像头ROI: " + roiToString(left_roi));
        } else {
            m_sharedData->setROIPrediction(1, cv::Rect(), 0.0f);
            DEBUG_ROI("左摄像头：球不可见，使用全画面搜索");
        }

        // 生成右摄像头ROI
        if (right_visible) {
            cv::Rect right_roi = generateROIAroundPoint(right_pixel, 150);
            m_sharedData->setROIPrediction(2, right_roi, 0.8f);
            DEBUG_ROI("右摄像头ROI: " + roiToString(right_roi));
        } else {
            m_sharedData->setROIPrediction(2, cv::Rect(), 0.0f);
            DEBUG_ROI("右摄像头：球不可见，使用全画面搜索");
        }

        // 更新立体匹配状态
        bool stereo_matchable = left_visible && right_visible;
        m_sharedData->setStereoMatchable(stereo_matchable);

        // 验证对极约束（复用现有的匹配阈值）
        if (stereo_matchable) {
            float y_diff = std::abs(left_pixel.y - right_pixel.y);
            if (y_diff > m_matchingThreshold) {
                DEBUG_ROI("警告：预测点对极约束验证失败，Y差异: " + std::to_string(y_diff));
            }
        }
    }

    // 辅助方法：在指定点周围生成ROI
    cv::Rect generateROIAroundPoint(const cv::Point2f& center, int size) {
        cv::Size image_size = m_dualEye->imageSize;

        cv::Rect roi(
            static_cast<int>(center.x - size/2),
            static_cast<int>(center.y - size/2),
            size, size
        );

        // 边界裁剪
        roi.x = std::max(0, std::min(roi.x, image_size.width - roi.width));
        roi.y = std::max(0, std::min(roi.y, image_size.height - roi.height));

        return roi;
    }

    std::string roiToString(const cv::Rect& roi) {
        return "(" + std::to_string(roi.x) + "," + std::to_string(roi.y) +
               "," + std::to_string(roi.width) + "x" + std::to_string(roi.height) + ")";
    }
};
```

## ⚡ 性能优化策略

### 1. 内存管理优化
```cpp
// ROI图像缓存池
class ROIImagePool {
private:
    std::queue<cv::Mat> m_pool;
    std::mutex m_poolMutex;
    
public:
    cv::Mat getReuseableROI(const cv::Size& size) {
        std::lock_guard<std::mutex> lock(m_poolMutex);
        if (!m_pool.empty()) {
            auto roi = m_pool.front();
            m_pool.pop();
            if (roi.size() == size) return roi;
        }
        return cv::Mat(size, CV_8UC3);
    }
    
    void returnROI(cv::Mat& roi) {
        std::lock_guard<std::mutex> lock(m_poolMutex);
        if (m_pool.size() < 10) {  // 限制池大小
            m_pool.push(roi);
        }
    }
};
```

### 2. 并行处理策略
```cpp
// 双摄像头ROI并行处理
void processROIParallel() {
    std::vector<std::future<DetectionResult>> futures;
    
    for (int camera_id = 1; camera_id <= 2; ++camera_id) {
        futures.push_back(std::async(std::launch::async, [this, camera_id]() {
            auto frame = m_sharedData->getLatestFrame(camera_id);
            return inferenceWithROI(frame, camera_id);
        }));
    }
    
    // 等待所有结果
    for (auto& future : futures) {
        auto result = future.get();
        // 处理结果...
    }
}
```

## 🧪 测试验证方法

### 1. 功能测试方案
```cpp
// 双目视觉ROI测试用例
class StereoROIFunctionalTest {
public:
    // 测试1：双目几何转换精度
    void testStereoGeometryAccuracy() {
        // 使用已知3D点验证左右摄像头投影精度
        cv::Point3f test_point(0.5f, 1.0f, 0.2f);  // 球桌中央上方

        cv::Point2f left_pixel = m_geometryHandler->worldToPixel(test_point, 1);
        cv::Point2f right_pixel = m_geometryHandler->worldToPixel(test_point, 2);

        // 验证对极约束
        float y_diff = std::abs(left_pixel.y - right_pixel.y);
        assert(y_diff < 20.0f);  // Y坐标差异应小于20像素

        DEBUG_TEST("几何转换测试通过：Y差异 = " + std::to_string(y_diff));
    }

    // 测试2：ROI覆盖准确性
    void testROICoverageAccuracy() {
        // 验证生成的ROI是否准确覆盖预测位置
        cv::Point3f ball_pos(0.3f, 0.8f, 0.15f);

        cv::Rect left_roi = m_geometryHandler->generateCameraSpecificROI(
            ball_pos, 1, cv::Size(1440, 1080), 150);
        cv::Rect right_roi = m_geometryHandler->generateCameraSpecificROI(
            ball_pos, 2, cv::Size(1440, 1080), 150);

        // 验证ROI包含预测点
        cv::Point2f left_projection = m_geometryHandler->worldToPixel(ball_pos, 1);
        cv::Point2f right_projection = m_geometryHandler->worldToPixel(ball_pos, 2);

        assert(left_roi.contains(left_projection));
        assert(right_roi.contains(right_projection));

        DEBUG_TEST("ROI覆盖测试通过");
    }

    // 测试3：边界可见性处理
    void testBoundaryVisibilityHandling() {
        // 测试球接近画面边界时的处理
        cv::Point3f boundary_pos(-0.1f, 1.0f, 0.2f);  // 接近左边界
        cv::Point3f velocity(0.5f, 0.0f, 0.0f);       // 向右运动

        auto visibility = m_visibilityHandler->analyzeVisibility(
            boundary_pos, velocity, cv::Size(1440, 1080));

        // 验证可见性分析结果
        DEBUG_TEST("边界可见性：左=" + std::to_string(visibility.left_visible) +
                  " 右=" + std::to_string(visibility.right_visible));
    }

    // 测试4：立体匹配一致性
    void testStereoMatchingConsistency() {
        // 验证左右ROI的立体匹配有效性
        cv::Point3f test_points[] = {
            {0.2f, 0.5f, 0.1f},   // 近距离
            {0.8f, 2.0f, 0.3f},   // 远距离
            {0.0f, 1.0f, 0.2f}    // 边界位置
        };

        for (auto& point : test_points) {
            cv::Rect left_roi = m_geometryHandler->generateCameraSpecificROI(
                point, 1, cv::Size(1440, 1080));
            cv::Rect right_roi = m_geometryHandler->generateCameraSpecificROI(
                point, 2, cv::Size(1440, 1080));

            bool consistent = m_geometryHandler->validateStereoROIConsistency(
                left_roi, right_roi, point);

            DEBUG_TEST("立体匹配一致性测试：" + std::to_string(consistent));
        }
    }
};
```

### 2. 性能测试指标
| 测试项目 | 当前值 | 目标值 | 验证方法 |
|---------|--------|--------|----------|
| **AI推理效率** | 2794次推理 | 减少50% | 统计推理调用次数 |
| **3D重建成功率** | 0.86% | >5% | 统计重建成功次数 |
| **快球检测率** | 低 | >70% | 快球场景测试 |
| **系统延迟** | 110-215ms | <100ms | 时间戳分析 |
| **双目ROI精度** | N/A | <10像素误差 | 几何转换精度测试 |
| **立体匹配成功率** | 低 | >80% | ROI一致性验证 |
| **边界处理准确性** | N/A | >90% | 边界场景测试 |

### 3. 双目视觉专项测试
```cpp
// 双目ROI性能监控
class StereoROIPerformanceMonitor {
private:
    struct PerformanceMetrics {
        int total_predictions = 0;
        int successful_left_roi = 0;
        int successful_right_roi = 0;
        int stereo_matching_success = 0;
        double avg_geometry_conversion_time = 0.0;
        double avg_roi_generation_time = 0.0;
    };

    PerformanceMetrics m_metrics;

public:
    void recordROIPrediction(bool left_success, bool right_success,
                           bool stereo_success, double conversion_time,
                           double generation_time) {
        m_metrics.total_predictions++;
        if (left_success) m_metrics.successful_left_roi++;
        if (right_success) m_metrics.successful_right_roi++;
        if (stereo_success) m_metrics.stereo_matching_success++;

        // 移动平均
        double alpha = 0.1;
        m_metrics.avg_geometry_conversion_time =
            alpha * conversion_time + (1.0 - alpha) * m_metrics.avg_geometry_conversion_time;
        m_metrics.avg_roi_generation_time =
            alpha * generation_time + (1.0 - alpha) * m_metrics.avg_roi_generation_time;
    }

    void printPerformanceReport() {
        double left_success_rate = (double)m_metrics.successful_left_roi / m_metrics.total_predictions * 100;
        double right_success_rate = (double)m_metrics.successful_right_roi / m_metrics.total_predictions * 100;
        double stereo_success_rate = (double)m_metrics.stereo_matching_success / m_metrics.total_predictions * 100;

        DEBUG_TEST("=== 双目ROI性能报告 ===");
        DEBUG_TEST("总预测次数: " + std::to_string(m_metrics.total_predictions));
        DEBUG_TEST("左摄像头ROI成功率: " + std::to_string(left_success_rate) + "%");
        DEBUG_TEST("右摄像头ROI成功率: " + std::to_string(right_success_rate) + "%");
        DEBUG_TEST("立体匹配成功率: " + std::to_string(stereo_success_rate) + "%");
        DEBUG_TEST("平均几何转换时间: " + std::to_string(m_metrics.avg_geometry_conversion_time) + "ms");
        DEBUG_TEST("平均ROI生成时间: " + std::to_string(m_metrics.avg_roi_generation_time) + "ms");
    }
};
```

## ⚠️ 风险评估和回滚方案

### 主要风险点
1. **ROI预测不准确**: 可能导致AI检测丢失
2. **3D重建失败**: 立体匹配可能失败
3. **球速检测中断**: 速度计算可能中断

### 回滚方案
```cpp
// 安全开关机制
class ROISafetySwitch {
private:
    std::atomic<bool> m_emergencyDisable{false};
    int m_failureCount = 0;

public:
    bool shouldUseROI() {
        if (m_emergencyDisable.load()) return false;
        if (m_failureCount > 10) {
            m_emergencyDisable.store(true);
            UTF8Utils::println("⚠️ ROI功能已自动禁用，切换到全画面AI推理");
            return false;
        }
        return true;
    }

    void reportFailure() {
        m_failureCount++;
    }

    void reset() {
        m_failureCount = 0;
        m_emergencyDisable.store(false);
        UTF8Utils::println("✅ ROI功能已重新启用");
    }
};

// 在InferenceService中的安全机制
InferenceResult InferenceService::processFrameWithRaw(const cv::Mat& frame, float conf_threshold) {
    if (roi_safety_switch_->shouldUseROI()) {
        try {
            // 尝试使用ROI推理
            return processWithROI(frame, conf_threshold);
        } catch (const std::exception& e) {
            roi_safety_switch_->reportFailure();
            UTF8Utils::println("⚠️ ROI推理失败，回退到全画面推理: " + std::string(e.what()));
        }
    }

    // 全画面推理
    return processFullFrame(frame, conf_threshold);
}
```

## 📁 实现文件清单

### 新增文件
- `Utils/DynamicROIPredictor.hpp/cpp` - **轨迹预测算法**

### 主要修改文件
- `Camera/dualEye.hpp/cpp` - **复用现有坐标转换功能**
  ```cpp
  // 现有方法已足够，无需新增接口
  std::pair<cv::Point2f, cv::Point2f> projectWorldPoint(const MU::Point3f& worldPoint);
  cv::Size imageSize;  // 现有成员
  ```

- `Services/StereoReconstructionService.hpp/cpp` - **扩展ROI预测功能**
  ```cpp
  // 新增成员
  std::unique_ptr<DynamicROIPredictor> m_roiPredictor;
  bool m_roiEnabled = false;

  // 新增方法
  void initializeROIFeatures();
  void updateROIPredictionsUsingDualEye(const BallPosition3D& latest_position);
  cv::Rect generateROIAroundPoint(const cv::Point2f& center, int size);
  ```

- `Utils/SharedData.hpp` - **添加ROI数据接口**
  ```cpp
  // 新增ROI相关接口
  void setROIPrediction(int camera_id, const cv::Rect& roi, float confidence);
  cv::Rect getLatestROI(int camera_id);
  void setStereoMatchable(bool matchable);
  bool isStereoMatchable() const;
  ```

- `Services/InferenceService.hpp/cpp` - **集成ROI推理**
  ```cpp
  // 修改推理方法支持ROI
  InferenceResult processFrameWithRaw(const cv::Mat& frame, float conf_threshold);
  void adjustDetectionCoordinates(std::map<std::string, std::vector<Yolo::Detection>>& detections, const cv::Rect& roi);
  ```

- `CMakeLists.txt` - **添加DynamicROIPredictor到构建系统**

### 架构依赖关系
```
AI推理数据流（ROI优化）：
StereoReconstructionService (扩展现有服务)
    ├── DualEye (复用现有几何转换能力)
    ├── DynamicROIPredictor (新增轨迹预测)
    └── SharedData (复用现有数据总线)

InferenceService (最小修改，使用ROI优化AI推理)
    └── SharedData (获取ROI数据)
```

### 架构优化效果
| 优化项目 | 原方案 | 优化后方案 | 改进效果 |
|---------|--------|------------|----------|
| **新增文件数量** | 6个新类 | 1个新类 | 减少83% |
| **代码重复** | 重新实现几何转换 | 复用DualEye能力 | 避免重复 |
| **维护复杂度** | 多个独立组件 | 扩展现有服务 | 显著降低 |
| **集成风险** | 高（新架构） | 低（渐进式扩展） | 风险可控 |

## 📊 预期性能提升

### 核心指标改进
| 指标 | 当前值 | 目标值 | 预期改进 |
|------|--------|--------|----------|
| **AI推理效率** | 2794次推理 | <1500次推理 | 减少46% |
| **3D重建成功率** | 0.86% (24/2794) | >5% (>75/1500) | 提升6倍 |
| **快球检测率** | 低 | >70% | 显著提升 |
| **系统延迟** | 110-215ms | <100ms | 减少10-50% |

### 实施优先级
| 优先级 | 实施内容 | 预计时间 | 关键改进 |
|--------|----------|----------|----------|
| **P1** | DynamicROIPredictor实现 | 1天 | 核心预测算法 |
| **P2** | StereoReconstructionService扩展 | 1天 | 最小化架构变更 |
| **P3** | InferenceService ROI集成 | 1天 | 渐进式集成 |
| **P4** | 安全机制和性能监控 | 1天 | 系统稳定性 |
| **P5** | 测试和调优 | 1-2天 | 验证和优化 |

---

**🎯 技术目标**: 通过动态ROI优化AI推理效率，显著提升3D重建成功率
**📅 预计完成**: 4-6天开发周期
**📞 技术支持**: <EMAIL>
