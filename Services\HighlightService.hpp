#pragma once

#include <memory>
#include <string>
#include <vector>
#include <mutex>
#include <thread>
#include <atomic>
#include <chrono>
#include <opencv2/opencv.hpp>
#include "../Utils/SharedData.hpp"

/**
 * @brief 智能精彩片段自动剪辑服务
 * 
 * 功能特性：
 * 1. 单摄像头录制（左摄像头优先）
 * 2. 实时球速监控和精彩时刻标记
 * 3. 基于时间戳的精确视频剪辑
 * 4. 多个精彩片段自动合并
 * 5. FFmpeg硬件加速处理
 */
class HighlightService {
public:
    /**
     * @brief 录制状态枚举
     */
    enum class RecordingState {
        IDLE,           // 空闲状态
        RECORDING,      // 录制中
        PROCESSING,     // 处理中（剪辑和合并）
        COMPLETED,      // 完成
        FAILED          // 错误状态
    };

    /**
     * @brief 精彩时刻数据结构
     */
    struct HighlightMoment {
        std::chrono::steady_clock::time_point absolute_time;    // 绝对时间
        std::chrono::milliseconds relative_time;                // 相对录制开始时间
        double ball_speed;                                      // 球速 (m/s)
        cv::Point3f ball_position;                             // 球的3D位置
        int frame_number;                                      // 对应的帧号

        HighlightMoment(std::chrono::steady_clock::time_point abs_time,
                       std::chrono::milliseconds rel_time,
                       double speed,
                       const cv::Point3f& position,
                       int frame_num)
            : absolute_time(abs_time), relative_time(rel_time), 
              ball_speed(speed), ball_position(position), frame_number(frame_num) {}
    };

    /**
     * @brief 精彩片段数据结构
     */
    struct HighlightClip {
        std::chrono::milliseconds start_time;                  // 片段开始时间
        std::chrono::milliseconds end_time;                    // 片段结束时间
        std::vector<HighlightMoment> moments;                  // 包含的精彩时刻
        std::string output_filename;                           // 输出文件名
        double max_speed;                                      // 最大球速

        HighlightClip() : max_speed(0.0) {}
    };

public:
    /**
     * @brief 构造函数
     * @param shared_data 共享数据总线
     */
    explicit HighlightService(std::shared_ptr<SharedData> shared_data);
    
    /**
     * @brief 析构函数
     */
    ~HighlightService();

    // === 核心控制接口 ===
    
    /**
     * @brief 开始精彩录制
     * @param speed_threshold 球速阈值 (m/s)
     * @param camera_id 录制的摄像头ID (默认1-左摄像头)
     * @return 是否成功开始录制
     */
    bool startHighlightRecording(double speed_threshold = 15.0, int camera_id = 1);
    
    /**
     * @brief 停止录制并生成精彩片段
     * @return 是否成功停止并开始处理
     */
    bool stopHighlightRecording();

    // === 状态查询接口 ===
    
    /**
     * @brief 获取当前录制状态
     */
    RecordingState getRecordingState() const { return recording_state_; }
    
    /**
     * @brief 获取当前录制时长（秒）
     */
    double getCurrentRecordingDuration() const;
    
    /**
     * @brief 获取检测到的精彩时刻数量
     */
    size_t getHighlightMomentsCount() const;
    
    /**
     * @brief 获取生成的片段数量
     */
    size_t getGeneratedClipsCount() const;

    // === 参数配置接口 ===
    
    /**
     * @brief 设置球速阈值
     * @param threshold 阈值 (m/s)，范围 [5.0, 50.0]
     */
    void setSpeedThreshold(double threshold);
    
    /**
     * @brief 设置片段时长参数
     * @param before_seconds 精彩时刻前的时长
     * @param after_seconds 精彩时刻后的时长
     */
    void setClipDuration(double before_seconds, double after_seconds);
    


    // === 错误处理接口 ===
    
    /**
     * @brief 是否有错误
     */
    bool hasError() const { return !error_message_.empty(); }
    
    /**
     * @brief 获取错误信息
     */
    std::string getErrorMessage() const { return error_message_; }
    
    /**
     * @brief 清除错误状态
     */
    void clearError() { error_message_.clear(); }

private:
    // === 核心线程函数 ===
    void monitoringThreadMain();       // 监控线程
    void processingThreadMain();       // 处理线程

    // === 录制控制相关 ===
    bool startRecordingViaMessage();
    bool stopRecordingViaMessage();
    void waitForRecordingResponse(const std::string& request_id, RecordingStatus expected_status);

    // === 监控相关 ===
    void checkForHighlightMoment();
    bool isValidHighlightMoment(double speed, const cv::Point3f& position);

    // === 处理相关 ===
    std::vector<HighlightClip> generateClips();
    std::vector<HighlightClip> mergeOverlappingClips(const std::vector<HighlightClip>& clips);
    bool createClipVideo(const HighlightClip& clip);
    bool createMetadataFile(const HighlightClip& clip);
    bool createFinalMergedVideo(const std::vector<HighlightClip>& clips);

    // === 工具函数 ===
    std::string formatTimestamp() const;
    std::string generateOutputFilename(const HighlightClip& clip) const;
    void setError(const std::string& error);

private:
    // === 核心组件 ===
    std::shared_ptr<SharedData> shared_data_;
    
    // === 线程管理 ===
    std::unique_ptr<std::thread> monitoring_thread_;
    std::unique_ptr<std::thread> processing_thread_;
    std::atomic<bool> should_stop_;

    // === 状态管理 ===
    std::atomic<RecordingState> recording_state_;
    std::string error_message_;
    mutable std::mutex error_mutex_;

    // === 录制参数 ===
    int camera_id_;                    // 录制的摄像头ID
    double speed_threshold_;           // 球速阈值

    // === 录制控制 ===
    std::string current_request_id_;   // 当前录制请求ID
    std::string current_output_path_;  // 当前录制输出路径
    
    // === 时间管理 ===
    std::chrono::steady_clock::time_point recording_start_time_;
    std::chrono::steady_clock::time_point last_highlight_time_;
    double highlight_interval_seconds_;  // 精彩时刻最小间隔
    double clip_before_seconds_;         // 精彩时刻前的时长
    double clip_after_seconds_;          // 精彩时刻后的时长
    
    // === 监控数据 ===
    std::atomic<double> current_ball_speed_;
    
    // === 精彩时刻数据 ===
    std::vector<HighlightMoment> highlight_moments_;
    mutable std::mutex highlights_mutex_;
    
    // === 生成的片段数据 ===
    std::vector<HighlightClip> generated_clips_;
    mutable std::mutex clips_mutex_;
    
    // === 路径配置 ===
    std::string output_directory_;
    static const std::string FFMPEG_PATH;  // FFmpeg可执行文件路径
};
