@echo off
setlocal
echo ======================================
echo 🎥 简化双目识别系统
echo ======================================

cd /d "%~dp0"

echo.
echo 📁 当前目录: %CD%
echo.

echo 🔨 构建后端服务器...
cd backend

if not exist "build" mkdir build
cd build

echo.
echo 🛠️  配置CMake项目...
cmake .. -G "Visual Studio 17 2022" -A x64
if %ERRORLEVEL% neq 0 (
    echo ❌ CMake配置失败！
    pause
    exit /b 1
)

echo.
echo 🔧 编译项目...
cmake --build . --config Release
if %ERRORLEVEL% neq 0 (
    echo ❌ 编译失败！
    pause
    exit /b 1
)

echo.
echo ✅ 编译成功！
echo.

echo 🚀 启动服务器...
echo.
echo 📱 Web访问地址: http://localhost:8080
echo 🔄 按 Ctrl+C 停止服务器
echo.

Release\server.exe

echo.
echo 服务器已停止
pause 