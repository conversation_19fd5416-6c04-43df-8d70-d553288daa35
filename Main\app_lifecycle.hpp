#pragma once

#include <vector>
#include <thread>
#include <memory>
#include <atomic>

// Service and Utility Headers
// By including the full headers here, we avoid the incomplete type issue with unique_ptr,
// even though it increases the number of includes for any file that includes this header.
#include "../Services/WebServerService.hpp"
#include "../Services/CameraService.hpp"
#include "../Services/InferenceService.hpp"
#include "../Services/RecordingService.hpp"
#include "../Services/StereoReconstructionService.hpp"
#include "../Services/DataLoggingService.hpp"
#include "../Services/CalibrationService.hpp"
#include "../Services/HighlightService.hpp"
#include "../Utils/SharedData.hpp"

/**
 * @brief 服务上下文，用于聚合所有核心服务和共享数据。
 */
struct ServiceContext {
    std::shared_ptr<SharedData> shared_data;
    std::unique_ptr<CameraService> camera_service;
    std::unique_ptr<InferenceService> inference_service_prototype; // 作为克隆的"原型"
    std::shared_ptr<RecordingService> recording_service;
    std::shared_ptr<WebServerService> web_service;
    std::unique_ptr<StereoReconstructionService> stereo_reconstruction_service;
    std::unique_ptr<Services::DataLoggingService> data_logging_service;
    std::unique_ptr<Services::CalibrationService> calibration_service;
    std::unique_ptr<HighlightService> highlight_service;
    std::atomic<bool> shutdown_flag{false};

    // With full types included, the default constructor/destructor are fine.
};


// --- Application Lifecycle Function Declarations ---

/**
 * @brief 阶段1: 初始化所有服务和共享数据。
 * @return 一个包含所有已初始化服务的上下文对象的 unique_ptr，如果失败则返回 nullptr。
 */
std::unique_ptr<ServiceContext> initialize_services();

/**
 * @brief 阶段2: 启动所有需要在后台独立运行的服务。
 */
void start_background_services(ServiceContext& context);

/**
 * @brief 阶段3: 根据检测到的摄像头数量，为每个摄像头创建并行的处理线程。
 * @return 包含所有已启动的工作线程的向量。
 */
std::vector<std::thread> launch_processing_pipelines(ServiceContext& context);

/**
 * @brief 阶段4: 等待所有工作线程执行完毕。
 */
void wait_for_shutdown(std::vector<std::thread>& threads, ServiceContext& context); 