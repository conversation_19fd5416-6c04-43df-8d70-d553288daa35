#pragma once

#include <stdio.h>
#include <conio.h>
#include <iostream>
#include <string>
#include "MvCameraControl.h"
#include "opencv2/opencv.hpp"
#include <getTimeStamp.h>
#include <vector>
#include <chrono>
#include <algorithm>
#include <filesystem>
#include <fstream>
#include <mutex>
#include <thread>
#include <atomic>
#include <condition_variable>
#include <queue>
#include <memory>

#define USE_TIMEOUT_READ_FRAME 0
class Frame
{
public:
    double time = -1;
    cv::Mat img;
    Frame() {};
    Frame(double time, cv::Mat frame) :time(time), img(img) {}

    Frame copy() const {
        Frame frame;
        frame.time = time;
        img.copyTo(frame.img);
        return frame;
    }
};
void savePic(cv::Mat pic);
void savePicCeli(cv::Mat left, cv::Mat right);

class Hik
{

public:
    class LRFrame {
    public:
        double time = -1;
        Frame frameLeft;
        Frame frameRight;
        bool empty() const {
            return time < 0 || frameLeft.img.empty() || frameRight.img.empty();
        }
        double timeDiff() const {
            return std::abs(frameLeft.time - frameRight.time);
        }
    };
    class Camera
    {
    private:
        void* handle = NULL;
        MV_CC_DEVICE_INFO_LIST* pInfoList;
        MV_CC_PIXEL_CONVERT_PARAM convert;
        unsigned int payload_size;
        char* currentRecordingPath = nullptr;

        bool bind();
        bool initCam();
        void convertInit();
        void convertFree();

    public:

        std::string id;
        unsigned int height;
        unsigned int width;
        MV_CC_DEVICE_INFO* pDeviceInfo;
        MV_FRAME_OUT_INFO_EX imgInfo;
        MV_FRAME_OUT mvFrame;

        bool read(cv::Mat& src_img);
        Camera(std::string id, MV_CC_DEVICE_INFO_LIST* pInfoList);
        ~Camera();
        float GetExposure();
        float GetGain();


    private:
       
        
    public:

    };
    class DualCamera {
        Hik::Camera* pCamLeft;
        Hik::Camera* pCamRight;
        bool started = false;
        std::string lastVideoPath;  // 存储最后录制的视频路径
    public:
        /**
         * @brief 获取最后录制的高帧率视频路径
         * @return 视频文件路径
         */
        std::string getLastHighFpsRecordingPath() const { return lastVideoPath; }

        DualCamera(Hik::Camera* pCamLeft, Hik::Camera* pCamRight)
            :pCamLeft(pCamLeft), pCamRight(pCamRight) {
        }
       
    
    };


    MV_CC_DEVICE_INFO_LIST infoList;
    Hik();
    ~Hik();
    void printDeviceInfo(MV_CC_DEVICE_INFO* pDeviceInfo);


private:
    void enumCams();

};


