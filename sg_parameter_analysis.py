#!/usr/bin/env python3
"""
SG滤波器参数优化分析脚本
基于210FPS相机的4.76ms真实采集间隔重新计算最优参数
"""

import math

def calculate_optimal_sg_parameters():
    """
    基于乒乓球运动特性和210FPS相机计算最优SG滤波器参数
    """
    
    # 系统参数
    camera_fps = 210  # 210 FPS相机
    real_interval_ms = 1000 / camera_fps  # 4.76ms真实采集间隔
    
    # 乒乓球运动特性
    max_ball_speed = 30  # m/s (专业选手最大球速)
    typical_ball_speed = 15  # m/s (典型球速)
    ball_diameter = 0.04  # m (乒乓球直径40mm)
    
    print("=== 乒乓球运动与相机参数 ===")
    print(f"相机帧率: {camera_fps} FPS")
    print(f"真实采集间隔: {real_interval_ms:.2f} ms")
    print(f"最大球速: {max_ball_speed} m/s")
    print(f"典型球速: {typical_ball_speed} m/s")
    print(f"球直径: {ball_diameter*1000} mm")
    print()
    
    # 计算不同窗口大小对应的时间跨度和距离跨度
    window_sizes = [3, 5, 7, 9, 11, 13, 15]
    
    print("=== 不同窗口大小的时间和空间覆盖分析 ===")
    print("窗口大小 | 时间跨度(ms) | 最大球速距离(mm) | 典型球速距离(mm) | 建议应用")
    print("-" * 80)
    
    optimal_candidates = []
    
    for ws in window_sizes:
        time_span_ms = ws * real_interval_ms
        max_distance_mm = max_ball_speed * (time_span_ms / 1000) * 1000  # 转换为mm
        typical_distance_mm = typical_ball_speed * (time_span_ms / 1000) * 1000
        
        # 评估准则（针对乒乓球高速运动重新调整）
        # 1. 时间跨度应该在合理范围内 (15-80ms，适合210FPS)
        # 2. 距离跨度重新评估：乒乓球高速运动下大距离是正常的
        # 3. 重点考虑时间窗口的合理性而非距离限制
        
        min_time_for_stability = 15  # ms，至少3个采样点以上
        max_reasonable_time = 80  # ms，保持响应性
        
        is_suitable = (min_time_for_stability <= time_span_ms <= max_reasonable_time)
        
        recommendation = ""
        if time_span_ms < min_time_for_stability:
            recommendation = "太短，噪声敏感"
        elif time_span_ms > max_reasonable_time:
            recommendation = "太长，响应迟钝"
        else:
            # 进一步细分推荐等级
            if 20 <= time_span_ms <= 50:
                recommendation = "✓ 优选 (平衡性能)"
                optimal_candidates.append(ws)
            elif 15 <= time_span_ms < 20:
                recommendation = "✓ 可选 (快速响应)"
                optimal_candidates.append(ws)
            elif 50 < time_span_ms <= 80:
                recommendation = "✓ 可选 (高精度)"
                optimal_candidates.append(ws)
            else:
                recommendation = "✓ 推荐使用"
                optimal_candidates.append(ws)
        
        print(f"{ws:8d} | {time_span_ms:10.1f} | {max_distance_mm:14.1f} | {typical_distance_mm:16.1f} | {recommendation}")
    
    print()
    print(f"=== 推荐的窗口大小: {optimal_candidates} ===")
    
    # 多项式阶数分析
    print("\n=== 多项式阶数选择分析 ===")
    print("阶数 | 特性 | 适用场景 | 计算复杂度")
    print("-" * 50)
    print("1    | 线性拟合，噪声鲁棒性强 | 高速直线运动，实时性要求高 | 低")
    print("2    | 二次拟合，可处理加速度 | 弧线轨迹，速度变化检测 | 中")
    print("3    | 三次拟合，平滑性更好 | 复杂轨迹，精确重建 | 高")
    
    # 对于乒乓球应用的建议
    print("\n=== 乒乓球应用的最终建议 ===")
    
    if optimal_candidates:
        recommended_window = optimal_candidates[0] if len(optimal_candidates) == 1 else optimal_candidates[1]
        print(f"推荐窗口大小: {recommended_window}")
        print(f"时间跨度: {recommended_window * real_interval_ms:.1f} ms")
        print(f"典型速度下覆盖距离: {typical_ball_speed * (recommended_window * real_interval_ms / 1000) * 1000:.1f} mm")
        
        print("\n多项式阶数建议:")
        print("- 实时性优先 (低延迟): 阶数1，线性拟合")
        print("- 精度平衡: 阶数2，可检测加速度变化") 
        print("- 最高精度 (略高延迟): 阶数2-3，适合离线分析")
        
        print(f"\n推荐配置 (实时系统):")
        print(f"window_size = {recommended_window}")
        print(f"poly_order = 1 或 2")
        
        return recommended_window, 2  # 返回推荐的窗口大小和多项式阶数
    else:
        print("警告：未找到理想的参数组合，需要进一步调整分析")
        return 7, 1

def compare_old_vs_new_parameters():
    """
    比较旧参数(基于142ms间隔)和新参数(基于4.76ms间隔)的效果
    """
    print("\n" + "="*60)
    print("旧参数 vs 新参数对比分析")
    print("="*60)
    
    # 旧参数 (基于处理时间戳)
    old_interval_ms = 142  # 处理间隔
    old_window_size = 5
    old_poly_order = 1
    old_time_span = old_window_size * old_interval_ms
    
    # 新参数 (基于相机时间戳)
    new_interval_ms = 4.76  # 210FPS真实间隔
    new_window_size = 9  # 建议值
    new_poly_order = 2  # 建议值
    new_time_span = new_window_size * new_interval_ms
    
    print(f"旧参数 (处理时间戳):")
    print(f"  间隔: {old_interval_ms} ms")
    print(f"  窗口: {old_window_size}")
    print(f"  阶数: {old_poly_order}")
    print(f"  总时间跨度: {old_time_span} ms")
    
    print(f"\n新参数 (相机时间戳):")
    print(f"  间隔: {new_interval_ms:.2f} ms")
    print(f"  窗口: {new_window_size}")
    print(f"  阶数: {new_poly_order}")
    print(f"  总时间跨度: {new_time_span:.1f} ms")
    
    print(f"\n改进效果:")
    print(f"  时间精度提升: {old_interval_ms/new_interval_ms:.1f}x")
    print(f"  时间跨度优化: {old_time_span:.0f}ms -> {new_time_span:.1f}ms")
    print(f"  拟合能力提升: 线性 -> 二次 (可检测加速度)")

if __name__ == "__main__":
    recommended_window, recommended_order = calculate_optimal_sg_parameters()
    compare_old_vs_new_parameters()
    
    print("\n" + "="*60)
    print("实施建议")
    print("="*60)
    print("1. 立即更新: 将窗口大小从5改为9")
    print("2. 性能测试: 多项式阶数从1升级到2，测试计算性能")
    print("3. 参数调优: 根据实际球速分布微调窗口大小 (7-11)")
    print("4. 性能监控: 关注速度计算稳定性和实时性平衡")