#pragma once

#include <string>
#include <memory>
#include <vector>
#include <mutex>
#include <atomic>
#include "opencv2/opencv.hpp"
#include "../Camera/hik.hpp"

class CameraService {
public:
    CameraService();
    ~CameraService();

    // 从指定相机获取一帧，返回一个Mat对象。
    // 返回Mat对象会触发廉价的浅拷贝（仅复制头和指针），不会复制数据。
    cv::Mat getFrame(int camera_id);
    
    // 为了简化，提供一个获取默认相机（左相机）帧的接口
    cv::Mat getNextFrame();

    bool areCamerasInitialized() const;
    
    // 获取帧尺寸
    unsigned int getFrameWidth() const;
    unsigned int getFrameHeight() const;

private:
    void initializeCameras();

    std::unique_ptr<Hik> hik_system;
    std::unique_ptr<Hik::Camera> left_camera;
    std::unique_ptr<Hik::Camera> right_camera;
    
    std::mutex camera_mutex;
    std::atomic<bool> cameras_initialized = false;

    // 相机ID常量
    static constexpr const char* idLeftCamera = "*********";
    static constexpr const char* idRightCamera = "*********";
    
}; 