<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>双目识别系统 - 界面演示</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <!-- 头部导航 -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-video"></i>
                    <h1>双目识别系统</h1>
                </div>
                <div class="status-indicator">
                    <div class="status-dot online"></div>
                    <span>已连接</span>
                </div>
                <div class="fps-indicator">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>24 FPS</span>
                </div>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 关键指标仪表板 -->
            <section class="dashboard-metrics">
                <h2><i class="fas fa-chart-line"></i> 实时监控仪表板</h2>
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-icon">
                            <i class="fas fa-tachometer-alt"></i>
                        </div>
                        <div class="metric-content">
                            <div class="metric-value">15.67 <span class="unit">m/s</span></div>
                            <div class="metric-label">实时球速</div>
                        </div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-icon">
                            <i class="fas fa-table-tennis-paddle-ball"></i>
                        </div>
                        <div class="metric-content">
                            <div class="metric-value">2</div>
                            <div class="metric-label">检测目标</div>
                        </div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-icon">
                            <i class="fas fa-video"></i>
                        </div>
                        <div class="metric-content">
                            <div class="metric-value">24 FPS</div>
                            <div class="metric-label">处理帧率</div>
                        </div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="metric-content">
                            <div class="metric-value">12:34:56</div>
                            <div class="metric-label">最后更新</div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 控制面板 -->
            <section class="control-dashboard">
                <div class="global-controls">
                    <div class="control-panel">
                        <h2><i class="fas fa-gamepad"></i> 系统控制</h2>
                        <div class="controls">
                            <button class="btn btn-primary"><i class="fas fa-play"></i> 启动系统</button>
                            <button class="btn"><i class="fas fa-stop"></i> 停止系统</button>
                            <button class="btn btn-record-all"><i class="fas fa-video"></i> 全部录制</button>
                            <button class="btn btn-calibration"><i class="fas fa-crosshairs"></i> 相机标定</button>
                            <button class="btn"><i class="fas fa-sync-alt"></i> 刷新连接</button>
                        </div>
                        <div class="threshold-control">
                            <label for="confidenceThreshold"><i class="fas fa-bullseye"></i> AI检测阈值</label>
                            <div class="slider-container">
                                <input type="range" id="confidenceThreshold" min="0.1" max="0.9" value="0.4" step="0.05">
                                <span id="thresholdValue">0.40</span>
                            </div>
                        </div>
                    </div>
                    <div class="status-panel">
                        <h2><i class="fas fa-heartbeat"></i> 系统状态</h2>
                        <div class="status-grid">
                            <div class="status-card">
                                <div class="status-icon"><i class="fas fa-camera"></i></div>
                                <div class="status-info">
                                    <div class="status-title">相机系统</div>
                                    <div class="status-value">运行中</div>
                                </div>
                            </div>
                            <div class="status-card">
                                <div class="status-icon"><i class="fas fa-satellite-dish"></i></div>
                                <div class="status-info">
                                    <div class="status-title">视频流</div>
                                    <div class="status-value">已启动</div>
                                </div>
                            </div>
                            <div class="status-card">
                                <div class="status-icon"><i class="fas fa-record-vinyl"></i></div>
                                <div class="status-info">
                                    <div class="status-title">全局录制</div>
                                    <div class="status-value">未录制</div>
                                </div>
                            </div>
                            <div class="status-card">
                                <div class="status-icon"><i class="fas fa-crosshairs"></i></div>
                                <div class="status-info">
                                    <div class="status-title">相机标定</div>
                                    <div class="status-value">空闲</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 摄像头画面 -->
            <section class="camera-display">
                <div class="camera-grid">
                    <div class="camera-item">
                        <div class="camera-header">
                            <h3><i class="fas fa-camera"></i> 左摄像头 (ID: 1)</h3>
                            <div class="camera-status status-connected">
                                <span class="status-text">已连接</span>
                            </div>
                        </div>
                        <div class="camera-view">
                            <div style="width: 100%; height: 100%; background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%); display: flex; align-items: center; justify-content: center; color: var(--text-muted-color); font-size: 1.2rem;">
                                <i class="fas fa-video" style="margin-right: 8px;"></i>
                                摄像头画面区域
                            </div>
                        </div>
                        <div class="camera-footer">
                            <div class="camera-stats">
                                <div class="stat-item">
                                    <i class="fas fa-cogs"></i>
                                    <span class="stat-value">210 FPS</span>
                                </div>
                            </div>
                            <div class="camera-controls">
                                <button class="btn btn-record"><i class="fas fa-circle"></i> 录制</button>
                                <div class="rec-status">
                                    <div class="rec-dot"></div>
                                    <span>未录制</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="camera-item">
                        <div class="camera-header">
                            <h3><i class="fas fa-camera"></i> 右摄像头 (ID: 2)</h3>
                            <div class="camera-status status-connected">
                                <span class="status-text">已连接</span>
                            </div>
                        </div>
                        <div class="camera-view">
                            <div style="width: 100%; height: 100%; background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%); display: flex; align-items: center; justify-content: center; color: var(--text-muted-color); font-size: 1.2rem;">
                                <i class="fas fa-video" style="margin-right: 8px;"></i>
                                摄像头画面区域
                            </div>
                        </div>
                        <div class="camera-footer">
                            <div class="camera-stats">
                                <div class="stat-item">
                                    <i class="fas fa-cogs"></i>
                                    <span class="stat-value">210 FPS</span>
                                </div>
                            </div>
                            <div class="camera-controls">
                                <button class="btn btn-record"><i class="fas fa-circle"></i> 录制</button>
                                <div class="rec-status">
                                    <div class="rec-dot"></div>
                                    <span>未录制</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 数据分析与可视化 -->
            <div class="analysis-section">
                <!-- 三维轨迹可视化 -->
                <section class="trajectory-viewer">
                    <div class="panel-header">
                        <h2><i class="fas fa-cube"></i> 三维轨迹可视化</h2>
                        <div class="status-message">轨迹数据正常</div>
                    </div>
                    <div class="trajectory-canvas-container">
                        <div style="width: 100%; height: 100%; background: linear-gradient(135deg, #000 0%, #111 100%); display: flex; align-items: center; justify-content: center; color: var(--text-muted-color); font-size: 1.2rem;">
                            <i class="fas fa-cube" style="margin-right: 8px;"></i>
                            三维轨迹可视化区域
                        </div>
                    </div>
                </section>

                <!-- 数据分析网格 -->
                <div class="analysis-grid">
                    <!-- 三维坐标历史 -->
                    <section class="coordinates-display">
                        <div class="panel-header">
                            <h2><i class="fas fa-table"></i> 坐标数据</h2>
                            <div class="coordinate-status">
                                <div class="status-dot online"></div>
                                <span>数据正常</span>
                            </div>
                        </div>
                        <div class="coordinates-content">
                             <div class="coordinates-table-container">
                                <table class="coordinates-table">
                                    <thead>
                                        <tr>
                                            <th><i class="fas fa-hashtag"></i> ID</th>
                                            <th><i class="fas fa-arrows-alt-h"></i> X (m)</th>
                                            <th><i class="fas fa-arrows-alt-v"></i> Y (m)</th>
                                            <th><i class="fas fa-arrow-up"></i> Z (m)</th>
                                            <th><i class="fas fa-percentage"></i> 置信度</th>
                                            <th><i class="fas fa-clock"></i> 时间</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr class="coordinate-row">
                                            <td>001</td>
                                            <td>1.234</td>
                                            <td>0.567</td>
                                            <td>0.890</td>
                                            <td>95%</td>
                                            <td>12:34:56</td>
                                        </tr>
                                        <tr class="coordinate-row">
                                            <td>002</td>
                                            <td>1.245</td>
                                            <td>0.578</td>
                                            <td>0.901</td>
                                            <td>92%</td>
                                            <td>12:34:57</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </section>

                    <!-- 数据库浏览器 -->
                    <section class="db-explorer">
                         <div class="panel-header">
                            <h2><i class="fas fa-database"></i> 数据查询</h2>
                        </div>
                        <div class="db-explorer-panel">
                            <div class="query-section">
                                <textarea class="sql-input" rows="3" placeholder="输入 SQL 查询语句...">SELECT * FROM trajectory ORDER BY id DESC LIMIT 10;</textarea>
                                <button class="btn"><i class="fas fa-play"></i> 执行查询</button>
                            </div>
                            <div class="results-container">
                                <div class="no-results">
                                    <i class="fas fa-search"></i>
                                    <p>执行查询以查看结果</p>
                                </div>
                            </div>
                        </div>
                    </section>
                </div>
            </div>
        </main>

        <!-- 底部信息 -->
        <footer class="footer">
            <div class="footer-content">
                <div class="footer-actions">
                    <button class="btn"><i class="fas fa-wifi"></i> 重新连接</button>
                    <button class="btn"><i class="fas fa-expand"></i> 全屏模式</button>
                </div>
                <div class="footer-info">
                    <p><i class="fas fa-copyright"></i> 2024 乒乓球自动裁判系统</p>
                    <p><i class="fas fa-clock"></i> 最后更新: 2024-06-23 12:34:56</p>
                </div>
            </div>
        </footer>
    </div>

    <script>
        // 简单的交互演示
        document.getElementById('thresholdValue').textContent = document.getElementById('confidenceThreshold').value;
        document.getElementById('confidenceThreshold').addEventListener('input', function() {
            document.getElementById('thresholdValue').textContent = this.value;
        });
    </script>
</body>
</html>
