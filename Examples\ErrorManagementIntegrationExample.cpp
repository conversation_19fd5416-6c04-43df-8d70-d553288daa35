// === Camera_Editor 错误管理系统集成示例 ===

#include "../Utils/ErrorManager.hpp"
#include "../Utils/PerformanceOptimizedErrorHandling.hpp"
#include "../Utils/MonitoringAndAlerting.hpp"
#include "../Services/CameraService.hpp"
#include "../Services/InferenceService.hpp"

using namespace ErrorManagement;

// === 1. 主程序初始化示例 ===
namespace MainIntegration {
    
    void initializeErrorManagement(std::shared_ptr<SharedData> shared_data) {
        // 1. 初始化错误管理系统
        ErrorManager::getInstance().initialize("C:/Dev/Camera_Editor/Data/error.log", ErrorSeverity::INFO);
        
        // 2. 配置210FPS高性能模式
        ErrorConfigPresets::setHighPerformanceMode();
        
        // 3. 注册错误恢复处理器
        registerErrorRecoveryHandlers(shared_data);
        
        // 4. 启动实时监控
        auto& monitor = RealTimeMonitor::getInstance();
        setupMonitoringAlerts(monitor);
        monitor.startMonitoring();
        
        LOG_INFO(ErrorCategory::SYSTEM_ERROR, "Error management system initialized successfully");
    }
    
    void registerErrorRecoveryHandlers(std::shared_ptr<SharedData> shared_data) {
        auto& manager = ErrorManager::getInstance();
        
        // 相机错误恢复
        manager.registerRecoveryHandler(ErrorCategory::CAMERA_ERROR, [shared_data]() {
            LOG_INFO(ErrorCategory::CAMERA_ERROR, "Attempting camera recovery...");
            
            try {
                // 1. 重新初始化相机服务
                // 2. 检查硬件连接
                // 3. 重置相机参数
                
                std::this_thread::sleep_for(std::chrono::milliseconds(100)); // 模拟恢复时间
                
                LOG_INFO(ErrorCategory::CAMERA_ERROR, "Camera recovery completed successfully");
                return true;
            } catch (...) {
                LOG_ERROR("Camera recovery failed");
                return false;
            }
        });
        
        // AI推理错误恢复
        manager.registerRecoveryHandler(ErrorCategory::INFERENCE_ERROR, [shared_data]() {
            LOG_INFO(ErrorCategory::INFERENCE_ERROR, "Attempting AI model recovery...");
            
            try {
                // 1. 重新加载模型
                // 2. 重新初始化GPU上下文
                // 3. 验证模型完整性
                
                LOG_INFO(ErrorCategory::INFERENCE_ERROR, "AI model recovery completed successfully");
                return true;
            } catch (...) {
                LOG_ERROR("AI model recovery failed");
                return false;
            }
        });
    }
    
    void setupMonitoringAlerts(RealTimeMonitor& monitor) {
        // 性能下降告警
        monitor.registerAlertHandler(RealTimeMonitor::AlertType::PERFORMANCE_DEGRADATION, 
            [](const RealTimeMonitor::Alert& alert) {
                LOG_WARNING(ErrorCategory::SYSTEM_ERROR, "Performance degradation detected: " + alert.message);
                
                // 可以触发自动优化措施
                // 例如：降低AI推理频率、减少日志输出等
            });
        
        // 高错误率告警
        monitor.registerAlertHandler(RealTimeMonitor::AlertType::HIGH_ERROR_RATE,
            [](const RealTimeMonitor::Alert& alert) {
                LOG_CRITICAL(ErrorCategory::SYSTEM_ERROR, "High error rate detected: " + alert.message);
                
                // 可以触发系统健康检查
                // 例如：运行诊断程序、生成详细报告等
            });
        
        // 硬件故障告警
        monitor.registerAlertHandler(RealTimeMonitor::AlertType::HARDWARE_FAILURE,
            [](const RealTimeMonitor::Alert& alert) {
                LOG_CRITICAL(ErrorCategory::CAMERA_ERROR, "Hardware failure detected: " + alert.message);
                
                // 可以触发紧急处理措施
                // 例如：切换到备用硬件、通知管理员等
            });
    }
}

// === 2. 相机服务集成示例 ===
namespace CameraServiceIntegration {
    
    class EnhancedCameraService : public CameraService {
    private:
        HighPerformanceErrorHandler& error_handler_;
        RealTimeMonitor& monitor_;
        PerformanceMonitor perf_monitor_;
        
        // 性能统计
        std::atomic<uint64_t> successful_captures_{0};
        std::atomic<uint64_t> failed_captures_{0};
        std::chrono::steady_clock::time_point last_stats_update_;
        
    public:
        EnhancedCameraService() 
            : error_handler_(HighPerformanceErrorHandler::getInstance()),
              monitor_(RealTimeMonitor::getInstance()),
              last_stats_update_(std::chrono::steady_clock::now()) {}
        
        cv::Mat getFrame(int camera_id) override {
            FAST_ERROR_CONTEXT("CameraService::getFrame", CAMERA_ERROR);
            
            auto start_time = std::chrono::high_resolution_clock::now();
            
            try {
                if (!cameras_initialized) {
                    LOG_FAST_ERROR(CAMERA_ERROR, "Cameras not initialized");
                    throw CameraException("Cameras not initialized", LOCATION_INFO);
                }
                
                Hik::Camera* camera = getCameraById(camera_id);
                if (!camera) {
                    LOG_FAST_ERROR(CAMERA_ERROR, "Invalid camera ID: " + std::to_string(camera_id));
                    throw CameraException("Invalid camera ID", LOCATION_INFO);
                }
                
                cv::Mat frame;
                if (!camera->read(frame)) {
                    failed_captures_++;
                    
                    // 使用高性能错误记录
                    error_handler_.logFastError(ErrorCategory::CAMERA_ERROR, ErrorSeverity::WARNING,
                                              "Failed to read frame from camera " + std::to_string(camera_id));
                    
                    // 更新监控指标
                    monitor_.incrementDroppedFrames();
                    
                    throw CameraException("Failed to capture frame", LOCATION_INFO);
                }
                
                successful_captures_++;
                monitor_.incrementFrameCount();
                
                // 记录性能指标
                auto end_time = std::chrono::high_resolution_clock::now();
                auto duration_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(end_time - start_time).count();
                perf_monitor_.recordFrameProcessingTime(duration_ns);
                
                // 定期更新统计信息
                updateStatistics();
                
                return frame.clone();
                
            } catch (const CameraException& ex) {
                auto end_time = std::chrono::high_resolution_clock::now();
                auto duration_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(end_time - start_time).count();
                perf_monitor_.recordErrorHandlingTime(duration_ns);
                
                ErrorManager::getInstance().handleException(ex);
                throw;
            }
        }
        
        // 高性能版本：使用Result类型避免异常开销
        FrameResult getFrameFast(int camera_id) {
            auto start_time = std::chrono::high_resolution_clock::now();
            
            if (!cameras_initialized) {
                error_handler_.logFastError(ErrorCategory::CAMERA_ERROR, ErrorSeverity::ERROR,
                                          "Cameras not initialized");
                return FrameResult(ErrorInfo(ErrorCategory::CAMERA_ERROR, ErrorSeverity::ERROR,
                                           "Cameras not initialized"));
            }
            
            Hik::Camera* camera = getCameraById(camera_id);
            if (!camera) {
                error_handler_.logFastError(ErrorCategory::CAMERA_ERROR, ErrorSeverity::ERROR,
                                          "Invalid camera ID");
                return FrameResult(ErrorInfo(ErrorCategory::CAMERA_ERROR, ErrorSeverity::ERROR,
                                           "Invalid camera ID"));
            }
            
            cv::Mat frame;
            if (!camera->read(frame)) {
                failed_captures_++;
                error_handler_.logFastError(ErrorCategory::CAMERA_ERROR, ErrorSeverity::WARNING,
                                          "Frame read failed");
                monitor_.incrementDroppedFrames();
                
                return FrameResult(ErrorInfo(ErrorCategory::CAMERA_ERROR, ErrorSeverity::WARNING,
                                           "Frame read failed"));
            }
            
            successful_captures_++;
            monitor_.incrementFrameCount();
            
            auto end_time = std::chrono::high_resolution_clock::now();
            auto duration_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(end_time - start_time).count();
            perf_monitor_.recordFrameProcessingTime(duration_ns);
            
            return FrameResult(frame.clone());
        }
        
    private:
        void updateStatistics() {
            auto now = std::chrono::steady_clock::now();
            auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - last_stats_update_);
            
            if (elapsed.count() >= 5) { // 每5秒更新一次
                uint64_t total = successful_captures_ + failed_captures_;
                if (total > 0) {
                    double success_rate = static_cast<double>(successful_captures_) / total;
                    double fps = static_cast<double>(successful_captures_) / elapsed.count();
                    
                    monitor_.updateFPS(fps);
                    
                    // 检查性能是否满足210FPS要求
                    if (fps < 200.0) {
                        monitor_.triggerAlert(RealTimeMonitor::AlertType::PERFORMANCE_DEGRADATION,
                                            RealTimeMonitor::AlertLevel::WARNING,
                                            "Camera FPS below threshold: " + std::to_string(fps),
                                            "CameraService");
                    }
                }
                
                last_stats_update_ = now;
                successful_captures_ = 0;
                failed_captures_ = 0;
            }
        }
    };
}

// === 3. AI推理服务集成示例 ===
namespace InferenceServiceIntegration {
    
    class EnhancedInferenceService : public InferenceService {
    private:
        HighPerformanceErrorHandler& error_handler_;
        PerformanceBaseline baseline_;
        
    public:
        EnhancedInferenceService(const std::string& engine_path, const std::string& class_names_path)
            : InferenceService(engine_path, class_names_path),
              error_handler_(HighPerformanceErrorHandler::getInstance()) {}
        
        DetectionResult processFrame(const cv::Mat& frame, float conf_threshold) override {
            FAST_ERROR_CONTEXT("InferenceService::processFrame", INFERENCE_ERROR);
            
            auto start_time = std::chrono::high_resolution_clock::now();
            
            try {
                if (!m_yolo) {
                    LOG_FAST_ERROR(INFERENCE_ERROR, "YOLO model not initialized");
                    throw InferenceException("YOLO model not initialized", LOCATION_INFO);
                }
                
                if (frame.empty()) {
                    LOG_FAST_ERROR(INFERENCE_ERROR, "Input frame is empty");
                    throw InferenceException("Input frame is empty", LOCATION_INFO);
                }
                
                // 执行推理
                auto yolo_result = m_yolo->inference(frame, conf_threshold);
                
                auto end_time = std::chrono::high_resolution_clock::now();
                auto duration_ms = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time).count();
                
                // 记录性能基线
                baseline_.addSample("inference_time_ms", duration_ms);
                
                // 检查推理时间是否异常
                if (baseline_.isAnomalous("inference_time_ms", duration_ms, 2.0)) {
                    LOG_FAST_WARNING(INFERENCE_ERROR, 
                                   "Inference time anomaly detected: " + std::to_string(duration_ms) + "ms");
                }
                
                // 210FPS要求检查
                if (duration_ms > 4) {
                    LOG_FAST_WARNING(INFERENCE_ERROR,
                                   "Inference time exceeds 210FPS threshold: " + std::to_string(duration_ms) + "ms");
                }
                
                // 转换结果
                DetectionResult service_result;
                // ... 结果转换逻辑 ...
                
                return service_result;
                
            } catch (const InferenceException& ex) {
                ErrorManager::getInstance().handleException(ex);
                throw;
            } catch (const std::exception& ex) {
                LOG_FAST_ERROR(INFERENCE_ERROR, "Unexpected error: " + std::string(ex.what()));
                throw InferenceException("Unexpected inference error", LOCATION_INFO);
            }
        }
    };
}

// === 4. 主处理循环集成示例 ===
namespace MainLoopIntegration {
    
    void processFrameWithErrorHandling(int camera_id, std::shared_ptr<SharedData> shared_data,
                                     EnhancedCameraService& camera_service,
                                     EnhancedInferenceService& inference_service) {
        
        FAST_ERROR_CONTEXT("MainLoop::processFrame", SYSTEM_ERROR);
        
        try {
            // 1. 获取帧（使用高性能版本）
            auto frame_result = camera_service.getFrameFast(camera_id);
            if (!frame_result.isSuccess()) {
                // 错误已经被记录，直接返回
                return;
            }
            
            cv::Mat frame = frame_result.getValue();
            
            // 2. AI推理
            float conf_threshold = shared_data->getConfidenceThreshold();
            auto detection_result = inference_service.processFrame(frame, conf_threshold);
            
            // 3. 更新共享数据
            shared_data->setNewFrame(camera_id, frame);
            shared_data->setDetectionResult(camera_id, detection_result);
            
        } catch (const CameraEditorException& ex) {
            // 统一异常处理
            ErrorManager::getInstance().handleException(ex);
            
            // 根据错误类型决定是否继续处理
            const auto& error_info = ex.getErrorInfo();
            if (error_info.severity >= ErrorSeverity::CRITICAL) {
                // 严重错误，可能需要停止处理
                LOG_CRITICAL(ErrorCategory::SYSTEM_ERROR, "Critical error in main loop, considering shutdown");
            }
            
        } catch (const std::exception& ex) {
            LOG_FAST_ERROR(SYSTEM_ERROR, "Unexpected error in main loop: " + std::string(ex.what()));
        }
    }
    
    // 主循环示例
    void mainProcessingLoop(std::shared_ptr<SharedData> shared_data) {
        EnhancedCameraService camera_service;
        EnhancedInferenceService inference_service("model.engine", "classes.txt");
        
        auto& monitor = RealTimeMonitor::getInstance();
        PerformanceMonitor perf_monitor;
        
        while (!shared_data->shouldShutdown()) {
            auto loop_start = std::chrono::high_resolution_clock::now();
            
            // 处理双摄像头
            for (int camera_id = 1; camera_id <= 2; ++camera_id) {
                processFrameWithErrorHandling(camera_id, shared_data, camera_service, inference_service);
            }
            
            auto loop_end = std::chrono::high_resolution_clock::now();
            auto loop_duration = std::chrono::duration_cast<std::chrono::microseconds>(loop_end - loop_start);
            
            // 性能监控
            perf_monitor.recordFrameProcessingTime(loop_duration.count() * 1000); // 转换为纳秒
            
            // 检查是否满足210FPS要求
            if (loop_duration.count() > 4760) { // 4.76ms in microseconds
                LOG_FAST_WARNING(SYSTEM_ERROR, 
                               "Main loop exceeded 210FPS threshold: " + std::to_string(loop_duration.count()) + "μs");
            }
            
            // 更新监控指标
            double current_fps = 1000000.0 / loop_duration.count(); // 计算当前FPS
            monitor.updateFPS(current_fps);
            
            // 检查系统健康状态
            if (!monitor.isSystemHealthy()) {
                LOG_WARNING(ErrorCategory::SYSTEM_ERROR, "System health check failed");
            }
        }
    }
}

// === 5. 最佳实践总结 ===
namespace BestPractices {
    
    /*
     * 1. 性能优化原则：
     *    - 使用FAST_ERROR_CONTEXT进行轻量级上下文管理
     *    - 优先使用LOG_FAST_ERROR而非异常处理
     *    - 在高频路径中使用Result类型避免异常开销
     *    - 定期批量处理错误而非实时处理
     * 
     * 2. 错误分类原则：
     *    - 按照错误来源分类（CAMERA_ERROR, INFERENCE_ERROR等）
     *    - 按照严重程度分级（WARNING, ERROR, CRITICAL等）
     *    - 为每种错误类型配置适当的恢复策略
     * 
     * 3. 监控告警原则：
     *    - 设置合理的告警阈值，避免告警风暴
     *    - 使用自适应阈值应对不同的系统负载
     *    - 实现告警聚合，减少重复告警
     *    - 提供清晰的告警处理指导
     * 
     * 4. 恢复策略原则：
     *    - 优先使用自动恢复，减少人工干预
     *    - 实现渐进式恢复，从轻量级到重量级
     *    - 设置恢复重试限制，避免无限循环
     *    - 记录恢复过程，便于问题诊断
     * 
     * 5. 日志管理原则：
     *    - 使用结构化日志格式，便于分析
     *    - 实现日志级别过滤，控制输出量
     *    - 定期轮转日志文件，避免磁盘空间耗尽
     *    - 提供日志搜索和分析工具
     */
}
