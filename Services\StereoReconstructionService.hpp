#pragma once

#include <memory>
#include <vector>
#include <mutex>
#include <chrono>
#include <deque>
#include "../Camera/dualEye.hpp"
#include "../Utils/SharedData.hpp"
#include "../Utils/math_utils.hpp"
#include "../Utils/DynamicROIPredictor.hpp"
#include "../Utils/DebugConfig.hpp"
#include "Deploy/yolo.hpp"
#include "../Main/sg_filter.hpp"
#include "DataLoggingService.hpp"

/**
 * @brief 立体视觉三维重建服务
 * 
 * 负责从双目摄像头的检测结果计算乒乓球的三维世界坐标
 * 使用DualEye库进行立体视觉匹配和三角化重建
 */
class StereoReconstructionService {
public:
    /**
     * @brief 构造函数
     * @param shared_data 共享数据总线指针
     * @param data_logging_service 数据记录服务指针
     * @param image_width 图像宽度 (默认1440)
     * @param image_height 图像高度 (默认1080)
     */
    explicit StereoReconstructionService(
        std::shared_ptr<SharedData> shared_data,
        Services::DataLoggingService* data_logging_service,
        int image_width = 1440, 
        int image_height = 1080
    );

    /**
     * @brief 处理双目检测结果，计算三维坐标
     * @param left_detections 左摄像头检测结果
     * @param right_detections 右摄像头检测结果
     * @param timestamp 时间戳
     * @return 是否成功计算出三维坐标
     */
    bool processDetections(
        const std::map<std::string, std::vector<Yolo::Detection>>& left_detections,
        const std::map<std::string, std::vector<Yolo::Detection>>& right_detections,
        std::chrono::milliseconds timestamp
    );

    /**
     * @brief 从SharedData获取检测结果并进行三维重建
     * @return 是否成功处理
     */
    bool processLatestDetections();

    /**
     * @brief 获取当前跟踪的目标类别列表
     */
    const std::vector<std::string>& getTargetClasses() const { return m_targetClasses; }

    /**
     * @brief 设置目标类别列表
     * @param classes 要追踪的类别名称列表
     */
    void setTargetClasses(const std::vector<std::string>& classes) { m_targetClasses = classes; }

    /**
     * @brief 设置匹配阈值
     * @param threshold Y坐标差异阈值（用于对极约束匹配）
     */
    void setMatchingThreshold(float threshold) { m_matchingThreshold = threshold; }

    /**
     * @brief 启用/禁用动态匹配阈值调整
     * @param enabled 是否启用动态阈值调整
     */
    void setDynamicThresholdEnabled(bool enabled) { m_dynamicThresholdEnabled = enabled; }

    /**
     * @brief 获取当前有效的匹配阈值（考虑动态调整）
     * @return 当前匹配阈值
     */
    float getCurrentMatchingThreshold() const;

    /**
     * @brief 重新加载标定数据
     */
    void reloadCalibrationData();

    /**
     * @brief 初始化ROI预测功能
     */
    void initializeROIFeatures();

    /**
     * @brief 启用/禁用ROI预测功能
     * @param enabled 是否启用ROI预测
     */
    void setROIEnabled(bool enabled);

    /**
     * @brief 获取统计信息
     */
    struct Statistics {
        int total_processed = 0;
        int successful_reconstructions = 0;
        int failed_matches = 0;
        double avg_processing_time_ms = 0.0;
    };

    Statistics getStatistics() const {
        std::lock_guard<std::mutex> lock(m_statsMutex);
        return m_stats;
    }

private:
    std::shared_ptr<SharedData> m_sharedData;
    Services::DataLoggingService* m_dataLoggingService;
    std::unique_ptr<DUE::C_DualEye> m_dualEye;
    
    // 配置参数
    std::vector<std::string> m_targetClasses = {"red_ball"}; // 默认追踪乒乓球（匹配YOLO模型输出）
    float m_matchingThreshold = 35.0f; // Y坐标匹配阈值（从20.0f放宽到35.0f以适应快球）

    // 动态匹配阈值调整参数
    bool m_dynamicThresholdEnabled = true; // 启用动态阈值调整
    float m_baseMatchingThreshold = 35.0f; // 基础匹配阈值
    float m_maxMatchingThreshold = 50.0f;  // 最大匹配阈值（快球场景）
    mutable int m_consecutiveFailures = 0; // 连续匹配失败次数（mutable允许在const方法中修改）
    static constexpr int MAX_FAILURES_BEFORE_ADJUSTMENT = 3; // 触发阈值调整的失败次数
    
    // --- 新增: 速度计算相关 ---
    struct TimedPoint3f {
        std::chrono::high_resolution_clock::time_point timestamp;
        MU::Point3f point;
    };
    std::deque<TimedPoint3f> m_positionHistory;
    const size_t m_historySize = 15; // 存储历史点的数量，应大于SG窗口
    
    // 基于210FPS相机和4.76ms真实采集间隔优化的SG滤波器参数
    const int m_sgWindowSize = 7;    // SG滤波器窗口大小：7点=33.3ms时间跨度，平衡性能最优
    const int m_sgPolyOrder = 2;     // SG滤波器多项式阶数：二次拟合，可检测加速度变化
    
    // 基于高精度速度计算和真实乒乓球运动特性优化的异常检测阈值
    static constexpr double SPEED_WARNING_THRESHOLD = 30.0;   // 警告：超出常见范围 (m/s)
    static constexpr double SPEED_ANOMALY_THRESHOLD = 35.0;   // 异常：可能的计算错误 (m/s)
    static constexpr double SPEED_CRITICAL_THRESHOLD = 40.0;  // 严重：明确的系统错误 (m/s)
    
    // 基于210FPS相机特性优化的时间间隔检测阈值
    static constexpr double EXPECTED_CAMERA_INTERVAL = 0.00476;  // 4.76ms理论间隔 (s)
    static constexpr double MAX_NORMAL_INTERVAL = 0.010;         // 10ms正常上限 (s)
    static constexpr double MAX_ACCEPTABLE_INTERVAL = 0.025;     // 25ms可接受上限 (s)
    static constexpr double MAX_PROBLEMATIC_INTERVAL = 0.100;    // 100ms问题间隔 (s)
    
    // 基于高精度检测优化的球丢失容忍时间
    static constexpr double BALL_LOSS_WARNING = 0.3;      // 300ms警告 (s)
    static constexpr double BALL_LOSS_TOLERANCE = 0.5;    // 500ms容忍极限 (s)
    
    // 数据质量检测阈值
    static constexpr double MIN_POSITION_CHANGE = 0.0005;  // 0.5mm最小位置变化 (m)
    static constexpr double MAX_ACCELERATION = 100.0;      // 100 m/s²最大加速度
    
    // --- 新增: 3D轨迹可视化相关 ---
    std::vector<BallPosition3D> m_currentTrajectory;
    const size_t m_maxTrajectorySize = 200; // 存储用于可视化的最大轨迹点数
    
    // --- 新增: 2D轨迹可视化相关 ---
    std::map<int, std::deque<cv::Point2f>> m_2d_trajectories;
    const size_t m_max2dTrajectorySize = 100; // 存储100个2D轨迹点

    bool m_wasBallDetectedLastFrame = false; // 用于判断是否开启新轨迹

    // --- 新增: ROI预测相关 ---
    std::unique_ptr<DynamicROIPredictor> m_roiPredictor;
    bool m_roiEnabled = false;

    // 统计信息
    mutable std::mutex m_statsMutex;
    Statistics m_stats;

    /**
     * @brief 更新统计信息
     */
    void updateStatistics(bool success, double processing_time_ms);

    /**
     * @brief (新增) 根据历史三维坐标点，使用SG滤波器计算并存储速度
     */
    void calculateAndStoreSpeed(const BallPosition3D& latest_position);
    
    /**
     * @brief (新增) 使用采集时间戳计算并存储速度
     * @param latest_position 最新的3D位置
     * @param capture_timestamp 相机采集时间戳
     */
    void calculateAndStoreSpeedWithTimestamp(const BallPosition3D& latest_position, 
                                           std::chrono::high_resolution_clock::time_point capture_timestamp);
    
    /**
     * @brief (新增) 带时间戳的检测处理方法
     * @param left_detections 左摄像头检测结果
     * @param right_detections 右摄像头检测结果
     * @param timestamp 时间戳
     * @param capture_time 相机采集时间戳
     * @return 是否成功计算出三维坐标
     */
    bool processDetectionsWithTimestamp(
        const std::map<std::string, std::vector<Yolo::Detection>>& left_detections,
        const std::map<std::string, std::vector<Yolo::Detection>>& right_detections,
        std::chrono::milliseconds timestamp,
        std::chrono::high_resolution_clock::time_point capture_time
    );

    /**
     * @brief 将Yolo检测结果转换为DetectionResult格式
     */
    DetectionResult convertYoloDetections(
        const std::map<std::string, std::vector<Yolo::Detection>>& yolo_detections
    );

    /**
     * @brief 使用DualEye进行ROI预测更新
     * @param latest_position 最新的3D位置
     */
    void updateROIPredictionsUsingDualEye(const BallPosition3D& latest_position);

    /**
     * @brief 在指定点周围生成ROI
     * @param center 中心点
     * @param size ROI尺寸
     * @return 生成的ROI矩形
     */
    cv::Rect generateROIAroundPoint(const cv::Point2f& center, int size = 150);
}; 