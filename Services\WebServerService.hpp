#pragma once

// === Claude Code Remote Development - Successfully Editing Windows Project ===
// This comment was added from WSL Ubuntu to prove remote editing works!
// Project path: /home/<USER>/Camera_Editor -> C:\Dev\Camera_Editor
// Edit timestamp: 2025-07-08 15:49

#include <crow.h>
#include <string>
#include <memory>
#include <vector>
#include <thread>
#include <mutex>
#include <atomic>
#include <unordered_set>
#include "../Camera/hik.hpp"
#include "opencv2/opencv.hpp"
#include "../Utils/SharedData.hpp"
#include "RecordingService.hpp"

// 前向声明
namespace Services {
    class CalibrationService;
}
class HighlightService;

// WebSocket连接管理
class WSConnection {
public:
    WSConnection(crow::websocket::connection* c, const std::string& connection_id);
    crow::websocket::connection* conn;
    std::string id;
    bool active;
    std::chrono::steady_clock::time_point last_activity;
};

class WebServerService {
public:
    static const int JPEG_QUALITY;
    static const int TARGET_FPS;

    WebServerService(std::shared_ptr<SharedData> shared_data, std::shared_ptr<RecordingService> recording_service);

    // 设置标定服务引用
    void setCalibrationService(Services::CalibrationService* calibration_service);

    // 设置精彩录制服务引用
    void setHighlightService(HighlightService* highlight_service);
    ~WebServerService();
    void start(int port = 8080);
    void broadcast3DCoordinates();
    void broadcastHighlightStatus();

private:
    crow::SimpleApp app;
    std::string frontend_path;
    std::shared_ptr<SharedData> shared_data_;
    std::shared_ptr<RecordingService> recording_service_;
    Services::CalibrationService* calibration_service_ = nullptr;
    HighlightService* highlight_service_ = nullptr;
    std::string db_path_;
    
    // WebSocket连接管理
    std::vector<WSConnection*> ws_connections;
    std::mutex ws_connections_mutex;
    std::atomic<int> connection_counter = {0};
    
    // 视频流管理
    std::atomic<bool> video_streaming{false};
    std::vector<std::thread> video_stream_threads;
    
    // 视频流配置
    // const int TARGET_FPS = 30; // 已移至静态成员
    // const int JPEG_QUALITY = 70; // 已移至静态成员

    bool hasExtension(const std::string& filename, const std::string& ext);
    void initConsole();
    bool fileExists(const std::string& path);
    std::string findFrontendPath();
    void startVideoStream();
    void captureAndBroadcastFrame(int camera_id, const cv::Mat& frame);
    void drawBallSpeedOverlay(cv::Mat& frame, int camera_id);
    std::vector<uint8_t> createWebSocketMessage(int camera_id, uint64_t timestamp, const std::vector<uchar>& jpeg_data);
    void broadcastToWebSocketClients(const std::vector<uint8_t>& message);
    void broadcastToWebSocketClients(const std::string& message);
    void stopVideoStream();
    void handleWebSocketMessage(WSConnection* conn, const std::string& message);
    void handle_db_query(const crow::request& req, crow::response& res);
    void broadcastTrajectory();
    void setupWebSocket();
    void setupRoutes();
}; 