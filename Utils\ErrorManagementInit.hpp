#pragma once

#include "ErrorManager.hpp"
#include "SharedData.hpp"
#include <memory>

namespace ErrorManagement {

/**
 * @brief 错误管理系统初始化器
 * 
 * 负责在应用启动时配置错误管理系统，
 * 注册各种错误恢复处理器
 */
class ErrorManagementInitializer {
public:
    /**
     * @brief 初始化错误管理系统
     * 
     * @param shared_data 共享数据总线引用
     * @param log_level 最小日志级别
     * @return 初始化是否成功
     */
    static bool initialize(std::shared_ptr<SharedData> shared_data, 
                          ErrorSeverity log_level = ErrorSeverity::INFO_LEVEL);
    
    /**
     * @brief 注册所有错误恢复处理器
     */
    static void registerRecoveryHandlers(std::shared_ptr<SharedData> shared_data);
    
    /**
     * @brief 配置210FPS性能优化
     */
    static void configure210FPSOptimizations();
    
    /**
     * @brief 关闭错误管理系统
     */
    static void shutdown();

private:
    // 具体的恢复处理器
    static bool recoverCameraError();
    static bool recoverInferenceError();
    static bool recoverReconstructionError();
    static bool recoverRecordingError();
    static bool recoverDatabaseError();
    static bool recoverNetworkError();
    
    static std::shared_ptr<SharedData> shared_data_;
};

/**
 * @brief 错误管理配置预设
 */
namespace ErrorConfigPresets {
    
    /**
     * @brief 开发模式配置
     * 详细日志，所有错误都记录
     */
    void setDevelopmentMode();
    
    /**
     * @brief 生产模式配置
     * 只记录WARNING及以上级别，启用性能优化
     */
    void setProductionMode();
    
    /**
     * @brief 210FPS高性能模式
     * 最小化错误处理开销，启用快速路径
     */
    void setHighPerformanceMode();
    
    /**
     * @brief 调试模式配置
     * 专注于特定类型的错误调试
     */
    void setDebuggingMode(ErrorCategory focus_category);
}

/**
 * @brief 系统健康监控器
 * 
 * 定期检查系统健康状态，触发必要的恢复操作
 */
class SystemHealthMonitor {
private:
    std::thread monitor_thread_;
    std::atomic<bool> should_stop_{false};
    std::chrono::seconds check_interval_{30}; // 30秒检查一次
    std::shared_ptr<SharedData> shared_data_;
    
    void monitorLoop();
    void checkSystemHealth();
    void reportHealthStatus();
    
public:
    SystemHealthMonitor(std::shared_ptr<SharedData> shared_data);
    ~SystemHealthMonitor();
    
    void start();
    void stop();
    void setCheckInterval(std::chrono::seconds interval) { check_interval_ = interval; }
};

/**
 * @brief 错误报告生成器
 * 
 * 生成详细的错误报告，用于问题诊断
 */
class ErrorReportGenerator {
public:
    struct SystemReport {
        std::chrono::system_clock::time_point report_time;
        std::chrono::duration<double> uptime;
        std::unordered_map<ErrorCategory, int> error_counts;
        std::unordered_map<ErrorSeverity, int> severity_counts;
        bool system_healthy;
        std::vector<std::string> recent_critical_errors;
        
        // 性能指标
        double average_fps;
        int total_frames_processed;
        int failed_operations;
        double success_rate;
    };
    
    /**
     * @brief 生成系统错误报告
     */
    static SystemReport generateReport(std::shared_ptr<SharedData> shared_data);
    
    /**
     * @brief 将报告保存到文件
     */
    static bool saveReportToFile(const SystemReport& report, const std::string& file_path);
    
    /**
     * @brief 将报告格式化为JSON字符串
     */
    static std::string formatReportAsJson(const SystemReport& report);
    
    /**
     * @brief 将报告格式化为可读文本
     */
    static std::string formatReportAsText(const SystemReport& report);
};

} // namespace ErrorManagement
