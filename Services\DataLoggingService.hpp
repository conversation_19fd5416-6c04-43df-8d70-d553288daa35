#pragma once

#include <string>
#include <vector>
#include <thread>
#include <atomic>
#include <memory>
#include "../Utils/threadsafe_queue.hpp"
#include "../Utils/math_utils.hpp"

// Forward declaration for SQLite
struct sqlite3;

namespace Services {

// 数据点结构，用于在服务之间传递
struct DataPoint {
    long long timestamp_ms; // Unix timestamp in milliseconds
    int camera_id;          // 摄像头ID (1=左摄像头, 2=右摄像头)
    MU::Point3f position;
    double speed;
};

class DataLoggingService {
public:
    /**
     * @brief 构造函数
     * @param db_path 数据库文件的路径
     */
    explicit DataLoggingService(const std::string& db_path);
    ~DataLoggingService();

    // 禁止拷贝和赋值
    DataLoggingService(const DataLoggingService&) = delete;
    DataLoggingService& operator=(const DataLoggingService&) = delete;

    /**
     * @brief 启动数据库写入线程
     */
    void start();

    /**
     * @brief 停止数据库写入线程
     */
    void stop();

    /**
     * @brief 记录一个数据点（线程安全）
     * @param data_point 要记录的数据
     */
    void log(DataPoint data_point);

private:
    /**
     * @brief 数据库写入线程的主循环
     */
    void writerLoop();

    /**
     * @brief 初始化数据库，如果表不存在则创建它
     * @return 如果成功则返回true
     */
    bool initializeDatabase();

    std::string m_dbPath;
    sqlite3* m_db = nullptr;
    ThreadSafeQueue<DataPoint> m_queue;
    std::atomic<bool> m_is_running{false};
    std::thread m_writerThread;
};

} // namespace Services 