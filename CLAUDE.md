# Claude Code 配置文件

## 项目信息
- **项目名称**: Camera_Editor - 乒乓球自动裁判系统
- **实际项目路径**: C:\Dev\Camera_Editor (Windows)
- **WSL访问路径**: /home/<USER>/Camera_Editor
- **构建环境**: Visual Studio 2022 + Windows 
- **运行环境**: Windows (录制、测试、相机控制)
- **编辑环境**: WSL (Claude Code远程编辑)

## 开发工作流
1. **编辑**: 在WSL中使用Claude Code编辑代码
2. **构建**: 切换到Windows环境运行构建脚本
3. **测试**: 在Windows环境中运行程序和测试
4. **调试**: 使用Visual Studio 2022进行调试

## 构建命令
- **Windows构建**: `build_web_server.bat`
- **CMake生成**: `cmake .. -G "Visual Studio 17 2022" -A x64`
- **编译**: `cmake --build . --config Release`

## 运行环境
- **主程序**: `./Release/Camera_Editor.exe`
- **Web访问**: `http://localhost:8080`
- **相机要求**: 双目高速摄像头

## 项目架构
- **架构模式**: 服务化设计 + SharedData数据总线
- **核心技术**: C++20 + CMake + TensorRT + OpenCV + Crow + SQLite
- **性能指标**: 210+ FPS处理，52.5次/秒AI推理

## 重要路径
- **源码**: Services/, Utils/, Deploy/, Main/
- **前端**: Web/frontend/
- **数据**: Data/
- **模型**: Deploy/models/
- **文档**: docs/

## 当前状态
- **项目状态**: 生产就绪，持续优化中
- **完成度**: 97%
- **下一步**: 动态ROI机制开发（最高优先级）

## 注意事项
- 必须使用绝对路径配置
- 严格遵循服务化架构，通过SharedData通信
- CMake是唯一真实来源，Visual Studio项目文件是自动生成的
- 所有服务间通信必须通过SharedData数据总线，禁止直接调用

## 联系方式
- **开发者**: <EMAIL>
- **最后更新**: 2025-07-08