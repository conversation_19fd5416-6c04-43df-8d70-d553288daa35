#include "CameraService.hpp"
#include "../Utils/utf8_utils.hpp"
#include <iostream>

CameraService::CameraService() {
    UTF8Utils::println("CameraService constructor called.");
    initializeCameras();
}

CameraService::~CameraService() {
    UTF8Utils::println("CameraService destructor called.");
    // 独占指针会自动管理资源，但为了明确，我们可以在这里调用 release
    if (left_camera) {
        left_camera.release();
    }
    if (right_camera) {
        right_camera.release();
    }
    if (hik_system) {
        hik_system.release();
    }
    UTF8Utils::println("相机资源已释放。");
}

void CameraService::initializeCameras() {
    try {
        UTF8Utils::println("🎥 正在初始化海康相机系统...");
        hik_system = std::make_unique<Hik>();
        
        UTF8Utils::println("📹 正在创建左相机 (ID: " + std::string(idLeftCamera) + ")...");
        left_camera = std::make_unique<Hik::Camera>(std::string(idLeftCamera), &hik_system->infoList);
        
        UTF8Utils::println("📹 正在创建右相机 (ID: " + std::string(idRightCamera) + ")...");
        right_camera = std::make_unique<Hik::Camera>(std::string(idRightCamera), &hik_system->infoList);
        
        cameras_initialized = true;
        UTF8Utils::println("✅ 海康相机初始化成功！");
    } catch (const std::exception& e) {
        std::cerr << "❌ 相机初始化失败: " << e.what() << std::endl;
        cameras_initialized = false;
    }
}

cv::Mat CameraService::getFrame(int camera_id) {
    if (!cameras_initialized) {
        return cv::Mat();
    }

    Hik::Camera* camera = nullptr;

    if (camera_id == 1) {
        camera = left_camera.get();
    } else if (camera_id == 2) {
        camera = right_camera.get();
    }

    if (camera) {
        cv::Mat frame;
        if (camera->read(frame)) {
            return frame.clone();
        }
    }
    return cv::Mat(); // 返回一个空Mat表示失败
}

cv::Mat CameraService::getNextFrame() {
    // 默认从左相机获取
    return getFrame(1);
}

bool CameraService::areCamerasInitialized() const {
    return cameras_initialized;
}

unsigned int CameraService::getFrameWidth() const {
    if (left_camera) {
        return left_camera->width;
    }
    return 0;
}

unsigned int CameraService::getFrameHeight() const {
    if (left_camera) {
        return left_camera->height;
    }
    return 0;
}

