#include <gtest/gtest.h>
#include "../Utils/ErrorManager.hpp"
#include "../Utils/ErrorManagementInit.hpp"
#include <chrono>
#include <thread>

using namespace ErrorManagement;

class ErrorManagementTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 初始化错误管理系统
        ErrorManager::getInstance().initialize("test_error.log", ErrorSeverity::DEBUG);
    }
    
    void TearDown() override {
        ErrorManager::getInstance().shutdown();
        ErrorManager::getInstance().resetErrorCounters();
    }
};

// === 基础功能测试 ===
TEST_F(ErrorManagementTest, BasicLogging) {
    auto& manager = ErrorManager::getInstance();
    
    // 测试不同级别的日志记录
    manager.log(ErrorSeverity::INFO, ErrorCategory::CAMERA_ERROR, "Test info message");
    manager.log(ErrorSeverity::WARNING, ErrorCategory::INFERENCE_ERROR, "Test warning message");
    manager.log(ErrorSeverity::ERROR, ErrorCategory::RECONSTRUCTION_ERROR, "Test error message");
    
    // 验证统计信息
    auto& stats = manager.getStatistics();
    EXPECT_EQ(stats.getErrorCount(ErrorCategory::CAMERA_ERROR), 1);
    EXPECT_EQ(stats.getErrorCount(ErrorCategory::INFERENCE_ERROR), 1);
    EXPECT_EQ(stats.getErrorCount(ErrorCategory::RECONSTRUCTION_ERROR), 1);
    EXPECT_EQ(stats.getSeverityCount(ErrorSeverity::INFO), 1);
    EXPECT_EQ(stats.getSeverityCount(ErrorSeverity::WARNING), 1);
    EXPECT_EQ(stats.getSeverityCount(ErrorSeverity::ERROR), 1);
}

// === 异常处理测试 ===
TEST_F(ErrorManagementTest, ExceptionHandling) {
    auto& manager = ErrorManager::getInstance();
    
    try {
        throw CameraException("Test camera exception", "test_location");
    } catch (const CameraEditorException& ex) {
        manager.handleException(ex);
        
        // 验证异常被正确记录
        auto& stats = manager.getStatistics();
        EXPECT_EQ(stats.getErrorCount(ErrorCategory::CAMERA_ERROR), 1);
        EXPECT_EQ(stats.getSeverityCount(ErrorSeverity::ERROR), 1);
    }
}

// === 错误抑制测试 ===
TEST_F(ErrorManagementTest, ErrorSuppression) {
    auto& manager = ErrorManager::getInstance();
    
    // 快速连续记录相同错误
    for (int i = 0; i < 10; ++i) {
        manager.log(ErrorSeverity::ERROR, ErrorCategory::CAMERA_ERROR, "Repeated error");
    }
    
    // 由于错误抑制，实际记录的错误数应该少于10
    auto& stats = manager.getStatistics();
    EXPECT_LT(stats.getErrorCount(ErrorCategory::CAMERA_ERROR), 10);
}

// === 性能测试 ===
TEST_F(ErrorManagementTest, PerformanceTest) {
    auto& manager = ErrorManager::getInstance();
    manager.enableFastPath(true);
    
    const int num_operations = 10000;
    auto start_time = std::chrono::high_resolution_clock::now();
    
    // 模拟210FPS场景下的高频错误记录
    for (int i = 0; i < num_operations; ++i) {
        manager.logFastPathError(ErrorCategory::CAMERA_ERROR, "Fast path error " + std::to_string(i));
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
    
    // 验证性能：10000次操作应在10ms内完成
    EXPECT_LT(duration.count(), 10000); // 10ms = 10000微秒
    
    std::cout << "Performance test: " << num_operations << " operations in " 
              << duration.count() << " microseconds" << std::endl;
}

// === 错误恢复测试 ===
TEST_F(ErrorManagementTest, ErrorRecovery) {
    auto& manager = ErrorManager::getInstance();
    
    bool recovery_called = false;
    
    // 注册恢复处理器
    manager.registerRecoveryHandler(ErrorCategory::CAMERA_ERROR, [&recovery_called]() {
        recovery_called = true;
        return true; // 模拟恢复成功
    });
    
    // 创建需要恢复的错误
    ErrorInfo error_info(ErrorCategory::CAMERA_ERROR, ErrorSeverity::ERROR, "Test error");
    error_info.recovery_strategy = RecoveryStrategy::RETRY;
    
    // 尝试恢复
    bool recovery_result = manager.attemptRecovery(ErrorCategory::CAMERA_ERROR, error_info);
    
    EXPECT_TRUE(recovery_result);
    EXPECT_TRUE(recovery_called);
}

// === 系统健康检查测试 ===
TEST_F(ErrorManagementTest, SystemHealthCheck) {
    auto& manager = ErrorManager::getInstance();
    
    // 初始状态应该是健康的
    EXPECT_TRUE(manager.isSystemHealthy());
    
    // 记录一些严重错误
    for (int i = 0; i < 5; ++i) {
        manager.log(ErrorSeverity::CRITICAL, ErrorCategory::SYSTEM_ERROR, "Critical error " + std::to_string(i));
    }
    
    // 系统应该仍然健康（阈值是10个严重错误）
    EXPECT_TRUE(manager.isSystemHealthy());
    
    // 记录更多严重错误
    for (int i = 0; i < 10; ++i) {
        manager.log(ErrorSeverity::CRITICAL, ErrorCategory::SYSTEM_ERROR, "Critical error " + std::to_string(i + 5));
    }
    
    // 现在系统应该不健康
    EXPECT_FALSE(manager.isSystemHealthy());
}

// === 多线程安全测试 ===
TEST_F(ErrorManagementTest, ThreadSafety) {
    auto& manager = ErrorManager::getInstance();
    const int num_threads = 10;
    const int operations_per_thread = 1000;
    
    std::vector<std::thread> threads;
    
    // 启动多个线程同时记录错误
    for (int t = 0; t < num_threads; ++t) {
        threads.emplace_back([&manager, operations_per_thread, t]() {
            for (int i = 0; i < operations_per_thread; ++i) {
                manager.log(ErrorSeverity::INFO, ErrorCategory::CAMERA_ERROR, 
                           "Thread " + std::to_string(t) + " operation " + std::to_string(i));
            }
        });
    }
    
    // 等待所有线程完成
    for (auto& thread : threads) {
        thread.join();
    }
    
    // 验证所有操作都被记录（考虑错误抑制）
    auto& stats = manager.getStatistics();
    EXPECT_GT(stats.getErrorCount(ErrorCategory::CAMERA_ERROR), 0);
    EXPECT_LE(stats.getErrorCount(ErrorCategory::CAMERA_ERROR), num_threads * operations_per_thread);
}

// === Result类型测试 ===
TEST_F(ErrorManagementTest, ResultType) {
    // 测试成功结果
    Result<int> success_result(42);
    EXPECT_TRUE(success_result.isSuccess());
    EXPECT_EQ(success_result.getValue(), 42);
    EXPECT_EQ(success_result.getValueOr(0), 42);
    
    // 测试错误结果
    ErrorInfo error_info(ErrorCategory::CAMERA_ERROR, ErrorSeverity::ERROR, "Test error");
    Result<int> error_result(error_info);
    EXPECT_FALSE(error_result.isSuccess());
    EXPECT_EQ(error_result.getValueOr(99), 99);
    EXPECT_EQ(error_result.getError().message, "Test error");
}

// === 集成测试 ===
class ErrorManagementIntegrationTest : public ::testing::Test {
protected:
    std::shared_ptr<SharedData> shared_data_;
    
    void SetUp() override {
        shared_data_ = std::make_shared<SharedData>();
        ErrorManagementInitializer::initialize(shared_data_, ErrorSeverity::DEBUG);
        ErrorConfigPresets::setDevelopmentMode();
    }
    
    void TearDown() override {
        ErrorManagementInitializer::shutdown();
    }
};

TEST_F(ErrorManagementIntegrationTest, FullSystemTest) {
    // 模拟完整的错误处理流程
    auto& manager = ErrorManager::getInstance();
    
    // 1. 记录各种类型的错误
    manager.log(ErrorSeverity::WARNING, ErrorCategory::CAMERA_ERROR, "Camera warning");
    manager.log(ErrorSeverity::ERROR, ErrorCategory::INFERENCE_ERROR, "Inference error");
    manager.log(ErrorSeverity::CRITICAL, ErrorCategory::RECONSTRUCTION_ERROR, "Reconstruction critical");
    
    // 2. 生成系统报告
    auto report = ErrorReportGenerator::generateReport(shared_data_);
    
    // 3. 验证报告内容
    EXPECT_GT(report.error_counts.size(), 0);
    EXPECT_GT(report.severity_counts.size(), 0);
    EXPECT_GT(report.uptime.count(), 0);
    
    // 4. 测试报告格式化
    std::string json_report = ErrorReportGenerator::formatReportAsJson(report);
    std::string text_report = ErrorReportGenerator::formatReportAsText(report);
    
    EXPECT_FALSE(json_report.empty());
    EXPECT_FALSE(text_report.empty());
    
    std::cout << "Generated JSON report length: " << json_report.length() << std::endl;
    std::cout << "Generated text report length: " << text_report.length() << std::endl;
}

// === 性能基准测试 ===
class ErrorManagementBenchmark : public ::testing::Test {
protected:
    void SetUp() override {
        ErrorManager::getInstance().initialize("benchmark_error.log", ErrorSeverity::WARNING);
        ErrorConfigPresets::setHighPerformanceMode();
    }
    
    void TearDown() override {
        ErrorManager::getInstance().shutdown();
    }
};

TEST_F(ErrorManagementBenchmark, HighFrequencyLogging) {
    auto& manager = ErrorManager::getInstance();
    const int iterations = 100000;
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    for (int i = 0; i < iterations; ++i) {
        FAST_LOG_ERROR(ErrorCategory::CAMERA_ERROR, "High frequency error " + std::to_string(i));
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
    
    double ops_per_second = static_cast<double>(iterations) / (duration.count() / 1000000.0);
    
    std::cout << "High frequency logging benchmark:" << std::endl;
    std::cout << "  Operations: " << iterations << std::endl;
    std::cout << "  Duration: " << duration.count() << " microseconds" << std::endl;
    std::cout << "  Operations per second: " << ops_per_second << std::endl;
    
    // 验证性能：应该能够处理至少100万次操作每秒
    EXPECT_GT(ops_per_second, 1000000);
}

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
