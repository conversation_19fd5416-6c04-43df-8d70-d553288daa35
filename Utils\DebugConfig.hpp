#pragma once

#include <string>
#include <iostream>
#include <chrono>
#include <iomanip>
#include <sstream>

/**
 * @brief 调试配置管理类
 *
 * 提供模块化的调试信息控制，避免终端信息过载
 * 支持运行时动态切换调试开关
 */
class DebugConfig {
public:
    // === 模块调试开关 ===
    static bool enable_ball_speed_debug;      // 球速计算调试
    static bool enable_camera_sync_debug;     // 摄像头同步调试
    static bool enable_frame_detection_debug; // 帧检测调试
    static bool enable_highlight_debug;       // 精彩时刻检测调试
    static bool enable_3d_reconstruction_debug; // 3D重建调试
    static bool enable_recording_debug;       // 录制功能调试
    static bool enable_web_server_debug;      // Web服务调试
    static bool enable_roi_debug;             // ROI预测调试

    // === 日志级别控制 ===
    enum LogLevel {
        DEBUG_LEVEL = 0,    // 详细调试信息
        INFO_LEVEL = 1,     // 一般信息
        WARNING_LEVEL = 2,  // 警告
        ERROR_LEVEL = 3,    // 错误
        CRITICAL_LEVEL = 4  // 严重错误
    };

    static LogLevel current_log_level;

    // === 摘要输出控制 ===
    static bool enable_summary_mode;          // 启用摘要模式（每N秒输出一次关键信息）
    static int summary_interval_seconds;      // 摘要输出间隔（秒）

    // === 运行时控制方法 ===

    /**
     * @brief 切换球速调试开关
     */
    static void toggleBallSpeedDebug();

    /**
     * @brief 切换摄像头同步调试开关
     */
    static void toggleCameraSyncDebug();

    /**
     * @brief 切换帧检测调试开关
     */
    static void toggleFrameDetectionDebug();

    /**
     * @brief 切换精彩时刻调试开关
     */
    static void toggleHighlightDebug();

    /**
     * @brief 设置日志级别
     */
    static void setLogLevel(LogLevel level);

    /**
     * @brief 启用/禁用摘要模式
     */
    static void setSummaryMode(bool enabled, int interval_seconds = 5);

    /**
     * @brief 启用所有调试
     */
    static void enableAllDebug();

    /**
     * @brief 禁用所有调试
     */
    static void disableAllDebug();

    /**
     * @brief 显示当前配置状态
     */
    static void showCurrentConfig();

    /**
     * @brief 检查是否应该输出指定级别的日志
     */
    static bool shouldLog(LogLevel level);

    /**
     * @brief 获取当前时间戳字符串
     */
    static std::string getCurrentTimestamp();

private:
    /**
     * @brief 记录配置变更
     */
    static void logConfigChange(const std::string& module, bool enabled);

    /**
     * @brief 日志级别转字符串
     */
    static std::string logLevelToString(LogLevel level);
};

// === 便捷的调试宏定义 ===

// 模块调试宏
#define DEBUG_BALL_SPEED(msg) if(DebugConfig::enable_ball_speed_debug) std::cout << "[🏓] " << msg << std::endl
#define DEBUG_CAMERA_SYNC(msg) if(DebugConfig::enable_camera_sync_debug) std::cout << "[📷] " << msg << std::endl
#define DEBUG_FRAME_DETECTION(msg) if(DebugConfig::enable_frame_detection_debug) std::cout << "[🎯] " << msg << std::endl
#define DEBUG_HIGHLIGHT(msg) if(DebugConfig::enable_highlight_debug) std::cout << "[⭐] " << msg << std::endl
#define DEBUG_3D_RECONSTRUCTION(msg) if(DebugConfig::enable_3d_reconstruction_debug) std::cout << "[🔺] " << msg << std::endl
#define DEBUG_RECORDING(msg) if(DebugConfig::enable_recording_debug) std::cout << "[🎬] " << msg << std::endl
#define DEBUG_WEB_SERVER(msg) if(DebugConfig::enable_web_server_debug) std::cout << "[🌐] " << msg << std::endl
#define DEBUG_ROI(msg) if(DebugConfig::enable_roi_debug) std::cout << "[🎯] " << msg << std::endl

// 日志级别宏
#define LOG_DEBUG(msg) if(DebugConfig::shouldLog(DebugConfig::DEBUG_LEVEL)) std::cout << "[" << DebugConfig::getCurrentTimestamp() << "] [DEBUG] " << msg << std::endl
#define LOG_INFO(msg) if(DebugConfig::shouldLog(DebugConfig::INFO_LEVEL)) std::cout << "[" << DebugConfig::getCurrentTimestamp() << "] [INFO] " << msg << std::endl
#define LOG_WARNING(msg) if(DebugConfig::shouldLog(DebugConfig::WARNING_LEVEL)) std::cout << "[" << DebugConfig::getCurrentTimestamp() << "] [WARNING] " << msg << std::endl
#define LOG_ERROR(msg) if(DebugConfig::shouldLog(DebugConfig::ERROR_LEVEL)) std::cout << "[" << DebugConfig::getCurrentTimestamp() << "] [ERROR] " << msg << std::endl
#define LOG_CRITICAL(msg) if(DebugConfig::shouldLog(DebugConfig::CRITICAL_LEVEL)) std::cout << "[" << DebugConfig::getCurrentTimestamp() << "] [CRITICAL] " << msg << std::endl
