#pragma once

#include <memory>
#include <opencv2/opencv.hpp>
#include "../Utils/SharedData.hpp"
#include "../Utils/DebugConfig.hpp"
#include "../Deploy/yolo.hpp" // 使用您提供的YOLO封装

// 推理结果结构，包含原始和转换后的数据
struct InferenceResult {
    DetectionResult detection_result;                                          // 转换后的结果
    std::map<std::string, std::vector<Yolo::Detection>> raw_yolo_detections;  // 原始YOLO结果
};

class InferenceService {
public:
    InferenceService(const std::string& engine_path, const std::string& class_names_path);
    ~InferenceService();

    // Clone method for creating thread-specific instances
    std::unique_ptr<InferenceService> clone() const;

    DetectionResult processFrame(const cv::Mat& frame, float conf_threshold);
    InferenceResult processFrameWithRaw(const cv::Mat& frame, float conf_threshold);

    // ROI支持方法
    InferenceResult processFrameWithROI(const cv::Mat& frame, int camera_id, float conf_threshold,
                                       std::shared_ptr<SharedData> shared_data);

    // 设置ROI安全开关
    void setROISafetySwitch(bool enabled);

private:
    // Private constructor for cloning
    InferenceService(std::unique_ptr<Yolo> yolo);

    std::unique_ptr<Yolo> m_yolo;

    // ROI安全机制
    bool m_roiSafetyEnabled = true;
    int m_roiFailureCount = 0;
    static constexpr int MAX_ROI_FAILURES = 10;

    // 辅助方法
    void adjustDetectionCoordinates(std::map<std::string, std::vector<Yolo::Detection>>& detections,
                                   const cv::Rect& roi);
    bool shouldUseROI() const;
}; 