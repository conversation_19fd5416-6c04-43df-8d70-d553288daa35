# 录制功能测试程序 CMake 配置

cmake_minimum_required(VERSION 3.16)

# 设置项目名称
project(RecordingTests)

# 设置 C++ 标准
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 查找 OpenCV
find_package(OpenCV REQUIRED)

# 包含目录
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/..)
include_directories(${OpenCV_INCLUDE_DIRS})

# 添加可执行文件
add_executable(recording_test 
    recording_test.cpp
    ../Services/RecordingService.cpp
    ../Services/CameraService.cpp
    ../Utils/SharedData.cpp
    ../Utils/utf8_utils.cpp
    ../Camera/hik.cpp
)

# 链接库
target_link_libraries(recording_test 
    ${OpenCV_LIBS}
    # 添加其他必要的库
)

# 设置编译选项
if(WIN32)
    target_compile_definitions(recording_test PRIVATE _WIN32_WINNT=0x0601)
    target_link_libraries(recording_test ws2_32)
endif()

# 设置输出目录
set_target_properties(recording_test PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# 复制测试数据（如果有的话）
file(MAKE_DIRECTORY ${CMAKE_BINARY_DIR}/Data/test_recordings)

message(STATUS "录制测试程序配置完成")
