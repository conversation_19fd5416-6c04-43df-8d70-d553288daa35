#include "ErrorManager.hpp"
#include "utf8_utils.hpp"
#include <iostream>
#include <iomanip>
#include <sstream>
#include <filesystem>

namespace ErrorManagement {

// === StructuredLogger 实现 ===
StructuredLogger::StructuredLogger(const std::string& log_file_path, 
                                 ErrorSeverity min_severity, bool console_output)
    : min_severity_(min_severity), console_output_(console_output) {
    
    // 确保日志目录存在
    std::filesystem::path path(log_file_path);
    if (path.has_parent_path()) {
        std::filesystem::create_directories(path.parent_path());
    }
    
    log_file_.open(log_file_path, std::ios::app);
    if (!log_file_.is_open()) {
        std::cerr << "Failed to open log file: " << log_file_path << std::endl;
    }
    
    writer_thread_ = std::thread(&StructuredLogger::writerLoop, this);
}

StructuredLogger::~StructuredLogger() {
    should_stop_ = true;
    queue_cv_.notify_all();
    if (writer_thread_.joinable()) {
        writer_thread_.join();
    }
    if (log_file_.is_open()) {
        log_file_.close();
    }
}

void StructuredLogger::log(ErrorSeverity severity, ErrorCategory category, 
                          const std::string& message, const std::string& location, 
                          const std::string& context) {
    if (severity < min_severity_) {
        return;
    }
    
    {
        std::lock_guard<std::mutex> lock(queue_mutex_);
        log_queue_.emplace(severity, category, message, location, context);
    }
    queue_cv_.notify_one();
}

void StructuredLogger::logError(const ErrorInfo& error_info) {
    log(error_info.severity, error_info.category, error_info.message, 
        error_info.source_location, error_info.context_data);
}

void StructuredLogger::writerLoop() {
    while (!should_stop_) {
        std::unique_lock<std::mutex> lock(queue_mutex_);
        queue_cv_.wait(lock, [this] { return !log_queue_.empty() || should_stop_; });
        
        while (!log_queue_.empty()) {
            LogEntry entry = log_queue_.front();
            log_queue_.pop();
            lock.unlock();
            
            std::string formatted = formatLogEntry(entry);
            
            // 写入文件
            if (log_file_.is_open()) {
                log_file_ << formatted << std::endl;
                log_file_.flush();
            }
            
            // 控制台输出
            if (console_output_) {
                if (entry.severity >= ErrorSeverity::ERROR_LEVEL) {
                    std::cerr << formatted << std::endl;
                } else {
                    UTF8Utils::println(formatted);
                }
            }
            
            lock.lock();
        }
    }
}

std::string StructuredLogger::formatLogEntry(const LogEntry& entry) const {
    std::ostringstream oss;
    
    // 时间戳
    auto time_t = std::chrono::system_clock::to_time_t(entry.timestamp);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        entry.timestamp.time_since_epoch()) % 1000;
    
    oss << "[" << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    oss << "." << std::setfill('0') << std::setw(3) << ms.count() << "] ";
    
    // 严重程度和分类
    oss << "[" << severityToString(entry.severity) << "] ";
    oss << "[" << categoryToString(entry.category) << "] ";
    
    // 线程ID
    oss << "[Thread:" << entry.thread_id << "] ";
    
    // 消息
    oss << entry.message;
    
    // 位置信息
    if (!entry.source_location.empty()) {
        oss << " @" << entry.source_location;
    }
    
    // 上下文数据
    if (!entry.context_data.empty()) {
        oss << " Context: " << entry.context_data;
    }
    
    return oss.str();
}

std::string StructuredLogger::severityToString(ErrorSeverity severity) const {
    switch (severity) {
        case ErrorSeverity::DEBUG_LEVEL: return "DEBUG";
        case ErrorSeverity::INFO_LEVEL: return "INFO";
        case ErrorSeverity::WARNING_LEVEL: return "WARN";
        case ErrorSeverity::ERROR_LEVEL: return "ERROR";
        case ErrorSeverity::CRITICAL_LEVEL: return "CRIT";
        case ErrorSeverity::FATAL_LEVEL: return "FATAL";
        default: return "UNKNOWN";
    }
}

std::string StructuredLogger::categoryToString(ErrorCategory category) const {
    switch (category) {
        case ErrorCategory::CAMERA_ERROR: return "CAMERA";
        case ErrorCategory::INFERENCE_ERROR: return "INFERENCE";
        case ErrorCategory::RECONSTRUCTION_ERROR: return "RECONSTRUCTION";
        case ErrorCategory::RECORDING_ERROR: return "RECORDING";
        case ErrorCategory::DATABASE_ERROR: return "DATABASE";
        case ErrorCategory::NETWORK_ERROR: return "NETWORK";
        case ErrorCategory::CALIBRATION_ERROR: return "CALIBRATION";
        case ErrorCategory::SYSTEM_ERROR: return "SYSTEM";
        case ErrorCategory::UNKNOWN_ERROR: return "UNKNOWN";
        default: return "UNDEFINED";
    }
}

// === ErrorManager 实现 ===
std::unique_ptr<ErrorManager> ErrorManager::instance_ = nullptr;
std::mutex ErrorManager::instance_mutex_;

ErrorManager::ErrorManager() {
    statistics_ = std::make_unique<ErrorStatistics>();
    recovery_handler_ = std::make_unique<ErrorRecoveryHandler>();
}

ErrorManager& ErrorManager::getInstance() {
    std::lock_guard<std::mutex> lock(instance_mutex_);
    if (!instance_) {
        instance_ = std::unique_ptr<ErrorManager>(new ErrorManager());
    }
    return *instance_;
}

void ErrorManager::initialize(const std::string& log_file_path, ErrorSeverity min_severity) {
    logger_ = std::make_unique<StructuredLogger>(log_file_path, min_severity, true);

    // 记录系统启动
    log(ErrorSeverity::INFO_LEVEL, ErrorCategory::SYSTEM_ERROR,
        "Camera_Editor Error Management System initialized");
}

void ErrorManager::log(ErrorSeverity severity, ErrorCategory category, 
                      const std::string& message, const std::string& location, 
                      const std::string& context) {
    if (!logger_) {
        return; // 未初始化时静默失败
    }
    
    // 性能优化：快速路径处理
    if (fast_path_enabled_ && severity <= ErrorSeverity::WARNING_LEVEL) {
        if (fast_path_error_count_.fetch_add(1) > MAX_FAST_PATH_ERRORS) {
            return; // 超过快速路径错误限制，丢弃
        }
    }
    
    ErrorInfo error_info(category, severity, message, location, context);
    statistics_->recordError(error_info);
    logger_->logError(error_info);
}

void ErrorManager::handleException(const CameraEditorException& ex) {
    const auto& error_info = ex.getErrorInfo();
    statistics_->recordError(error_info);
    
    if (logger_) {
        logger_->logError(error_info);
    }
    
    // 尝试错误恢复
    if (error_info.recovery_strategy != RecoveryStrategy::NONE) {
        attemptRecovery(error_info.category, error_info);
    }
}

void ErrorManager::handleUnknownException(const std::string& location) {
    log(ErrorSeverity::CRITICAL_LEVEL, ErrorCategory::UNKNOWN_ERROR,
        "Unknown exception caught", location);
}

void ErrorManager::registerRecoveryHandler(ErrorCategory category, 
                                          std::function<bool()> handler) {
    if (recovery_handler_) {
        recovery_handler_->registerRecoveryHandler(category, std::move(handler));
    }
}

bool ErrorManager::attemptRecovery(ErrorCategory category, const ErrorInfo& error_info) {
    if (recovery_handler_) {
        return recovery_handler_->attemptRecovery(error_info);
    }
    return false;
}

bool ErrorManager::shouldSuppressError(const std::string& error_key) {
    std::lock_guard<std::mutex> lock(suppression_mutex_);
    auto now = std::chrono::steady_clock::now();
    auto it = last_error_times_.find(error_key);
    
    if (it == last_error_times_.end() || 
        (now - it->second) > suppression_interval_) {
        last_error_times_[error_key] = now;
        return false; // 不抑制
    }
    
    return true; // 抑制
}

void ErrorManager::logFastPathError(ErrorCategory category, const std::string& message) {
    // 快速路径：最小开销的错误记录
    if (fast_path_error_count_.load() < MAX_FAST_PATH_ERRORS) {
        fast_path_error_count_++;
        // 可以选择性地记录到内存缓冲区，定期批量写入
    }
}

bool ErrorManager::isSystemHealthy() const {
    // 检查系统健康状态
    int critical_errors = statistics_->getSeverityCount(ErrorSeverity::CRITICAL_LEVEL);
    int fatal_errors = statistics_->getSeverityCount(ErrorSeverity::FATAL_LEVEL);

    return (critical_errors < 10 && fatal_errors == 0);
}

void ErrorManager::resetErrorCounters() {
    fast_path_error_count_ = 0;
    statistics_ = std::make_unique<ErrorStatistics>();
}

void ErrorManager::shutdown() {
    if (logger_) {
        log(ErrorSeverity::INFO_LEVEL, ErrorCategory::SYSTEM_ERROR,
            "Camera_Editor Error Management System shutting down");
        logger_.reset();
    }
}

} // namespace ErrorManagement
