<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>轨迹显示测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1a1a2e;
            color: white;
            font-family: Arial, sans-serif;
        }
        
        .test-container {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .camera-container {
            position: relative;
            width: 640px;
            height: 360px;
            border: 2px solid #333;
            background: #000;
        }
        
        .video-canvas {
            width: 100%;
            height: 100%;
            background: #222;
        }
        
        .trajectory-canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }
        
        .controls {
            margin: 20px 0;
        }
        
        .btn {
            padding: 10px 20px;
            margin: 5px;
            background: #0066cc;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        
        .btn:hover {
            background: #0088ff;
        }
        
        .status {
            background: #333;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .log {
            background: #000;
            padding: 10px;
            border-radius: 5px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>轨迹显示测试页面</h1>
    
    <div class="test-container">
        <div class="camera-container">
            <canvas class="video-canvas" id="leftCamera" width="640" height="360"></canvas>
            <canvas class="trajectory-canvas" id="leftTrajectoryCanvas"></canvas>
            <div style="position: absolute; bottom: 5px; left: 5px; background: rgba(0,0,0,0.7); padding: 5px; border-radius: 3px;">
                摄像头 1
            </div>
        </div>
        
        <div class="camera-container">
            <canvas class="video-canvas" id="rightCamera" width="640" height="360"></canvas>
            <canvas class="trajectory-canvas" id="rightTrajectoryCanvas"></canvas>
            <div style="position: absolute; bottom: 5px; left: 5px; background: rgba(0,0,0,0.7); padding: 5px; border-radius: 3px;">
                摄像头 2
            </div>
        </div>
    </div>
    
    <div class="controls">
        <button class="btn" onclick="startSimulation()">开始模拟轨迹</button>
        <button class="btn" onclick="stopSimulation()">停止模拟</button>
        <button class="btn" onclick="clearTrajectories()">清除轨迹</button>
        <button class="btn" onclick="testSinglePoint()">测试单点</button>
        <button class="btn" onclick="testMultiplePoints()">测试多点</button>
    </div>
    
    <div class="status">
        <h3>轨迹状态</h3>
        <div id="trajectoryStatus">等待测试...</div>
    </div>
    
    <div class="log" id="logContainer">
        <div>测试日志:</div>
    </div>

    <script>
        // 模拟轨迹测试器
        class TrajectoryTester {
            constructor() {
                this.trajectoryData2D = {
                    1: { points: [], width: 640, height: 360 },
                    2: { points: [], width: 640, height: 360 }
                };
                
                this.trajectoryConfig = {
                    maxAge: 300,
                    maxPoints: 200,
                    baseLineWidth: 3,
                    minOpacity: 0.1,
                    glowIntensity: 0.5,
                    pointRadius: 4,
                    pointShowDuration: 100,
                    colors: {
                        trajectory: [255, 87, 34],
                        point: [255, 255, 255],
                        glow: [255, 87, 34]
                    }
                };
                
                this.leftTrajectoryCanvas = document.getElementById('leftTrajectoryCanvas');
                this.rightTrajectoryCanvas = document.getElementById('rightTrajectoryCanvas');
                this.leftTrajectoryCtx = this.leftTrajectoryCanvas?.getContext('2d');
                this.rightTrajectoryCtx = this.rightTrajectoryCanvas?.getContext('2d');
                
                this.simulationInterval = null;
                this.lastCleanupTime = 0;
                
                // 开始清理循环
                setInterval(() => this.cleanup2DTrajectories(), 50);
                
                // 状态更新
                setInterval(() => this.updateStatus(), 1000);
                
                this.log('轨迹测试器初始化完成');
            }
            
            log(message) {
                const logContainer = document.getElementById('logContainer');
                const timestamp = new Date().toLocaleTimeString();
                logContainer.innerHTML += `<div>[${timestamp}] ${message}</div>`;
                logContainer.scrollTop = logContainer.scrollHeight;
            }
            
            // 轨迹处理函数（从 simple-video.js 复制）
            handleTrajectory2DUpdate(data) {
                const { camera_id, trajectory, original_width, original_height } = data;
                if (!camera_id || !trajectory) return;

                const currentTime = Date.now();

                // 初始化轨迹数据结构（如果不存在）
                if (!this.trajectoryData2D[camera_id]) {
                    this.trajectoryData2D[camera_id] = {
                        points: [],
                        width: original_width,
                        height: original_height,
                    };
                }

                // 更新画布尺寸信息
                this.trajectoryData2D[camera_id].width = original_width;
                this.trajectoryData2D[camera_id].height = original_height;

                // 增量更新轨迹点
                const existingPoints = this.trajectoryData2D[camera_id].points;
                const config = this.trajectoryConfig;

                // 清理过期的轨迹点
                this.trajectoryData2D[camera_id].points = existingPoints.filter(
                    point => currentTime - point.timestamp < config.maxAge
                );

                // 为新轨迹点添加时间戳
                const newPointsWithTime = trajectory.map((point, index) => ({
                    x: point.x,
                    y: point.y,
                    timestamp: currentTime - (trajectory.length - 1 - index) * 16
                }));

                // 合并新轨迹点，避免重复
                const existingPointsAfterCleanup = this.trajectoryData2D[camera_id].points;

                for (const newPoint of newPointsWithTime) {
                    let shouldAdd = true;

                    // 检查重复
                    const checkCount = Math.min(3, existingPointsAfterCleanup.length);
                    for (let i = existingPointsAfterCleanup.length - checkCount; i < existingPointsAfterCleanup.length; i++) {
                        if (i >= 0) {
                            const existingPoint = existingPointsAfterCleanup[i];
                            const distance = Math.sqrt(
                                Math.pow(newPoint.x - existingPoint.x, 2) +
                                Math.pow(newPoint.y - existingPoint.y, 2)
                            );

                            const timeDiff = Math.abs(newPoint.timestamp - existingPoint.timestamp);
                            if (distance < 3 && timeDiff < 50) {
                                shouldAdd = false;
                                break;
                            }
                        }
                    }

                    if (shouldAdd) {
                        this.trajectoryData2D[camera_id].points.push(newPoint);
                    }
                }

                // 限制轨迹点数量
                if (this.trajectoryData2D[camera_id].points.length > config.maxPoints) {
                    this.trajectoryData2D[camera_id].points = this.trajectoryData2D[camera_id].points.slice(-config.maxPoints);
                }

                this.drawTrajectory(camera_id);
            }

            drawTrajectory(cameraId) {
                const trajectoryInfo = this.trajectoryData2D[cameraId];
                if (!trajectoryInfo) return;

                let canvas, ctx;
                if (cameraId === 1) {
                    canvas = this.leftTrajectoryCanvas;
                    ctx = this.leftTrajectoryCtx;
                } else if (cameraId === 2) {
                    canvas = this.rightTrajectoryCanvas;
                    ctx = this.rightTrajectoryCtx;
                } else {
                    return;
                }

                if (!canvas || !ctx) return;

                // 确保Canvas尺寸正确
                const rect = canvas.getBoundingClientRect();
                const displayWidth = Math.round(rect.width);
                const displayHeight = Math.round(rect.height);

                if (canvas.width !== displayWidth || canvas.height !== displayHeight) {
                    canvas.width = displayWidth;
                    canvas.height = displayHeight;
                }

                // 清空Canvas
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                if (!trajectoryInfo.points || trajectoryInfo.points.length < 2) {
                    return;
                }

                const scaleX = canvas.width / trajectoryInfo.width;
                const scaleY = canvas.height / trajectoryInfo.height;
                const currentTime = Date.now();
                const config = this.trajectoryConfig;

                // 过滤有效轨迹点
                const validPoints = trajectoryInfo.points.filter(
                    point => point && typeof point.x === 'number' && typeof point.y === 'number' &&
                             currentTime - point.timestamp < config.maxAge
                );

                if (validPoints.length < 2) return;

                // 设置绘制样式
                ctx.lineCap = 'round';
                ctx.lineJoin = 'round';

                // 绘制轨迹线段
                for (let i = 1; i < validPoints.length; i++) {
                    const prevPoint = validPoints[i - 1];
                    const currPoint = validPoints[i];

                    // 计算透明度
                    const age = currentTime - currPoint.timestamp;
                    const normalizedAge = Math.min(age / config.maxAge, 1);
                    const easeOut = 1 - Math.pow(normalizedAge, 2);
                    const opacity = Math.max(config.minOpacity, easeOut);

                    // 计算线宽
                    const lineWidth = Math.max(1, config.baseLineWidth * Math.pow(opacity, 0.5));

                    // 设置颜色
                    const [r, g, b] = config.colors.trajectory;
                    ctx.strokeStyle = `rgba(${r}, ${g}, ${b}, ${opacity})`;
                    ctx.lineWidth = lineWidth;

                    // 发光效果
                    const [gr, gg, gb] = config.colors.glow;
                    ctx.shadowColor = `rgba(${gr}, ${gg}, ${gb}, ${opacity * config.glowIntensity})`;
                    ctx.shadowBlur = 3 * opacity;

                    // 绘制线段
                    ctx.beginPath();
                    ctx.moveTo(prevPoint.x * scaleX, prevPoint.y * scaleY);
                    ctx.lineTo(currPoint.x * scaleX, currPoint.y * scaleY);
                    ctx.stroke();
                }

                // 重置阴影
                ctx.shadowColor = 'transparent';
                ctx.shadowBlur = 0;

                // 绘制最新点
                if (validPoints.length > 0) {
                    const latestPoint = validPoints[validPoints.length - 1];
                    const age = currentTime - latestPoint.timestamp;

                    if (age < config.pointShowDuration) {
                        const pointOpacity = Math.max(0.3, 1 - (age / config.pointShowDuration));
                        const [pr, pg, pb] = config.colors.point;

                        // 外圈
                        ctx.fillStyle = `rgba(${pr}, ${pg}, ${pb}, ${pointOpacity * 0.3})`;
                        ctx.beginPath();
                        ctx.arc(latestPoint.x * scaleX, latestPoint.y * scaleY, config.pointRadius * 1.5 * pointOpacity, 0, 2 * Math.PI);
                        ctx.fill();

                        // 内圈
                        ctx.fillStyle = `rgba(${pr}, ${pg}, ${pb}, ${pointOpacity})`;
                        ctx.beginPath();
                        ctx.arc(latestPoint.x * scaleX, latestPoint.y * scaleY, config.pointRadius * pointOpacity, 0, 2 * Math.PI);
                        ctx.fill();
                    }
                }
            }

            cleanup2DTrajectories() {
                const currentTime = Date.now();
                const config = this.trajectoryConfig;
                let needsRedraw = false;

                for (const cameraIdStr in this.trajectoryData2D) {
                    const cameraId = parseInt(cameraIdStr);
                    const trajectoryInfo = this.trajectoryData2D[cameraId];

                    if (!trajectoryInfo || !trajectoryInfo.points) continue;

                    const oldLength = trajectoryInfo.points.length;

                    // 清理过期轨迹点
                    trajectoryInfo.points = trajectoryInfo.points.filter(point => {
                        if (!point || typeof point.timestamp !== 'number') {
                            return false;
                        }
                        return currentTime - point.timestamp < config.maxAge;
                    });

                    const newLength = trajectoryInfo.points.length;

                    if (oldLength !== newLength) {
                        needsRedraw = true;
                        this.drawTrajectory(cameraId);

                        if (newLength === 0 && oldLength > 0) {
                            this.log(`摄像头${cameraId}轨迹已清空 (清理了${oldLength}个点)`);
                        }
                    }
                }

                // 定期优化
                if (needsRedraw) {
                    const now = Date.now();
                    if (!this.lastCleanupTime || now - this.lastCleanupTime > 5000) {
                        this.lastCleanupTime = now;

                        for (const cameraIdStr in this.trajectoryData2D) {
                            const trajectoryInfo = this.trajectoryData2D[cameraIdStr];
                            if (trajectoryInfo && trajectoryInfo.points) {
                                if (trajectoryInfo.points.length > config.maxPoints) {
                                    trajectoryInfo.points = trajectoryInfo.points.slice(-config.maxPoints);
                                    this.log(`摄像头${cameraIdStr}轨迹点数量超限，已裁剪`);
                                }
                            }
                        }
                    }
                }
            }

            updateStatus() {
                const currentTime = Date.now();
                const statusDiv = document.getElementById('trajectoryStatus');
                let statusText = '';

                for (const cameraId in this.trajectoryData2D) {
                    const trajectoryInfo = this.trajectoryData2D[cameraId];
                    if (trajectoryInfo && trajectoryInfo.points) {
                        const validPoints = trajectoryInfo.points.filter(
                            point => currentTime - point.timestamp < this.trajectoryConfig.maxAge
                        );

                        statusText += `摄像头${cameraId}: ${validPoints.length}个有效轨迹点 (总计${trajectoryInfo.points.length}个)<br>`;

                        if (validPoints.length > 0) {
                            const oldestAge = currentTime - validPoints[0].timestamp;
                            const newestAge = currentTime - validPoints[validPoints.length - 1].timestamp;
                            statusText += `  最旧点: ${oldestAge}ms, 最新点: ${newestAge}ms<br>`;
                        }
                    }
                }

                statusDiv.innerHTML = statusText || '无轨迹数据';
            }
        }
        
        // 全局测试器实例
        let tester = new TrajectoryTester();
        
        // 测试函数
        function startSimulation() {
            if (tester.simulationInterval) return;
            
            tester.log('开始轨迹模拟');
            let angle = 0;
            
            tester.simulationInterval = setInterval(() => {
                // 模拟圆形轨迹
                const centerX = 320;
                const centerY = 180;
                const radius = 100;
                
                const x = centerX + Math.cos(angle) * radius;
                const y = centerY + Math.sin(angle) * radius;
                
                // 为两个摄像头添加轨迹点
                tester.handleTrajectory2DUpdate({
                    camera_id: 1,
                    trajectory: [{x: x, y: y}],
                    original_width: 640,
                    original_height: 360
                });
                
                tester.handleTrajectory2DUpdate({
                    camera_id: 2,
                    trajectory: [{x: x + 50, y: y + 30}], // 略微偏移
                    original_width: 640,
                    original_height: 360
                });
                
                angle += 0.1;
            }, 50);
        }
        
        function stopSimulation() {
            if (tester.simulationInterval) {
                clearInterval(tester.simulationInterval);
                tester.simulationInterval = null;
                tester.log('停止轨迹模拟');
            }
        }
        
        function clearTrajectories() {
            tester.trajectoryData2D[1].points = [];
            tester.trajectoryData2D[2].points = [];
            tester.drawTrajectory(1);
            tester.drawTrajectory(2);
            tester.log('清除所有轨迹');
        }
        
        function testSinglePoint() {
            const x = Math.random() * 640;
            const y = Math.random() * 360;
            
            tester.handleTrajectory2DUpdate({
                camera_id: 1,
                trajectory: [{x: x, y: y}],
                original_width: 640,
                original_height: 360
            });
            
            tester.log(`添加单个轨迹点: (${x.toFixed(1)}, ${y.toFixed(1)})`);
        }
        
        function testMultiplePoints() {
            const points = [];
            for (let i = 0; i < 10; i++) {
                points.push({
                    x: Math.random() * 640,
                    y: Math.random() * 360
                });
            }
            
            tester.handleTrajectory2DUpdate({
                camera_id: 2,
                trajectory: points,
                original_width: 640,
                original_height: 360
            });
            
            tester.log(`添加${points.length}个轨迹点`);
        }
    </script>
</body>
</html>
