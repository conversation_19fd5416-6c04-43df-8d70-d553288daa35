@echo off
setlocal enabledelayedexpansion
chcp 65001 >nul 2>&1

echo 测试可执行文件路径检查...
echo 当前目录: %CD%
echo.

echo 检查文件是否存在:
if exist "backend\build\camera_web_server.exe" (
    echo ✓ 文件存在: backend\build\camera_web_server.exe
    set EXE_PATH=backend\build\camera_web_server.exe
    echo ✓ EXE_PATH 设置为: !EXE_PATH!
    
    REM 再次检查 EXE_PATH 变量
    if defined EXE_PATH (
        echo ✓ EXE_PATH 变量已定义
        if exist "!EXE_PATH!" (
            echo ✓ 通过变量路径验证文件存在
        ) else (
            echo ✗ 通过变量路径验证文件不存在
        )
    ) else (
        echo ✗ EXE_PATH 变量未定义
    )
) else (
    echo ✗ 文件不存在: backend\build\camera_web_server.exe
)

pause 