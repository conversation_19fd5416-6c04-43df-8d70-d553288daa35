#include "detect.hpp"
#include "hik.hpp"

#include "dualEye.hpp"
#include <map>

#include <opencv2/highgui.hpp>
#include <opencv2/imgproc.hpp>
#include <opencv2/core/types.hpp>
#include <vector>
#include <chrono>
#include <cstdlib>
#include <thread>
#include <atomic>
#include <queue>
#include <mutex>
#include <condition_variable>

#include <cmath>
#include "Deploy/yolo.hpp"
#include<windows.h>
#include <iomanip> // for std::setprecision
#include <sstream> // for std::stringstream
#include "Utils/threadsafe_queue.hpp"

// 数据包结构，用于在线程间传递
struct FrameData {
	cv::Mat image;
	std::map<std::string, std::vector<Yolo::Detection>> detections;
	long long timestamp;
};

// 全局原子布尔值，用于控制线程的停止
std::atomic<bool> stop_threads(false);

// 生产者线程函数：负责单个摄像头的图像捕获和推理
void camera_producer_thread(int camera_id, ThreadSafeQueue<FrameData>* queue, std::unique_ptr<Yolo> yolo) {
	try {
		Hik hik;
		if (camera_id >= hik.infoList.nDeviceNum) {
			std::cerr << "Error: Camera ID " << camera_id << " is out of range." << std::endl;
			return;
		}
		Hik::Camera cam(std::to_string(camera_id), &hik.infoList);

		while (!stop_threads) {
			FrameData data;
			if (cam.read(data.image)) {
				data.detections = yolo->inference(data.image);
				data.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count();
				queue->push(data);
			}
			else {
				// 如果读取失败，可以稍微等待一下，避免空转消耗过多CPU
				std::this_thread::sleep_for(std::chrono::milliseconds(1));
			}
		}
	}
	catch (const std::exception& e) {
		std::cerr << "Exception in camera thread " << camera_id << ": " << e.what() << std::endl;
	}
	std::cout << "Camera thread " << camera_id << " is stopping." << std::endl;
}

// 添加缺失的宏定义
#ifndef CELIBRATE_WORLD
#define CELIBRATE_WORLD 0
#endif

#ifndef SAVE_IMGLR_TICK
#define SAVE_IMGLR_TICK 0
#endif

#ifndef SAVE_CELI_TICK
#define SAVE_CELI_TICK 0
#endif

void detectTask();
void startDetect()
{
	std::thread detectThread(detectTask);
	detectThread.join();//�ȴ��߳̽���
}
void detectTask() {

	// 初始化双目视觉系统
	// 注意：这里的宽高应该与相机实际输出的宽高一致
	DUE::C_DualEye de(1440, 1080); // 假设相机分辨率为1440x1080

	// 创建一个主YOLO实例，使用您指定的模型和类别文件
	Yolo yolo_master("../Yolo/new_best.engine", "../Yolo/classes.txt");

	// 创建两个线程安全的队列
	ThreadSafeQueue<FrameData> left_queue;
	ThreadSafeQueue<FrameData> right_queue;

	// 创建并启动生产者线程，传递 Yolo 实例的克隆
	// 使用整数ID：0 表示左摄像头，1 表示右摄像头
	std::thread left_producer(camera_producer_thread, 0, &left_queue, yolo_master.clone());
	std::thread right_producer(camera_producer_thread, 1, &right_queue, yolo_master.clone());

	cv::Mat imglr;

	auto t2 = std::chrono::system_clock::now();
	auto t3 = std::chrono::system_clock::now();
	while (true)
	{
		// 从队列中获取数据（阻塞式）
		FrameData left_data;
		left_queue.wait_and_pop(left_data);
		FrameData right_data;
		right_queue.wait_and_pop(right_data);

		// 优化的同步：对于210FPS摄像头，使用更严格的同步阈值
		const int SYNC_TOLERANCE_MS = 10; // 优化：从30ms降低到10ms，适合210FPS(~4.76ms/帧)
		if (std::abs(left_data.timestamp - right_data.timestamp) > SYNC_TOLERANCE_MS) {
			// 需要包含DebugConfig.hpp来使用调试宏
			std::cout << "⚠️ 双摄像头帧不同步，时间差: " <<
				std::abs(left_data.timestamp - right_data.timestamp) << "ms > " <<
				SYNC_TOLERANCE_MS << "ms，丢弃帧" << std::endl;
			continue;
		}

		cv::Mat& imgl = left_data.image;
		cv::Mat& imgr = right_data.image;

#if CELIBRATE_WORLD==1
		de.resetExternalRT(imgl, imgr);
#endif

		// 定义需要追踪的物体类别列表 (根据用户在README的修改，只追踪 "ball")
		const std::vector<std::string> target_classes = { "ball" };
		std::vector<DUE::C_PixData> all_matched_pairs;

		// 遍历所有目标类别，进行跨视图匹配
		for (const auto& class_name : target_classes) {
			auto matched_pairs = DUE::classifyMultiple(left_data.detections, right_data.detections, class_name, 20.0f);
			if (!matched_pairs.empty()) {
				all_matched_pairs.insert(all_matched_pairs.end(), matched_pairs.begin(), matched_pairs.end());
			}
		}

		// 首先绘制原始的2D检测框
		cv::Mat imgl_with_boxes = yolo_master.drawBox(imgl, &left_data.detections);
		cv::Mat imgr_with_boxes = yolo_master.drawBox(imgr, &right_data.detections);

		// 在图像上绘制世界坐标系原点，用于参考
		de.drawWorldOrigin(imgl_with_boxes, imgr_with_boxes);


		// 如果找到了匹配对，则计算三维坐标并进行可视化
		if (!all_matched_pairs.empty())
		{
			// 进行三维重建，计算世界坐标
			auto world_points = de.calP3inWorld(all_matched_pairs);

			// 遍历所有重建出的三维点
			for (size_t i = 0; i < world_points.size(); ++i)
			{
				const auto& p = world_points[i];
				std::stringstream ss;
				ss << "Ball " << i << " (m): X=" << std::fixed << std::setprecision(2) << p.x
					<< " Y=" << p.y << " Z=" << p.z;
				
				// 在左侧视频流上显示3D坐标
				cv::putText(imgl_with_boxes, ss.str(), cv::Point(10, 30 + i * 30), cv::FONT_HERSHEY_SIMPLEX, 0.7, cv::Scalar(0, 255, 255), 2);

				// 将3D点重投影回2D图像，用于验证精度
				auto projectedPoints = de.projectWorldPoint(p);

				// 检查投影点是否在图像范围内，然后绘制标记
				if (projectedPoints.first.x >= 0 && projectedPoints.second.x >= 0) {
					cv::drawMarker(imgl_with_boxes, projectedPoints.first, cv::Scalar(0, 255, 0), cv::MARKER_CROSS, 20, 2);
					cv::drawMarker(imgr_with_boxes, projectedPoints.second, cv::Scalar(0, 255, 0), cv::MARKER_CROSS, 20, 2);
				}
			}
		}

		// 拼接图像用于显示
		cv::vconcat(std::vector<cv::Mat> {
			imgl_with_boxes,
			imgr_with_boxes
		}, imglr);

		cv::imshow("infer", imglr);

		int key = cv::waitKey(1);

		if (key == 27)
		{
			stop_threads = true; // 设置停止标志
			break;
		}
		else if (key == 's'
#if SAVE_IMGLR_TICK==1
			|| std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now() - t2).count() > 500
#endif
			) {
			t2 = std::chrono::system_clock::now();
			savePic(imglr);
		}
		else if (key == 'c'
#if SAVE_CELI_TICK==1
			|| std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now() - t3).count() > 500

#endif
			) {
			t3 = std::chrono::system_clock::now();
			savePicCeli(imgl, imgr);
		}
	}

	// 等待生产者线程结束
	if(left_producer.joinable()) left_producer.join();
	if(right_producer.joinable()) right_producer.join();

	cv::destroyAllWindows();
}


