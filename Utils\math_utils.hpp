#ifndef MATH_UTILS_H
#define MATH_UTILS_H

#define _USE_MATH_DEFINES
#include <iostream>
#include <ostream>
#include <set>
#include <opencv2/core/types.hpp>

namespace MU
{
	//����������2��
	struct Derivate2f {
		// �Ƚ���  ��d0��Ϊset����������
		struct compare_d0 {
			bool operator()(const Derivate2f& a, const Derivate2f& b) const {
				return a.d0 < b.d0;
			}
		};
		float d0 = {};//���
		float d1 = {};//һ��
		float d2 = {};//����
		Derivate2f() :d0(0), d1(0), d2(0) {}
		Derivate2f(float d0) :d0(d0), d1(0), d2(0) {}
		Derivate2f(float d0, float d1) :d0(d0), d1(d1), d2(0) {}
		Derivate2f(float d0,float d1,float d2):d0(d0),d1(d1),d2(d2){}

		Derivate2f operator+(const Derivate2f& p) const;
		Derivate2f operator/(const float& p) const;
	};

	//3άֱ������
	class Point3f
	{
	public:
		float x{};
		float y{};
		float z{};

		// ���캯��

		Point3f(float x, float y, float z);
		Point3f(float p[]);
		Point3f(cv::Point3f p) :x(p.x), y(p.y), z(p.z) {}
		Point3f();

		//����ŷʽ����
		float distance(Point3f p = Point3f()) const;
		// 添加length()方法作为distance()的别名，用于计算向量长度
		inline float length() const { return distance(); }
		//
		Point3f cross(const Point3f p) const;
		//���
		float dot(const Point3f p) const;
		//��ĳ����ת
		Point3f rotateWith(const Point3f axis, const float angle) const;
		//��һ��
		Point3f normalize()const;
		//תΪ����
		void toArray(float dstArray[]) const;
		void print(char end)const;


		// ����������е�
		static Point3f midPoint3f(const Point3f& A, const Point3f& B) {
			return Point3f((A.x + B.x) / 2, (A.y + B.y) / 2, (A.z + B.z) / 2);
		}

		// �����д����ϵĵ㣬d�ǵ��������ߵľ��룬v�Ǹ����ķ�������
		static Point3f findPerpendicularPoint3f(const Point3f& A, const Point3f& B, const Point3f& v, double d) {
			
			Point3f M = midPoint3f(A, B); // �е�
			Point3f AB = B - A; // ��������
			Point3f u = AB.cross(v).cross(AB);
			Point3f direction = u.normalize(); // �д��߷�����������һ����

			// ���ݵ㵽�ߵľ��빫ʽ�����ŷ������������ƶ�����d
			double scale = d / direction.distance(Point3f());
			return M + direction * scale;
		}
		// ���������

		Point3f operator+(const Point3f& p) const;
		Point3f operator-(const Point3f& p) const;
		Point3f operator*(const Point3f& p) const;
		Point3f operator/(const Point3f& p) const;
		bool operator>=(const Point3f& p) const;
		bool operator<=(const Point3f& p) const;
		bool operator==(const Point3f& p) const;

		Point3f operator+(float f) const;
		Point3f operator-(float f) const;
		Point3f operator*(float f) const;
		Point3f operator/(float f) const;
		bool operator>=(float f) const;
		bool operator<=(float f) const;

		// ���� << �����
		friend std::ostream& operator<<(std::ostream& os, const Point3f& p) {
			os
				<< "["
				<< p.x << ","
				<< p.y << ","
				<< p.z << ","
				<< "]";
			return os;
		}
	};
	
	//6�ؽ�
	struct Joint6f
	{
		float a0{};
		float a1{};
		float a2{};
		float a3{};
		float a4{};
		float a5{};

		// ���캯��
		Joint6f(float a0, float a1, float a2, float a3, float a4, float a5);
		Joint6f(float a[]);
		Joint6f();

		float sum() const;
		float absMax() const;
		Joint6f abs() const;
		void toArray(float* dst) const;
		void print(char end) const;
		bool applyLim(Joint6f down, Joint6f up, Joint6f& limited);
		// ���������
		Joint6f operator+(const Joint6f& rhs) const;
		Joint6f operator-(const Joint6f& rhs) const;
		Joint6f operator*(const Joint6f& rhs) const;
		Joint6f operator/(const Joint6f& rhs) const;
		bool operator>=(const Joint6f& p) const;
		bool operator<=(const Joint6f& p) const;
		Joint6f operator+(const float& rhs) const;
		Joint6f operator-(const float& rhs) const;
		Joint6f operator*(const float& rhs) const;
		Joint6f operator/(const float& rhs) const;
		bool operator>=(const float& p) const;
		bool operator<=(const float& p) const;
		// ���� << �����
		friend std::ostream& operator<<(std::ostream& os, const Joint6f& p) {
			os
				<< "["
				<< p.a0 << ",\t"
				<< p.a1 << ",\t"
				<< p.a2 << ",\t"
				<< p.a3 << ",\t"
				<< p.a4 << ",\t"
				<< p.a5 << "\t"
				<< "]";
			return os;
		}
	};
	//6άλ��
	struct Point6f
	{
		//ԭ��
		Point3f origin;
		//x��
		Point3f xAxis;
		//y��
		Point3f yAxis;
		//z��
		Point3f zAxis;

		// �չ��캯��
		Point6f();
		/**
		* @brief ����λ�˱任������λ��
		* @param poseMat 4x4��λ�˱任����
		*/
		Point6f(float poseMat[16]);
		/**
		* @brief ����λ��ʸ����X��ʸ����Y��ʸ������λ�ˣ�Z��������ϵȷ��
		* @param o ԭ������ʸ��
		* @param x x��ʸ��
		* @param y y��ʸ��
		*/
		Point6f(Point3f o, Point3f x = Point3f(1, 0, 0), Point3f y = Point3f(0, 1, 0));
		/**
		* @brief �ȸ���λ��ʸ����X��ʸ����angleOffsetX����Z�ᣬ����Z��ƫ��angleOffsetZ�õ��µ�X��
		* @param o ԭ������ʸ��
		* @param x x��ʸ��
		*/
		Point6f(Point3f o, Point3f x, float angleOffsetX = 0.0f, float angleOffsetZ = 0.0f, float angleOffsetY = 0.0f);
		//����

		//para o : Origin Vector
		//para x : X Axis Vector
		//para angleOffset : Rotate Angle Around x
		/**
		* @brief �ȸ���λ��ʸ����X��ʸ����angleOffsetZ����Z�ᣬ����X��ƫ��angleOffsetX
		* @param o ԭ������ʸ��
		* @param x x��ʸ��
		*/
		//Point6f createByOX(Point3f o, Point3f x, float angleOffsetZ = 0.0f,float angleOffsetX=0.0f);

		Point6f createByOE(Point3f o, Point3f eulerAngle) const;
		void toPosMat(float* dstPosMat) const;
		Point3f toEulerAngle() const;
		void print(char end) const;

		// ���������
		Point6f operator+(const Point6f& rhs) const;
		Point6f operator-(const Point6f& rhs) const;
		Point6f operator+(const float& rhs) const;
		Point6f operator-(const float& rhs) const;
		Point6f operator*(const float& rhs) const;
		Point6f operator/(const float& rhs) const;
		// ���� << �����
		friend std::ostream& operator<<(std::ostream& os, const Point6f& p) 
		{
			Point3f tmp = p.toEulerAngle();
			os
				<< "["
				<< p.origin.x << ",\t"
				<< p.origin.y << ",\t"
				<< p.origin.z << ",\t"
				<< tmp.x << ",\t"
				<< tmp.y << ",\t"
				<< tmp.z << "\t"
				<< "]";
			return os;
		}
	};
	class Track3f {
	private:
		//���ó�ĩλ����ʱ�������ٶ�
		Point3f ppt2v0(MU::Point3f p0, MU::Point3f pt, double dt, Point3f v0 = Point3f())const;

	public:
		double timeStamp = 0.0;
		Point3f position;
		Point3f speed;

		struct compare_timeStamp {
			bool operator()(const Track3f& a, const Track3f& b) const {
				return a.timeStamp < b.timeStamp;
			}
		};

		Track3f();
		Track3f(double timeStamp, Point3f position, Point3f speed);
		Track3f(double timeStamp, Point3f position, Point3f position2, float zMax);
		Track3f(double timeStamp, Point3f position, double timeStamp2, Point3f position2);

		//���������ߵ�Ŀ��߶ȵ�ʱ��,dt1<=dt2
		bool calTimeToZ(float z, double& dt1, double& dt2)const;
		inline bool inRangeX(float low, float high)const {
			return this->position.x >= low && this->position.x <= high;
		}
		inline bool inRangeY(float low, float high)const {
			return this->position.y >= low && this->position.y <= high;
		}
		MU::Point6f toCatchPose(float xAxisFactor=-1);
		Track3f predict(double timeStamp)const;
		inline Track3f predictRef(double timeOffset)const {
			return this->predict(this->timeStamp + timeOffset);
		}
		double whenToMaxZ()const;
		double calFlyTime(float zBottom)const
		{
			double dt[2] = {};
			if (!calTimeToZ(zBottom, *dt, *(dt + 1)))
			{
				throw std::invalid_argument("");
			}
			if (dt[0] >= 0 || dt[1] <= 0)
			{
				throw std::invalid_argument("");
			}

			return -dt[0] + dt[1];
		}

		void print(char end)const;


	};


	struct Quaternion {
        float w;
        float x;
        float y;
        float z;
    } ;

    float angle2rad(float angle);
    Joint6f angle2rad(Joint6f angle);

    float rad2angle(float rad);
    Joint6f rad2angle(Joint6f angle);
    float RadLiPi(float rad);
    float AngleLi180(float angle);

    void MatMultiply(const float* _matrix1, const float* _matrix2, float* _matrixOut, const int _m, const int _l, const int _n);
	void MatTransPose(const float* matIn, float* matOut, const int n, const int m);
    Point3f crossProduct(const Point3f vector1, const Point3f vector2);
	//��ת����תŷ����
	Point3f RotMatToEulerAngle(const float* _rotationM);
	Point3f rotateVector(const Point3f& v, const Point3f& axis, float angleDegrees);
    Point3f normalizeVector(const Point3f vector);
	//D-H����תΪ4x4����
	void DH2matAdvanced(float d, float theta, float a, float alpha, float* _matrixOut);
    float vectorLength(const Point3f vector);

	// �������
	bool Mat4x4Inv(const float m[16], float out[16]);
};

//struct pose_t {
//	// �Ƚ���  ��timeStamp��Ϊset����������
//	struct compare_timeStamp {
//		bool operator()(const pose_t& a, const pose_t& b) const {
//			return a.timeStamp < b.timeStamp;
//		}
//	};
//	double timeStamp;
//	//λ��
//	MU::Point3f p;
//	//�ٶ�
//	MU::Point3f v;
//	//���ٶ�
//	MU::Point3f a;
//
//	pose_t(double tim, float x, float y, float z);
//	pose_t(double tim, MU::Point3f p);
//	pose_t(double tim, MU::Point3f p, MU::Point3f v);
//	pose_t();
//	void print();
//};

#endif // !MATH_UTILS_H
