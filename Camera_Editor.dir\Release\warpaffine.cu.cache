Identity=Deploy\infer\warpaffine.cu
AdditionalCompilerOptions=
AdditionalCompilerOptions=
AdditionalDependencies=
AdditionalDeps=
AdditionalLibraryDirectories=
AdditionalOptions= -std=c++17 --generate-code=arch=compute_52,code=[compute_52,sm_52] -Xcompiler="/EHsc -Ob2"
AdditionalOptions= -std=c++17 --generate-code=arch=compute_52,code=[compute_52,sm_52] -Xcompiler="/EHsc -Ob2"
CodeGeneration=
CodeGeneration=
CompileOut=Camera_Editor.dir\Release\warpaffine.obj
CudaRuntime=Static
CudaToolkitCustomDir=
DebugInformationFormat=None
DebugInformationFormat=None
Defines=;_WINDOWS;NDEBUG;API_EXPORTS;WIN32;_MBCS;CMAKE_INTDIR="Release";_MBCS;;WIN32;_WINDOWS;NDEBUG;API_EXPORTS;_MBCS;CMAKE_INTDIR="Release"
Emulation=false
EnableVirtualArchInFatbin=true
ExtensibleWholeProgramCompilation=false
FastMath=false
GenerateLineInfo=false
GenerateRelocatableDeviceCode=true
GPUDebugInfo=false
GPUDebugInfo=false
HostDebugInfo=false
Include=C:\Dev\Camera_Editor;C:\Dev\Camera_Editor\Camera;C:\Dev\Camera_Editor\Main;C:\Dev\Camera_Editor\Deploy;C:\Dev\Camera_Editor\Utils;C:\Dev\Camera_Editor\Services;C:\Dev\Camera_Editor\third_party\sqlite;C:\Dev\Camera_Editor\third_party;C:\Program Files (x86)\MVS\Development\Includes;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\include;C:\Dev\TensorRT-*********\include;C:\Dev\opencv\build\include;C:\Dev\Crow-master\include;C:\Dev\asio-1.30.2\include
Inputs=
InterleaveSourceInPTX=false
Keep=false
KeepDir=Camera_Editor\x64\Release
LinkOut=
MaxRegCount=0
NvccCompilation=compile
NvccPath=
Optimization=O2
Optimization=O2
PerformDeviceLink=
PerformDeviceLinkTimeOptimization=
PtxAsOptionV=false
RequiredIncludes=
Runtime=MD
Runtime=MD
RuntimeChecks=Default
RuntimeChecks=Default
SplitCompile=Default
SplitCompileCustomThreads=
TargetMachinePlatform=64
TargetMachinePlatform=64
TypeInfo=true
TypeInfo=true
UseHostDefines=true
UseHostInclude=false
UseHostLibraryDependencies=
UseHostLibraryDirectories=
Warning=W1
Warning=W1
