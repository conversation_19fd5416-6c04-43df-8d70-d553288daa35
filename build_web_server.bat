@echo off
echo ====================================
echo 双目视觉检测系统Web服务器编译脚本
echo ====================================

REM 设置编码为UTF-8
chcp 65001 > nul

REM 检查是否存在构建目录
if not exist "build" (
    echo 创建构建目录...
    mkdir build
)

REM 进入构建目录
cd build

echo 配置CMake项目...
cmake .. -G "Visual Studio 17 2022" -A x64

if %errorlevel% neq 0 (
    echo CMake配置失败！
    pause
    exit /b %errorlevel%
)

echo 编译项目...
cmake --build . --config Release --target camera_web_server

if %errorlevel% neq 0 (
    echo 编译失败！
    pause
    exit /b %errorlevel%
)

echo.
echo ====================================
echo 编译完成！
echo Web服务器可执行文件位置：
echo %cd%\Web\backend\build\camera_web_server.exe
echo ====================================
echo.

REM 询问是否启动服务器
set /p choice="是否立即启动Web服务器？(Y/N): "
if /i "%choice%"=="Y" (
    echo 启动Web服务器...
    cd Web\backend\build
    start camera_web_server.exe
    echo 服务器正在启动，请在浏览器中访问: http://localhost:8080
)

echo 按任意键退出...
pause > nul 