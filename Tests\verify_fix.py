#!/usr/bin/env python3
"""
录制修复验证脚本
用于分析录制的视频文件，检测帧重复问题是否已修复
"""

import cv2
import numpy as np
import os
import sys
from pathlib import Path
import argparse
from typing import List, Tuple, Dict

class FrameDuplicationDetector:
    """帧重复检测器"""
    
    def __init__(self, similarity_threshold: float = 0.95):
        """
        初始化检测器
        
        Args:
            similarity_threshold: 帧相似度阈值，超过此值认为是重复帧
        """
        self.similarity_threshold = similarity_threshold
        self.duplicate_pairs = []
        self.total_frames = 0
        
    def calculate_frame_similarity(self, frame1: np.ndarray, frame2: np.ndarray) -> float:
        """
        计算两帧之间的相似度
        
        Args:
            frame1: 第一帧
            frame2: 第二帧
            
        Returns:
            相似度值 (0-1)
        """
        # 转换为灰度图
        gray1 = cv2.cvtColor(frame1, cv2.COLOR_BGR2GRAY) if len(frame1.shape) == 3 else frame1
        gray2 = cv2.cvtColor(frame2, cv2.COLOR_BGR2GRAY) if len(frame2.shape) == 3 else frame2
        
        # 计算结构相似性
        # 使用简单的均方误差作为相似度度量
        mse = np.mean((gray1.astype(float) - gray2.astype(float)) ** 2)
        
        # 转换为相似度 (MSE越小，相似度越高)
        max_mse = 255.0 ** 2  # 最大可能的MSE
        similarity = 1.0 - (mse / max_mse)
        
        return similarity
    
    def detect_duplicates_in_video(self, video_path: str) -> Dict:
        """
        检测视频中的重复帧
        
        Args:
            video_path: 视频文件路径
            
        Returns:
            检测结果字典
        """
        print(f"🔍 分析视频: {video_path}")
        
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise ValueError(f"无法打开视频文件: {video_path}")
        
        # 获取视频信息
        fps = cap.get(cv2.CAP_PROP_FPS)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        duration = total_frames / fps if fps > 0 else 0
        
        print(f"  - 帧率: {fps:.2f} FPS")
        print(f"  - 总帧数: {total_frames}")
        print(f"  - 时长: {duration:.2f} 秒")
        
        self.total_frames = total_frames
        self.duplicate_pairs = []
        
        prev_frame = None
        frame_index = 0
        duplicate_count = 0
        consecutive_duplicates = 0
        max_consecutive = 0
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
                
            if prev_frame is not None:
                similarity = self.calculate_frame_similarity(prev_frame, frame)
                
                if similarity > self.similarity_threshold:
                    self.duplicate_pairs.append((frame_index - 1, frame_index, similarity))
                    duplicate_count += 1
                    consecutive_duplicates += 1
                    max_consecutive = max(max_consecutive, consecutive_duplicates)
                else:
                    consecutive_duplicates = 0
            
            prev_frame = frame.copy()
            frame_index += 1
            
            # 显示进度
            if frame_index % 100 == 0:
                progress = (frame_index / total_frames) * 100
                print(f"  进度: {progress:.1f}% ({frame_index}/{total_frames})")
        
        cap.release()
        
        # 计算统计信息
        duplicate_rate = (duplicate_count / total_frames) * 100 if total_frames > 0 else 0
        
        result = {
            'video_path': video_path,
            'fps': fps,
            'total_frames': total_frames,
            'duration': duration,
            'duplicate_count': duplicate_count,
            'duplicate_rate': duplicate_rate,
            'max_consecutive_duplicates': max_consecutive,
            'duplicate_pairs': self.duplicate_pairs[:10]  # 只保留前10个重复对作为示例
        }
        
        return result
    
    def analyze_duplicate_pattern(self, results: Dict) -> str:
        """
        分析重复帧模式
        
        Args:
            results: 检测结果
            
        Returns:
            模式分析结果
        """
        duplicate_count = results['duplicate_count']
        total_frames = results['total_frames']
        max_consecutive = results['max_consecutive_duplicates']
        
        if duplicate_count == 0:
            return "✅ 未检测到重复帧"
        elif duplicate_count < total_frames * 0.05:  # 少于5%
            return "⚠️ 检测到少量重复帧，可能是正常现象"
        elif max_consecutive >= 2:
            return "❌ 检测到连续重复帧，可能存在帧重复bug"
        else:
            return "⚠️ 检测到分散的重复帧"

def main():
    parser = argparse.ArgumentParser(description='录制修复验证工具')
    parser.add_argument('--video-dir', '-d', 
                       default='C:/Dev/Camera_Editor/Data/recordings',
                       help='录制视频目录')
    parser.add_argument('--threshold', '-t', type=float, default=0.95,
                       help='帧相似度阈值 (默认: 0.95)')
    parser.add_argument('--latest', '-l', action='store_true',
                       help='只分析最新的录制文件')
    
    args = parser.parse_args()
    
    video_dir = Path(args.video_dir)
    if not video_dir.exists():
        print(f"❌ 录制目录不存在: {video_dir}")
        return 1
    
    # 查找视频文件
    video_files = list(video_dir.glob('*.mp4')) + list(video_dir.glob('*.avi'))
    
    if not video_files:
        print(f"❌ 在目录 {video_dir} 中未找到视频文件")
        return 1
    
    if args.latest:
        # 只分析最新的文件
        video_files = [max(video_files, key=lambda f: f.stat().st_mtime)]
    
    print(f"📹 找到 {len(video_files)} 个视频文件")
    
    detector = FrameDuplicationDetector(args.threshold)
    
    for video_file in video_files:
        try:
            print(f"\n{'='*60}")
            results = detector.detect_duplicates_in_video(str(video_file))
            
            # 显示结果
            print(f"\n📊 分析结果:")
            print(f"  - 文件: {video_file.name}")
            print(f"  - 帧率: {results['fps']:.2f} FPS")
            print(f"  - 总帧数: {results['total_frames']}")
            print(f"  - 重复帧数: {results['duplicate_count']}")
            print(f"  - 重复率: {results['duplicate_rate']:.2f}%")
            print(f"  - 最大连续重复: {results['max_consecutive_duplicates']}")
            
            # 模式分析
            pattern_analysis = detector.analyze_duplicate_pattern(results)
            print(f"  - 模式分析: {pattern_analysis}")
            
            # 显示重复帧示例
            if results['duplicate_pairs']:
                print(f"  - 重复帧示例:")
                for i, (frame1, frame2, similarity) in enumerate(results['duplicate_pairs'][:5]):
                    print(f"    * 帧 {frame1} 和 {frame2}: 相似度 {similarity:.3f}")
            
        except Exception as e:
            print(f"❌ 分析视频 {video_file} 时出错: {e}")
    
    print(f"\n✅ 分析完成！")
    
    # 给出修复建议
    print(f"\n💡 修复验证建议:")
    print(f"  1. 如果重复率 < 5%，说明修复效果良好")
    print(f"  2. 如果仍有大量连续重复帧，需要进一步调试")
    print(f"  3. 建议对比修复前后的录制文件")
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
