<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>球速心电图测试</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Chart.js 库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.1/dist/chart.umd.js"></script>
    <style>
        body {
            background: #0a0a0a;
            color: #ffffff;
            font-family: 'Orbitron', monospace;
            margin: 0;
            padding: 20px;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-header h1 {
            color: #00ff00;
            text-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
        }
        
        .ecg-demo {
            background: rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(0, 255, 0, 0.3);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .ecg-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            justify-content: center;
        }
        
        .btn {
            background: rgba(0, 255, 0, 0.2);
            border: 1px solid rgba(0, 255, 0, 0.5);
            color: #00ff00;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-family: 'Orbitron', monospace;
            transition: all 0.3s;
        }
        
        .btn:hover {
            background: rgba(0, 255, 0, 0.3);
            box-shadow: 0 0 10px rgba(0, 255, 0, 0.3);
        }
        
        .chart-container {
            background: rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(0, 255, 0, 0.3);
            border-radius: 8px;
            padding: 16px;
            height: 400px;
        }
        
        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .chart-header h3 {
            color: #00ff00;
            margin: 0;
        }
        
        .ecg-current-speed {
            display: flex;
            align-items: center;
            gap: 4px;
            padding: 4px 8px;
            background: rgba(0, 255, 0, 0.1);
            border: 1px solid rgba(0, 255, 0, 0.3);
            border-radius: 4px;
            font-family: 'Orbitron', monospace;
        }
        
        .ecg-speed-label {
            color: #a0a0a0;
            font-size: 11px;
        }
        
        .ecg-speed-value {
            color: #00ff00;
            font-size: 14px;
            font-weight: bold;
            text-shadow: 0 0 4px rgba(0, 255, 0, 0.5);
            min-width: 40px;
            text-align: right;
        }
        
        .ecg-speed-unit {
            color: #a0a0a0;
            font-size: 11px;
        }
        
        .chart-wrapper {
            height: calc(100% - 60px);
        }
        
        #speedEcgChart {
            background: rgba(0, 0, 0, 0.8);
            border: 1px solid rgba(0, 255, 0, 0.2);
            border-radius: 4px;
        }
        
        .status-info {
            margin-top: 20px;
            padding: 15px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 4px;
            font-size: 12px;
            color: #a0a0a0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-heartbeat"></i> 球速心电图测试</h1>
            <p>模拟实时球速数据，测试心电图样式的可视化效果</p>
        </div>
        
        <div class="ecg-demo">
            <div class="ecg-controls">
                <button class="btn" id="startSimulation"><i class="fas fa-play"></i> 开始模拟</button>
                <button class="btn" id="stopSimulation"><i class="fas fa-stop"></i> 停止模拟</button>
                <button class="btn" id="clearData"><i class="fas fa-trash"></i> 清空数据</button>
                <button class="btn" id="randomPulse"><i class="fas fa-bolt"></i> 随机脉冲</button>
            </div>
            
            <div class="chart-container">
                <div class="chart-header">
                    <h3><i class="fas fa-heartbeat"></i> 球速心电图</h3>
                    <div class="ecg-current-speed">
                        <span class="ecg-speed-label">当前球速:</span>
                        <span class="ecg-speed-value" id="ecgCurrentSpeed">0.0</span>
                        <span class="ecg-speed-unit">m/s</span>
                    </div>
                </div>
                <div class="chart-wrapper">
                    <canvas id="speedEcgChart"></canvas>
                </div>
            </div>
            
            <div class="status-info">
                <p><strong>测试说明:</strong></p>
                <ul>
                    <li>点击"开始模拟"开始生成随机球速数据</li>
                    <li>心电图会实时显示最近100个数据点</li>
                    <li>当前球速值会根据速度大小改变颜色</li>
                    <li>绿色线条模拟医疗设备的心电图效果</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        class EcgTest {
            constructor() {
                this.chart = null;
                this.simulationInterval = null;
                this.ecgData = {
                    timeLabels: [],
                    speedValues: [],
                    maxDataPoints: 100,
                    currentSpeed: 0
                };
                
                this.init();
            }
            
            init() {
                this.initChart();
                this.bindEvents();
            }
            
            initChart() {
                const canvas = document.getElementById('speedEcgChart');
                const ctx = canvas.getContext('2d');
                
                this.chart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: this.ecgData.timeLabels,
                        datasets: [{
                            label: '球速 (m/s)',
                            data: this.ecgData.speedValues,
                            borderColor: '#00ff00',
                            backgroundColor: 'rgba(0, 255, 0, 0.1)',
                            borderWidth: 2,
                            fill: false,
                            tension: 0.1,
                            pointRadius: 0,
                            pointHoverRadius: 4,
                            pointBackgroundColor: '#00ff00',
                            pointBorderColor: '#00ff00',
                            pointBorderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        animation: {
                            duration: 0
                        },
                        interaction: {
                            intersect: false,
                            mode: 'index'
                        },
                        plugins: {
                            legend: {
                                display: false
                            },
                            tooltip: {
                                enabled: true,
                                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                                titleColor: '#00ff00',
                                bodyColor: '#ffffff',
                                borderColor: '#00ff00',
                                borderWidth: 1
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                max: 30,
                                title: {
                                    display: true,
                                    text: '球速 (m/s)',
                                    color: '#00ff00',
                                    font: {
                                        family: 'Orbitron',
                                        size: 12
                                    }
                                },
                                ticks: {
                                    color: '#00ff00',
                                    font: {
                                        family: 'Orbitron',
                                        size: 10
                                    }
                                },
                                grid: {
                                    color: 'rgba(0, 255, 0, 0.2)',
                                    lineWidth: 1
                                }
                            },
                            x: {
                                display: false,
                                grid: {
                                    color: 'rgba(0, 255, 0, 0.1)',
                                    lineWidth: 1
                                }
                            }
                        }
                    }
                });
            }
            
            bindEvents() {
                document.getElementById('startSimulation').addEventListener('click', () => this.startSimulation());
                document.getElementById('stopSimulation').addEventListener('click', () => this.stopSimulation());
                document.getElementById('clearData').addEventListener('click', () => this.clearData());
                document.getElementById('randomPulse').addEventListener('click', () => this.addRandomPulse());
            }
            
            startSimulation() {
                if (this.simulationInterval) return;
                
                this.simulationInterval = setInterval(() => {
                    // 生成模拟球速数据（0-25 m/s）
                    const baseSpeed = Math.random() * 15 + 2; // 2-17 m/s 基础速度
                    const noise = (Math.random() - 0.5) * 4; // ±2 m/s 噪声
                    const speed = Math.max(0, baseSpeed + noise);
                    
                    this.addDataPoint(speed);
                }, 50); // 每50ms添加一个数据点
                
                console.log('🎾 开始球速模拟');
            }
            
            stopSimulation() {
                if (this.simulationInterval) {
                    clearInterval(this.simulationInterval);
                    this.simulationInterval = null;
                    console.log('⏹️ 停止球速模拟');
                }
            }
            
            clearData() {
                this.ecgData.timeLabels = [];
                this.ecgData.speedValues = [];
                this.ecgData.currentSpeed = 0;
                
                this.chart.data.labels = [];
                this.chart.data.datasets[0].data = [];
                this.chart.update();
                
                this.updateCurrentSpeedDisplay(0);
                console.log('🗑️ 清空心电图数据');
            }
            
            addRandomPulse() {
                // 添加一个高速脉冲
                const pulseSpeed = Math.random() * 10 + 20; // 20-30 m/s
                this.addDataPoint(pulseSpeed);
                console.log(`⚡ 添加随机脉冲: ${pulseSpeed.toFixed(1)} m/s`);
            }
            
            addDataPoint(speed) {
                const currentTime = Date.now();
                const relativeTime = (currentTime % 30000) / 1000;
                
                this.ecgData.timeLabels.push(relativeTime.toFixed(1));
                this.ecgData.speedValues.push(speed);
                
                // 保持数据点数量限制
                if (this.ecgData.timeLabels.length > this.ecgData.maxDataPoints) {
                    this.ecgData.timeLabels.shift();
                    this.ecgData.speedValues.shift();
                }
                
                this.ecgData.currentSpeed = speed;
                this.updateCurrentSpeedDisplay(speed);
                
                // 更新图表
                this.chart.data.labels = [...this.ecgData.timeLabels];
                this.chart.data.datasets[0].data = [...this.ecgData.speedValues];
                this.chart.update('none');
            }
            
            updateCurrentSpeedDisplay(speed) {
                const speedElement = document.getElementById('ecgCurrentSpeed');
                if (speedElement) {
                    speedElement.textContent = speed.toFixed(1);
                    
                    // 根据速度值改变颜色
                    if (speed > 20) {
                        speedElement.style.color = '#ff4444';
                        speedElement.style.textShadow = '0 0 6px rgba(255, 68, 68, 0.8)';
                    } else if (speed > 10) {
                        speedElement.style.color = '#ffaa00';
                        speedElement.style.textShadow = '0 0 6px rgba(255, 170, 0, 0.8)';
                    } else {
                        speedElement.style.color = '#00ff00';
                        speedElement.style.textShadow = '0 0 4px rgba(0, 255, 0, 0.5)';
                    }
                }
            }
        }
        
        // 初始化测试
        document.addEventListener('DOMContentLoaded', () => {
            new EcgTest();
        });
    </script>
</body>
</html>
