#pragma once

#include "CameraService.hpp"
#include "../Utils/ErrorManager.hpp"
#include <chrono>
#include <atomic>

/**
 * @brief 增强版相机服务，集成统一错误管理
 * 
 * 主要改进：
 * 1. 统一的异常处理和错误恢复
 * 2. 性能优化的错误记录
 * 3. 相机健康监控
 * 4. 自动重连机制
 */
class EnhancedCameraService : public CameraService {
private:
    // 错误恢复相关
    std::atomic<int> consecutive_failures_{0};
    std::chrono::steady_clock::time_point last_success_time_;
    static constexpr int MAX_CONSECUTIVE_FAILURES = 5;
    static constexpr std::chrono::seconds RECOVERY_TIMEOUT{30};
    
    // 性能监控
    std::atomic<uint64_t> total_frames_captured_{0};
    std::atomic<uint64_t> failed_frame_captures_{0};
    std::chrono::steady_clock::time_point service_start_time_;
    
    // 相机健康状态
    enum class CameraHealth {
        HEALTHY,
        DEGRADED,
        CRITICAL,
        OFFLINE
    };
    std::atomic<CameraHealth> camera_health_{CameraHealth::OFFLINE};
    
    // 错误恢复策略
    bool attemptCameraRecovery();
    void updateCameraHealth();
    void logCameraStatistics();
    
public:
    EnhancedCameraService();
    ~EnhancedCameraService();
    
    // 重写基类方法，添加错误处理
    cv::Mat getFrame(int camera_id) override;
    cv::Mat getNextFrame() override;
    bool areCamerasInitialized() const override;
    
    // 新增方法
    CameraHealth getCameraHealth() const { return camera_health_.load(); }
    double getSuccessRate() const;
    void resetStatistics();
    
    // 错误恢复接口
    bool recoverFromError();
    void enableAutoRecovery(bool enable);
    
protected:
    void initializeCameras() override;
    
private:
    bool auto_recovery_enabled_{true};
    std::mutex recovery_mutex_;
};

/**
 * @brief 相机操作结果包装器
 * 避免异常开销，提供类型安全的错误处理
 */
template<typename T>
class CameraResult {
private:
    bool success_;
    T value_;
    ErrorManagement::ErrorInfo error_info_;
    
public:
    CameraResult(T&& value) 
        : success_(true), value_(std::move(value)),
          error_info_(ErrorManagement::ErrorCategory::CAMERA_ERROR, 
                     ErrorManagement::ErrorSeverity::INFO, "") {}
    
    CameraResult(const ErrorManagement::ErrorInfo& error) 
        : success_(false), error_info_(error) {}
    
    bool isSuccess() const { return success_; }
    const T& getValue() const { return value_; }
    const ErrorManagement::ErrorInfo& getError() const { return error_info_; }
    
    T getValueOr(const T& default_value) const {
        return success_ ? value_ : default_value;
    }
};

using FrameResult = CameraResult<cv::Mat>;
using BoolResult = CameraResult<bool>;

/**
 * @brief 相机操作的RAII包装器
 * 自动处理错误上下文和性能监控
 */
class CameraOperation {
private:
    std::string operation_name_;
    std::chrono::steady_clock::time_point start_time_;
    EnhancedCameraService* service_;
    
public:
    CameraOperation(const std::string& operation_name, EnhancedCameraService* service)
        : operation_name_(operation_name), service_(service),
          start_time_(std::chrono::steady_clock::now()) {}
    
    ~CameraOperation() {
        auto duration = std::chrono::steady_clock::now() - start_time_;
        auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(duration).count();
        
        if (ms > 50) { // 相机操作超过50ms记录警告
            ErrorManagement::ErrorManager::getInstance().log(
                ErrorManagement::ErrorSeverity::WARNING,
                ErrorManagement::ErrorCategory::CAMERA_ERROR,
                "Camera operation '" + operation_name_ + "' took " + std::to_string(ms) + "ms",
                LOCATION_INFO
            );
        }
    }
};

#define CAMERA_OPERATION(name, service) CameraOperation _op(name, service)
