// 解决 WinSock.h 和 asio/winsock2.hpp 的冲突
#ifdef _WIN32
#define _WIN32_WINNT 0x0A00
#endif

#include "app_lifecycle.hpp"
#include "../Utils/utf8_utils.hpp"
#include "../Utils/DebugConfig.hpp"
#include <iostream>
#include <exception>
#include <csignal>   // For signal handling
#include <atomic>    // For std::atomic

// 前向声明
namespace DebugConfigPresets {
    void setDevelopmentMode();
    void setBallSpeedDebugMode();
    void setRecordingDebugMode();
    void setSummaryOnlyMode();
    void setProductionMode();
}

// 全局关闭标志，用于通知所有线程退出
std::atomic<bool> g_shutdown_flag(false);

void signal_handler(int signal) {
    if (signal == SIGINT) {
        UTF8Utils::println("\n接收到 Ctrl+C 信号，准备优雅关闭...");
        g_shutdown_flag.store(true);
    }
}

// ============================= 主函数 (高层调度器) =====================

int main() {
    UTF8Utils::initConsole();

    // 初始化调试配置 - 快球检测修复验证模式
    DebugConfig::enable_ball_speed_debug = true;   // 启用球速调试以验证修复效果
    DebugConfig::enable_highlight_debug = true;    // 启用精彩时刻调试
    DebugConfig::enable_recording_debug = false;   // 关闭录制调试，减少输出
    DebugConfig::enable_camera_sync_debug = true;  // 启用摄像头同步调试以监控时间戳
    DebugConfig::enable_frame_detection_debug = false; // 关闭帧检测调试
    DebugConfig::enable_3d_reconstruction_debug = true; // 启用3D重建调试以监控成功率
    DebugConfig::enable_web_server_debug = false; // 关闭Web服务调试
    DebugConfig::enable_roi_debug = false;        // 关闭ROI调试

    // 启用摘要模式，每5秒输出关键信息
    DebugConfig::setSummaryMode(true, 5);
    DebugConfig::setLogLevel(DebugConfig::INFO_LEVEL);

    std::cout << "🔧 调试配置已优化 - 启用摘要模式，减少终端信息过载" << std::endl;

    // 注册信号处理器以捕获 Ctrl+C
    signal(SIGINT, signal_handler);

    UTF8Utils::println("===== 统一视频检测与Web服务系统 (v2.0 并行架构) =====");
    UTF8Utils::println("系统启动中... 按 Ctrl+C 可随时关闭。");

    try {
        // 1. 初始化所有服务和共享数据
        auto context = initialize_services();
        if (!context) {
            UTF8Utils::println("错误：服务初始化失败，程序退出。");
            return -1;
        }

        // 将全局关闭标志与服务上下文关联
        context->shutdown_flag.store(g_shutdown_flag.load());
        std::thread flag_watcher([&]() {
            while (true) {
                if (g_shutdown_flag.load()) {
                    context->shutdown_flag.store(true);
                    break;
                }
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
            }
        });
        flag_watcher.detach();


        // 2. 启动Web服务器等后台服务
        start_background_services(*context);

        // 3. 为每个摄像头启动一个并行的处理流水线
        auto processing_threads = launch_processing_pipelines(*context);

        // 4. 等待所有处理线程结束 (程序将在此处阻塞)
        wait_for_shutdown(processing_threads, *context);

    } catch (const std::exception& e) {
        std::cerr << "捕获到未处理的顶层异常: " << e.what() << std::endl;
        return 1;
    }

    UTF8Utils::println("所有服务已关闭，程序退出。");
    return 0;
} 