---
description: 
globs: 
alwaysApply: true
---
# AI助手上下文备忘录 (general.mdc)

**注意**: 本文档已被重构为更详细的文档架构。请参考以下新的文档结构：

- **[docs/AI_CONTEXT.md](docs/AI_CONTEXT.md)** - AI助手专用的详细上下文文档
- **[docs/ARCHITECTURE.md](docs/ARCHITECTURE.md)** - 系统架构设计文档
- **[docs/DEVELOPMENT.md](docs/DEVELOPMENT.md)** - 开发指南和历程记录
- **[docs/TROUBLESHOOTING.md](docs/TROUBLESHOOTING.md)** - 问题解决指南

以下内容保留作为快速参考：

## 1. 项目核心

- **目标**: 一个基于C++20的、服务化的全自动乒乓球裁判系统。
- **架构**: 单一可执行文件，多服务化设计。服务之间完全解耦，通过一个中央的、线程安全的 `SharedData` 对象进行通信。
- **构建系统**: **CMake是唯一的真实来源**。要添加或移除项目文件，**必须**编辑根目录的 `CMakeLists.txt` 文件。Visual Studio的解决方案（`.sln`）是由CMake自动生成的。

## 2. 关键组件与文件职责

这是项目中最核心的文件及其角色的地图。

### A. 核心调度 (`Main/`)
- **`Main/main.cpp`**: 应用程序的入口点，逻辑极其简单，仅负责调用应用生命周期管理器。
- **`Main/app_lifecycle.cpp`/`.hpp`**: **真正的"主"逻辑所在地**。负责创建所有服务、注入依赖（如`SharedData`）、启动后台线程，并管理核心的处理循环。

### B. "数据总线" (`Utils/`)
- **`Utils/SharedData.hpp`**: **整个系统的心脏**。一个线程安全的、类似单例的容器，用于存放所有服务间共享的数据（视频帧、检测结果、三维坐标点等），以此杜绝服务间的直接调用。

### C. 核心服务 (`Services/`)
- **`Services/CameraService.cpp`/`.hpp`**: 管理所有相机硬件，是项目中唯一与相机SDK直接交互的模块。
- **`Services/InferenceService.cpp`/`.hpp`**: `Deploy`库的轻量级封装器，负责运行YOLO目标检测。
- **`Services/StereoReconstructionService.cpp`/`.hpp`**: 获取左右相机的2D检测结果，进行匹配，然后执行三角化计算得到三维坐标。它还在内部使用SG滤波器计算球速，并运行在自己的专用线程中。
- **`Services/RecordingService.cpp`/`.hpp`**: 在后台线程中处理高帧率的视频录制。采用生产者-消费者队列模型，并使用NVIDIA硬件编码(`h264_nvenc`)以保证性能。
- **`Services/DataLoggingService.cpp`/`.hpp`**: 在专用的后台线程中，将遥测数据（三维坐标、速度）异步写入SQLite数据库 (`Data/telemetry.db`)。
- **`Services/WebServerService.cpp`/`.hpp`**: 运行Crow Web服务器。负责托管前端静态文件，并通过WebSocket将实时数据（视频、检测结果、遥测数据）流式传输到UI。它还提供了`/api/db/query`端点用于前端查询数据库。

### D. AI与部署 (`Deploy/`)
- **`Deploy/yolo.hpp`**: 用于YOLO推理的高级封装类。`InferenceService` 直接使用此类。
- **`Deploy/models/`**: 存放TensorRT引擎文件 (`.engine`) 的位置。

### E. 前端 (`Web/`)
- **`Web/frontend/index.html`**: Web界面的主HTML结构。
- **`Web/frontend/style.css`**: UI的全部样式。采用一个现代化的、多层HUD布局。
- **`Web/frontend/simple-video.js`**: 核心前端JavaScript。负责处理WebSocket消息、渲染视频和2D/3D轨迹，并管理所有UI交互。

## 3. 核心架构原则

- **服务间绝对解耦**: **严禁服务间直接调用**。任何服务都不应持有其他服务的指针或引用。所有数据交换**必须**通过中央数据总线 `SharedData` 进行。
- **并行处理模型**: 系统的核心数据处理流程是**多线程并行**的。每个处理线程拥有**独立**的 `InferenceService` 实例，以实现无锁、高性能的推理。

## 4. 关键开发规则与"避坑"指南

- **文件路径至关重要**: 服务通常从`build/`目录启动。对于访问持久化数据（如数据库`telemetry.db`）或保存录像，**必须使用绝对路径或从已知基准目录解析的路径**。使用相对路径是"文件未找到"错误的常见根源。
- **如何添加文件**: 要将一个新的`.cpp`或`.h`文件加入到项目中，**必须**在根目录的 `CMakeLists.txt` 文件中手动添加它，然后重新运行CMake生成步骤。
- **线程模型**: `WebServerService`, `StereoReconstructionService`, `DataLoggingService`, 和 `RecordingService` 都在其**各自专用的后台线程**中运行。相机处理本身也拥有并行的工作线程（每个相机一个），这些都在`app_lifecycle.cpp`中管理。在修改代码时，必须警惕数据竞争，优先使用`SharedData`进行跨线程通信。
- **`cv::Mat`是智能指针**: `cv::Mat`内部使用引用计数机制。按值传递`cv::Mat`对象是低开销的，并且是避免复杂内存管理问题的首选方式。**避免**用`std::shared_ptr`来包装它。

## 5. 技术栈

- **核心语言**: C++20
- **构建系统**: CMake
- **Web框架**: Crow (内嵌)
- **AI推理**: TensorRT (通过 `Deploy/` 库封装)
- **数据库**: SQLite
- **图像处理**: OpenCV
- **JSON处理**: nlohmann/json

## 6. AI 助手协作指南

- **首要信息源**: 本 `general.mdc` 文件是我理解项目架构的首要参考。
- **遵循现有模式**: 我提供的代码建议**必须**符合已建立的服务化和并行处理架构。
- **响应语言**: 我将**始终使用中文**与您沟通。



