/*!
 * Chart.js v4.4.1
 * https://www.chartjs.org
 * (c) 2024 Chart.js Contributors
 * Released under the MIT License
 */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).Chart=e()}(this,(function(){"use strict";var t=Object.freeze({__proto__:null,get Colors(){return Uo},get Decimation(){return $o},get Filler(){return ca},get Legend(){return da},get SubTitle(){return ma},get Title(){return pa},get Tooltip(){return ka}});return class{static register(...e){Zt.add(...e)}static unregister(...e){Zt.remove(...e)}constructor(t,e){const i=this.config=new Yt(e),s=Rt(t),n=s&&Bt(s);if(!n)throw new Error("Canvas element and canvas 2D context must be available");this.platform=new(i.platform||class{acquireContext(t){return t&&t.getContext&&t.getContext("2d")||null}releaseContext(){return!0}addEventListener(){}removeEventListener(){}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(t,e,i,s){return Nt(t,e,i,s)}isAttached(t){const e=t&&t.canvas;return!(!e||!e.offsetParent||"none"===e.offsetParent.style.display)}}),this.canvas=s,this.ctx=n,this.data=i.data,this.options=i.options||{},this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new Kt,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=function(t){let e,i=!1,s=!1;return function(...n){e=n,i||(i=!0,s&&(s=!1,t.apply(this,e)),requestAnimationFrame((()=>{i=!1,s&&t.apply(this,e)})))}}((t=>{const e=this.options.onResize;e&&e.call(this,this,t),this.resize(t)})),this._dataChanges=[],Zt.notify(this,"construct"),this.initialize()}get aspectRatio(){const{options:{aspectRatio:t,maintainAspectRatio:e},width:i,height:s,_aspectRatio:n}=this;return l(t)?e&&n?n:s?i/s:null:t}get data(){return this.config.data}set data(t){this.config.data=t}get options(){return this.config.options}set options(t){this.config.options=t}get registry(){return Zt}initialize(){return this._initialize(),this.notifyPlugins("afterInit"),this.options.responsive&&this.resize(),this}clear(){return Tt(this.canvas,this.ctx),this}stop(){return Ht.stop(this),this}resize(t){Ht.running(this)||this._resize(t),this._layers.forEach((t=>{t._configure()}))}show(t,e){return this._show(t,e,!0)}hide(t,e){return this._show(t,e,!1)}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(t){const e=this.data.datasets[t];if(!e)return!1;const i=this.getDatasetMeta(t);return"boolean"==typeof i.hidden?!i.hidden:!e.hidden}setDatasetVisibility(t,e){this.getDatasetMeta(t).hidden=!e}toggleDataVisibility(t){this._hiddenIndices[t]=!this._hiddenIndices[t]}getDataVisibility(t){return!this._hiddenIndices[t]}_updateVisibility(t,e,i){const s=i?"show":"hide",n=this.getDatasetMeta(t),o=n.data||[];n[s](),o.forEach((t=>{t[s]()}))}setActiveElements(t){const e=this._active;this._active=t.map((({datasetIndex:t,index:e})=>{const i=this.getDatasetMeta(t);if(!i)throw new Error("No dataset found at index "+t);return{datasetIndex:t,element:i.data[e],index:e}})),!f(e,this._active)&&(this._lastEvent=null,this._updateHoverStyles(this._active,e))}notifyPlugins(t,e,i){return this._plugins.notify(this,t,e,i)}isPluginEnabled(t){return 1===this._plugins._cache.filter((e=>e.plugin.id===t)).length}_updateHoverStyles(t,e,i){const s=this.options.hover,n=(t,e)=>t.filter((t=>!e.some((e=>t.datasetIndex===e.datasetIndex&&t.index===e.index)))),o=n(e,t),a=i?t:n(t,e);o.forEach((t=>{const e=this.getDatasetMeta(t.datasetIndex).data[t.index];e&&this._updateStyle(e,t.index,!1)})),a.forEach((t=>{const e=this.getDatasetMeta(t.datasetIndex).data[t.index];e&&this._updateStyle(e,t.index,!0)}))}getActiveElements(){return this._active||[]}_show(t,e,i){const s=this.getSortedVisibleDatasetMetas(),n=this.getDatasetMeta(t);if(o(t)&&s.length)s.forEach((t=>{this._updateVisibility(t.index,e,i)}));else{if(!n)return;this._updateVisibility(t,e,i)}this.update("none"),this.render()}destroy(){this.notifyPlugins("beforeDestroy");const{canvas:t,ctx:e}=this;this.stop(),this.platform.releaseContext(e),this.canvas=null,this.ctx=null,this.data=null,this.options=null,Zt.notify(this,"destroy"),delete Wt[this.id],t&&Tt(t,e)}toBase64Image(...t){return this.canvas.toDataURL(...t)}getElementsAtEventForMode(t,e,i,s){const n=te[e];return"function"==typeof n?n(this,t,i,s):[]}getDatasetMeta(t){const e=this.data.datasets[t],i=this._metasets;let s=i.filter((e=>e&&e._dataset===e)).pop();return s||(s={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:e&&e.order||0,index:t,_dataset:e,_parsed:[],_sorted:!1},i[t]=s),s}getContext(){return this.$context||(this.$context=Ci(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(t){const e=this.data.datasets[t];if(!e)return!1;const i=this.getDatasetMeta(t);return"boolean"==typeof i.hidden?!i.hidden:!e.hidden}getSortedVisibleDatasetMetas(){const t=this._sortedMetasets,e=[];let i,s;for(i=0,s=t.length;i<s;++i){const s=t[i];this.isDatasetVisible(s.index)&&e.push(s)}return e}updateHoverStyle(t,e,i){const s=i?"set":"remove";let n,o,a,r;switch(e){case"dataset":n=this.getDatasetMeta(t.datasetIndex),n&&this._updateStyle(n.dataset,void 0,i);break;case"data":n=this.getDatasetMeta(t.datasetIndex),n&&n.data&&this._updateStyle(n.data[t.index],t.index,i);break;default:n=this.getDatasetMeta(t.datasetIndex),n&&n.data&&(o=n.data[t.index],a=o&&o.$context,r=a&&a.parsed,r&&r.length&&this._updateStyle(o,t.index,i))}}getDatasetAtEvent(t){return this.getElementsAtEventForMode(t,"dataset",{intersect:!0},!1)}getElementsAtEvent(t){return this.getElementsAtEventForMode(t,"nearest",{intersect:!0},!1)}getElementsAtXAxis(t){return this.getElementsAtEventForMode(t,"index",{intersect:!1},!1)}getElementsAtY(t){return this.getElementsAtEventForMode(t,"dataset",{intersect:!1},!1)}_initialize(){this.notifyPlugins("beforeInit"),this.options.responsive&&this.platform.isAttached(this.canvas)&&this.resize(),this._initializePlatform(),this._initializeController(),this._initializeCanvas(),this.ensureScalesHaveIDs(),this._buildOrUpdateScales(),this._initializeMetasets(),this.reset(),this.notifyPlugins("afterInit")&&this.options.animation&&this.render()}_initializePlatform(){this._layers=[],this.platform.acquireContext(this.ctx,this.options),this.canvas.addEventListener&&this.canvas.addEventListener("resize",this._doResize),this._responsiveListeners=this.platform.isAttached(this.canvas)}_initializeController(){this.data.datasets.forEach(((t,e)=>{this.getDatasetMeta(e).type=t.type||this.config.type})),this._updateMetasets(),this.data.datasets.forEach(((t,e)=>{this.getDatasetMeta(e).controller||this._updateController(e)}))}}
