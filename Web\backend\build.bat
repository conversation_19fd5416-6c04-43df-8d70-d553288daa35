@echo off
echo Setting UTF-8 encoding...
chcp 65001 >nul 2>&1

echo.
echo ===== 双目视觉检测Web服务器构建脚本 =====
echo.

REM 清理旧的构建文件
if exist "build" (
    echo 清理旧构建文件...
    rmdir /s /q "build" 2>nul
)

echo 创建构建目录...
mkdir build

cd build

echo 配置CMake...
cmake -G "Visual Studio 17 2022" -A x64 -S .. -B .

if %errorlevel% neq 0 (
    echo.
    echo CMake配置失败！
    echo 请检查：
    echo   1. Visual Studio 2022已安装
    echo   2. Crow和asio路径正确
    echo   3. CMake在PATH中
    echo.
    cd ..
    pause
    exit /b 1
)

echo 编译项目...
cmake --build . --config Release

if %errorlevel% neq 0 (
    echo.
    echo 编译失败！
    echo 可能原因：
    echo   1. 缺少必要库文件
    echo   2. 编译器配置问题  
    echo   3. 源代码错误
    echo.
    cd ..
    pause
    exit /b 1
)

echo.
echo ===== 编译成功！ =====
echo 可执行文件位置: build\camera_web_server.exe
echo.

cd ..

echo 运行服务器请执行:
echo   cd Web
echo   start_server.bat
echo.
pause 