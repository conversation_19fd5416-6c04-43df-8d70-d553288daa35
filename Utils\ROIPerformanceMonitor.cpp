#include "ROIPerformanceMonitor.hpp"
#include <iomanip>
#include <sstream>

ROIPerformanceMonitor::ROIPerformanceMonitor() 
    : m_startTime(std::chrono::steady_clock::now()) {
}

void ROIPerformanceMonitor::recordROIPrediction(bool left_success, bool right_success, 
                                               bool stereo_success, double conversion_time, 
                                               double generation_time) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    m_metrics.total_predictions++;
    if (left_success) m_metrics.successful_left_roi++;
    if (right_success) m_metrics.successful_right_roi++;
    if (stereo_success) m_metrics.stereo_matching_success++;
    
    // 更新移动平均
    updateMovingAverage(m_metrics.avg_geometry_conversion_time, conversion_time);
    updateMovingAverage(m_metrics.avg_roi_generation_time, generation_time);
}

void ROIPerformanceMonitor::recordInference(bool is_roi_inference, double inference_time) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (is_roi_inference) {
        m_metrics.roi_inference_count++;
        updateMovingAverage(m_metrics.avg_roi_inference_time, inference_time);
    } else {
        m_metrics.fullframe_inference_count++;
    }
}

ROIPerformanceMonitor::PerformanceMetrics ROIPerformanceMonitor::getMetrics() const {
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_metrics;
}

void ROIPerformanceMonitor::printPerformanceReport() const {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    auto now = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(now - m_startTime);
    
    std::stringstream ss;
    ss << std::fixed << std::setprecision(2);
    
    ss << "\n=== 🎯 动态ROI性能报告 ===\n";
    ss << "运行时间: " << duration.count() << " 秒\n";
    ss << "总预测次数: " << m_metrics.total_predictions << "\n";
    ss << "左摄像头ROI成功率: " << m_metrics.getLeftROISuccessRate() << "%\n";
    ss << "右摄像头ROI成功率: " << m_metrics.getRightROISuccessRate() << "%\n";
    ss << "立体匹配成功率: " << m_metrics.getStereoMatchingSuccessRate() << "%\n";
    ss << "ROI推理占比: " << m_metrics.getROIInferenceRatio() << "%\n";
    ss << "平均几何转换时间: " << m_metrics.avg_geometry_conversion_time << "ms\n";
    ss << "平均ROI生成时间: " << m_metrics.avg_roi_generation_time << "ms\n";
    ss << "平均ROI推理时间: " << m_metrics.avg_roi_inference_time << "ms\n";
    
    // 性能评估
    if (isPerformanceTargetMet()) {
        ss << "✅ 性能目标达成\n";
    } else {
        ss << "⚠️ 性能目标未达成\n";
    }
    
    ss << "========================\n";
    
    UTF8Utils::println(ss.str());
}

void ROIPerformanceMonitor::reset() {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_metrics = PerformanceMetrics();
    m_startTime = std::chrono::steady_clock::now();
}

bool ROIPerformanceMonitor::isPerformanceTargetMet() const {
    // 性能目标：
    // 1. 左右摄像头ROI成功率 > 70%
    // 2. 立体匹配成功率 > 60%
    // 3. ROI推理占比 > 50%
    // 4. 平均ROI推理时间 < 30ms
    
    return m_metrics.getLeftROISuccessRate() > 70.0 &&
           m_metrics.getRightROISuccessRate() > 70.0 &&
           m_metrics.getStereoMatchingSuccessRate() > 60.0 &&
           m_metrics.getROIInferenceRatio() > 50.0 &&
           m_metrics.avg_roi_inference_time < 30.0;
}

void ROIPerformanceMonitor::updateMovingAverage(double& avg, double new_value, double alpha) {
    avg = alpha * new_value + (1.0 - alpha) * avg;
}
