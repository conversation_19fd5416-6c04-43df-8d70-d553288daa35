#include "InferenceService.hpp"
#include "../Utils/utf8_utils.hpp"

// 构造函数：创建Yolo实例
InferenceService::InferenceService(const std::string& engine_path, const std::string& class_names_path) {
    try {
        m_yolo = std::make_unique<Yolo>(engine_path, class_names_path);
        UTF8Utils::println("Yolo object created successfully using provided Deploy library.");
    } catch (const std::exception& e) {
        UTF8Utils::println("Error creating Yolo object: " + std::string(e.what()));
    }
}

// Private constructor for cloning
InferenceService::InferenceService(std::unique_ptr<Yolo> yolo) : m_yolo(std::move(yolo)) {}

// Clone method implementation
std::unique_ptr<InferenceService> InferenceService::clone() const {
    if (!m_yolo) {
        return nullptr;
    }
    auto yolo_clone = m_yolo->clone();
    return std::unique_ptr<InferenceService>(new InferenceService(std::move(yolo_clone)));
}

// 析构函数：unique_ptr会自动管理Yolo对象的生命周期
// 但由于Yolo是不完整类型，需要在这里提供一个空的实现
InferenceService::~InferenceService() = default;

// 核心功能：调用Yolo库进行推理，并转换结果
DetectionResult InferenceService::processFrame(const cv::Mat& frame, float conf_threshold) {
    // std::lock_guard<std::mutex> lock(m_mutex); // No longer needed, each thread will have its own instance

    DetectionResult service_result;
    if (!m_yolo) {
        return service_result;
    }

    // 调用您提供的Yolo库的推理接口，并传入动态的置信度阈值
    std::map<std::string, std::vector<Yolo::Detection>> yolo_result = m_yolo->inference(frame, conf_threshold);

    // 将Yolo库的返回结果转换为我们项目内部统一的DetectionResult格式
    for (const auto& pair : yolo_result) {
        const std::string& class_name = pair.first;
        const std::vector<Yolo::Detection>& detections = pair.second;

        for (const auto& det : detections) {
            service_result.boxes.emplace_back(cv::Rect(cv::Point(det.left, det.top), cv::Point(det.right, det.bottom)));
            service_result.confidences.push_back(det.conf);
            service_result.class_ids.push_back(det.class_id);
            service_result.class_names.push_back(class_name);
        }
    }
    
    return service_result;
}

// 新增：返回原始YOLO结果和转换后结果的方法
InferenceResult InferenceService::processFrameWithRaw(const cv::Mat& frame, float conf_threshold) {
    InferenceResult result;
    
    if (!m_yolo) {
        return result;
    }

    // 调用YOLO推理
    std::map<std::string, std::vector<Yolo::Detection>> yolo_result = m_yolo->inference(frame, conf_threshold);
    
    // 保存原始结果
    result.raw_yolo_detections = yolo_result;

    // 将Yolo库的返回结果转换为DetectionResult格式
    for (const auto& pair : yolo_result) {
        const std::string& class_name = pair.first;
        const std::vector<Yolo::Detection>& detections = pair.second;

        for (const auto& det : detections) {
            result.detection_result.boxes.emplace_back(cv::Rect(cv::Point(det.left, det.top), cv::Point(det.right, det.bottom)));
            result.detection_result.confidences.push_back(det.conf);
            result.detection_result.class_ids.push_back(det.class_id);
            result.detection_result.class_names.push_back(class_name);
        }
    }
    
    return result;
}

InferenceResult InferenceService::processFrameWithROI(const cv::Mat& frame, int camera_id,
                                                     float conf_threshold,
                                                     std::shared_ptr<SharedData> shared_data) {
    InferenceResult result;
    if (!m_yolo) {
        return result;
    }

    try {
        // 检查是否启用ROI模式且安全开关允许
        if (shared_data->isROIProcessingEnabled() && shouldUseROI()) {
            // 获取预测的ROI
            cv::Rect predicted_roi = shared_data->getLatestROI(camera_id);

            if (predicted_roi.area() > 100) {  // ROI有效
                // ROI推理
                cv::Mat roi_frame = frame(predicted_roi);
                std::map<std::string, std::vector<Yolo::Detection>> yolo_result =
                    m_yolo->inference(roi_frame, conf_threshold * 0.8f);  // 降低阈值

                // 将ROI坐标转换回全画面坐标
                adjustDetectionCoordinates(yolo_result, predicted_roi);

                // 保存原始结果
                result.raw_yolo_detections = yolo_result;

                // 转换为DetectionResult格式
                for (const auto& pair : yolo_result) {
                    const std::string& class_name = pair.first;
                    const std::vector<Yolo::Detection>& detections = pair.second;

                    for (const auto& det : detections) {
                        result.detection_result.boxes.emplace_back(cv::Rect(
                            cv::Point(det.left, det.top), cv::Point(det.right, det.bottom)));
                        result.detection_result.confidences.push_back(det.conf);
                        result.detection_result.class_ids.push_back(det.class_id);
                        result.detection_result.class_names.push_back(class_name);
                    }
                }

                DEBUG_ROI("相机" + std::to_string(camera_id) + " ROI推理成功，检测到 " +
                         std::to_string(result.detection_result.boxes.size()) + " 个目标");
                return result;
            }
        }

        // 回退到全画面推理
        return processFrameWithRaw(frame, conf_threshold);

    } catch (const std::exception& e) {
        m_roiFailureCount++;
        UTF8Utils::println("⚠️ ROI推理失败，回退到全画面推理: " + std::string(e.what()));
        return processFrameWithRaw(frame, conf_threshold);
    }
}

void InferenceService::setROISafetySwitch(bool enabled) {
    m_roiSafetyEnabled = enabled;
    if (enabled) {
        m_roiFailureCount = 0;  // 重置失败计数
        UTF8Utils::println("✅ ROI安全开关已启用");
    } else {
        UTF8Utils::println("⚠️ ROI安全开关已禁用");
    }
}

void InferenceService::adjustDetectionCoordinates(std::map<std::string, std::vector<Yolo::Detection>>& detections,
                                                 const cv::Rect& roi) {
    for (auto& [class_name, detection_list] : detections) {
        for (auto& detection : detection_list) {
            // 将ROI内的坐标转换为全画面坐标
            detection.left += roi.x;
            detection.top += roi.y;
            detection.right += roi.x;
            detection.bottom += roi.y;
        }
    }
}

bool InferenceService::shouldUseROI() const {
    if (!m_roiSafetyEnabled) return false;
    if (m_roiFailureCount > MAX_ROI_FAILURES) {
        UTF8Utils::println("⚠️ ROI功能已自动禁用，失败次数过多: " + std::to_string(m_roiFailureCount));
        return false;
    }
    return true;
}