#pragma once

#include <vector>
#include <deque>
#include <stdexcept>
#include <numeric>
#include <cmath>
#include <algorithm> // For std::min/max

// Include the Point3f definition (adjust path if necessary)
// Assuming it's defined in a file like this relative to Main/
#include "../Utils/math_utils.hpp" 

namespace SignalProcessing {

    // --- Internal Helper: Matrix operations for coefficient calculation ---
    namespace Detail {
        // Basic Matrix class (simplified)
        class Matrix {
        public:
            Matrix(int rows, int cols) : rows_(rows), cols_(cols), data_(rows * cols) {}

            double& operator()(int r, int c) {
                return data_[r * cols_ + c];
            }
            const double& operator()(int r, int c) const {
                return data_[r * cols_ + c];
            }
            int rows() const { return rows_; }
            int cols() const { return cols_; }
            std::vector<double>& getData() { return data_; } // Added getter
            const std::vector<double>& getData() const { return data_; } // Added getter

            // Simple transpose
            Matrix transpose() const {
                Matrix result(cols_, rows_);
                for (int i = 0; i < rows_; ++i) {
                    for (int j = 0; j < cols_; ++j) {
                        result(j, i) = (*this)(i, j);
                    }
                }
                return result;
            }

            // Simple matrix multiplication
            Matrix multiply(const Matrix& other) const {
                if (cols_ != other.rows()) {
                    throw std::runtime_error("Matrix dimensions mismatch for multiplication");
                }
                Matrix result(rows_, other.cols());
                for (int i = 0; i < rows_; ++i) {
                    for (int j = 0; j < other.cols(); ++j) {
                        result(i, j) = 0.0;
                        for (int k = 0; k < cols_; ++k) {
                            result(i, j) += (*this)(i, k) * other(k, j);
                        }
                    }
                }
                return result;
            }

            // Simple Gaussian elimination based inverse (for small matrices)
            // WARNING: This is a basic implementation, not robust for ill-conditioned matrices.
            Matrix inverse() const {
                if (rows_ != cols_) {
                    throw std::runtime_error("Matrix must be square for inversion");
                }
                int n = rows_;
                Matrix augmented(n, 2 * n);
                for (int i = 0; i < n; ++i) {
                    for (int j = 0; j < n; ++j) {
                        augmented(i, j) = (*this)(i, j);
                    }
                    augmented(i, i + n) = 1.0; // Identity matrix part
                }

                // Forward elimination
                for (int i = 0; i < n; ++i) {
                    // Find pivot
                    int pivot = i;
                    for (int j = i + 1; j < n; ++j) {
                        if (std::abs(augmented(j, i)) > std::abs(augmented(pivot, i))) {
                            pivot = j;
                        }
                    }
                    if (std::abs(augmented(pivot, i)) < 1e-10) {
                         throw std::runtime_error("Matrix is singular, cannot invert");
                    }
                    // Swap rows
                    if (pivot != i) {
                        for (int k = 0; k < 2 * n; ++k) {
                            std::swap(augmented(i, k), augmented(pivot, k));
                        }
                    }

                    // Normalize row i
                    double div = augmented(i, i);
                    for (int k = i; k < 2 * n; ++k) {
                        augmented(i, k) /= div;
                    }

                    // Eliminate other rows
                    for (int j = 0; j < n; ++j) {
                        if (i != j) {
                            double mult = augmented(j, i);
                            for (int k = i; k < 2 * n; ++k) {
                                augmented(j, k) -= mult * augmented(i, k);
                            }
                        }
                    }
                }

                // Extract inverse
                Matrix result(n, n);
                for (int i = 0; i < n; ++i) {
                    for (int j = 0; j < n; ++j) {
                        result(i, j) = augmented(i, j + n);
                    }
                }
                return result;
            }

        private:
            int rows_, cols_;
            std::vector<double> data_;
        };

    } // namespace Detail

    /**
     * @brief Computes the Savitzky-Golay filter coefficients.
     * @param window_size The size of the filter window (must be a positive odd integer).
     * @param poly_order The order of the polynomial to fit (must be non-negative and less than window_size).
     * @param derivative_order The order of the derivative to compute (0 for smoothing). Default is 0.
     * @return A vector containing the filter coefficients.
     * @throws std::invalid_argument if parameters are invalid.
     * @throws std::runtime_error if matrix inversion fails.
     */
    inline std::vector<double> compute_sg_coeffs(
        int window_size, 
        int poly_order, 
        int derivative_order = 0)
    {
        if (window_size <= 0 || window_size % 2 == 0) {
            throw std::invalid_argument("Window size must be a positive odd integer.");
        }
        if (poly_order < 0 || poly_order >= window_size) {
            throw std::invalid_argument("Polynomial order must be non-negative and less than window size.");
        }
        if (derivative_order < 0 || derivative_order > poly_order) {
            throw std::invalid_argument("Derivative order must be non-negative and no larger than polynomial order.");
        }

        int half_window = window_size / 2;
        
        // Create the Vandermonde-like matrix X
        Detail::Matrix X(window_size, poly_order + 1);
        for (int i = 0; i < window_size; ++i) {
            for (int j = 0; j <= poly_order; ++j) {
                X(i, j) = std::pow(static_cast<double>(i - half_window), j);
            }
        }
        
        // Compute (X^T * X)^-1 * X^T
        Detail::Matrix Xt = X.transpose();
        Detail::Matrix XtX = Xt.multiply(X);
        Detail::Matrix XtX_inv = XtX.inverse(); // Can throw if singular
        Detail::Matrix FilterMatrix = XtX_inv.multiply(Xt);

        // The coefficients for the derivative_order at time t=0 (center of window)
        // correspond to the (derivative_order)-th row of the FilterMatrix.
        std::vector<double> coeffs(window_size);
        double factorial = 1.0;
        if (derivative_order > 0) {
            for(int i=1; i <= derivative_order; ++i) factorial *= i;
        }
        
        for (int i = 0; i < window_size; ++i) {
            coeffs[i] = FilterMatrix(derivative_order, i) * factorial;
        }

        return coeffs;
    }

    /**
     * @brief Applies the Savitzky-Golay filter to a 1D data sequence.
     *
     * This version applies the filter to the central part of the data where a full window is available.
     * Boundary points (first and last half_window points) are currently returned unmodified.
     *
     * @param data The input data sequence.
     * @param window_size The size of the filter window (must be a positive odd integer).
     * @param poly_order The order of the polynomial to fit (must be non-negative and less than window_size).
     * @return A vector containing the filtered data.
     * @throws std::invalid_argument if parameters are invalid or data size is too small.
     */
    inline std::vector<double> sg_filter(const std::vector<double>& data, int window_size, int poly_order) {
        if (window_size <= 0 || window_size % 2 == 0) {
            throw std::invalid_argument("Window size must be a positive odd integer.");
        }
        if (poly_order < 0 || poly_order >= window_size) {
            throw std::invalid_argument("Polynomial order must be non-negative and less than window size.");
        }
        if (data.size() < window_size) {
            // Not enough data for a full window - return original data as a simple fallback
            // Consider throwing an error or implementing specific boundary handling if needed.
            // For now, just return a copy to avoid modifying the original if it was passed by ref elsewhere.
             return data; 
        }

        std::vector<double> smoothed_data = data; // Initialize with original data
        std::vector<double> coeffs = compute_sg_coeffs(window_size, poly_order, 0); // 0 for smoothing
        int half_window = window_size / 2;

        // Apply filter only to the central part where a full window is available
        for (size_t i = half_window; i < data.size() - half_window; ++i) {
            double sum = 0.0;
            for (int j = 0; j < window_size; ++j) {
                // Ensure indices are valid (should be, due to loop bounds)
                size_t data_idx = i - half_window + j;
                if (data_idx < data.size()) { // Redundant check given loop bounds, but safe
                     sum += data[data_idx] * coeffs[j];
                }
            }
            smoothed_data[i] = sum;
        }

        // Boundary points (first/last half_window) remain unmodified in smoothed_data

        return smoothed_data;
    }

    /**
     * @brief Applies the Savitzky-Golay filter to a history of 3D points.
     *
     * Takes the last `window_size` points from the history, applies the 1D SG filter
     * to each coordinate (X, Y, Z), and returns the smoothed 3D point corresponding
     * to the *center* of that window (i.e., the point at index `history.size() - 1 - half_window`).
     *
     * @param history A deque containing the history of MU::Point3f points.
     * @param window_size The size of the filter window (must be a positive odd integer).
     * @param poly_order The order of the polynomial to fit (must be non-negative and less than window_size).
     * @return The smoothed MU::Point3f corresponding to the center of the applied window.
     * @throws std::invalid_argument if parameters are invalid or history size is less than window_size.
     */
    inline MU::Point3f apply_sg_filter_to_history(
        const std::deque<MU::Point3f>& history,
        int window_size,
        int poly_order)
    {
        if (history.size() < window_size) {
            throw std::invalid_argument("History size must be at least window_size for filtering.");
        }
        if (window_size <= 0 || window_size % 2 == 0) {
            throw std::invalid_argument("Window size must be a positive odd integer.");
        }
        if (poly_order < 0 || poly_order >= window_size) {
            throw std::invalid_argument("Polynomial order must be non-negative and less than window size.");
        }

        std::vector<double> x_data, y_data, z_data;
        x_data.reserve(window_size);
        y_data.reserve(window_size);
        z_data.reserve(window_size);

        // Extract the last 'window_size' points for filtering
        // Using iterators starting from the end minus window_size
        auto start_it = history.end() - window_size;
        for (auto it = start_it; it != history.end(); ++it) {
            x_data.push_back(static_cast<double>(it->x));
            y_data.push_back(static_cast<double>(it->y));
            z_data.push_back(static_cast<double>(it->z));
        }

        // Apply 1D filter to each coordinate
        // sg_filter will handle boundary conditions internally (currently by returning original data at ends)
        std::vector<double> smoothed_x = sg_filter(x_data, window_size, poly_order);
        std::vector<double> smoothed_y = sg_filter(y_data, window_size, poly_order);
        std::vector<double> smoothed_z = sg_filter(z_data, window_size, poly_order);

        // Return the smoothed point corresponding to the center of the window
        // The center of the window corresponds to the element at index `half_window`
        // in the `smoothed_*` vectors (which have size `window_size`).
        int center_index = window_size / 2;
        
        if (center_index >= smoothed_x.size() || center_index >= smoothed_y.size() || center_index >= smoothed_z.size()){
             throw std::runtime_error("Smoothed data size mismatch, cannot access center index.");
        }

        // Cast back to float if MU::Point3f uses floats
        return MU::Point3f(static_cast<float>(smoothed_x[center_index]), 
                           static_cast<float>(smoothed_y[center_index]), 
                           static_cast<float>(smoothed_z[center_index]));
    }
    
    /**
     * SGFilter 类提供了一个方便的Savitzky-Golay滤波器接口，
     * 用于平滑1D数据序列
     */
    class SGFilter {
    public:
        /**
         * @brief 构造一个Savitzky-Golay滤波器
         * @param window_size 滤波窗口大小（必须是正奇数）
         * @param poly_order 多项式阶数（必须是非负数且小于window_size）
         */
        SGFilter(int window_size, int poly_order) 
            : window_size_(window_size), poly_order_(poly_order) {
            // 验证参数
            if (window_size <= 0 || window_size % 2 == 0) {
                throw std::invalid_argument("Window size must be a positive odd integer.");
            }
            if (poly_order < 0 || poly_order >= window_size) {
                throw std::invalid_argument("Polynomial order must be non-negative and less than window size.");
            }
            
            // 预先计算滤波系数
            coeffs_ = compute_sg_coeffs(window_size, poly_order, 0);
        }
        
        /**
         * @brief 对数据序列应用滤波
         * @param data 输入数据序列
         * @return 滤波后的数据序列
         */
        std::vector<float> filter(const std::vector<float>& data) const {
            // 如果数据不足一个窗口，返回原始数据
            if (data.size() < window_size_) {
                return data;
            }
            
            // 转换为double进行滤波
            std::vector<double> double_data(data.begin(), data.end());
            std::vector<double> filtered = sg_filter(double_data, window_size_, poly_order_);
            
            // 转换回float返回
            return std::vector<float>(filtered.begin(), filtered.end());
        }
        
        /**
         * @brief 获取窗口大小
         * @return 窗口大小
         */
        int getWindowSize() const {
            return window_size_;
        }
        
        /**
         * @brief 获取多项式阶数
         * @return 多项式阶数
         */
        int getPolyOrder() const {
            return poly_order_;
        }
        
    private:
        int window_size_;   // 窗口大小
        int poly_order_;    // 多项式阶数
        std::vector<double> coeffs_; // 预计算的滤波系数
    };

} // namespace SignalProcessing 