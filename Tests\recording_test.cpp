#include "../Services/RecordingService.hpp"
#include "../Services/CameraService.hpp"
#include "../Utils/SharedData.hpp"
#include <iostream>
#include <chrono>
#include <thread>
#include <vector>
#include <opencv2/opencv.hpp>

/**
 * 录制功能测试程序
 * 用于验证帧重复bug修复效果和双摄像头同步性能
 */

class RecordingTester {
private:
    std::unique_ptr<RecordingService> recording_service_;
    std::unique_ptr<CameraService> camera_service_;
    std::shared_ptr<SharedData> shared_data_;
    
    // 测试统计
    struct TestStats {
        int total_frames_pushed = 0;
        int duplicate_frames_detected = 0;
        std::chrono::steady_clock::time_point start_time;
        std::chrono::steady_clock::time_point end_time;
        std::map<int, std::vector<std::chrono::steady_clock::time_point>> frame_timestamps;
    };
    
    TestStats stats_;

public:
    RecordingTester() {
        // 初始化服务
        recording_service_ = std::make_unique<RecordingService>("C:/Dev/Camera_Editor/Data/test_recordings");
        camera_service_ = std::make_unique<CameraService>();
        shared_data_ = std::make_shared<SharedData>();
        
        // 启用双目同步
        recording_service_->enableStereoSync(true);
        
        std::cout << "🧪 录制测试器初始化完成" << std::endl;
    }
    
    /**
     * 模拟摄像头帧推送测试
     */
    void simulateFramePushing(int duration_seconds = 10) {
        std::cout << "📹 开始模拟帧推送测试 (持续 " << duration_seconds << " 秒)" << std::endl;
        
        stats_.start_time = std::chrono::steady_clock::now();
        
        // 为两个摄像头启动录制
        recording_service_->startRecording(1);
        recording_service_->startRecording(2);
        
        // 模拟210 FPS的帧推送
        auto frame_interval = std::chrono::microseconds(1000000 / 210); // 210 FPS
        auto test_end_time = stats_.start_time + std::chrono::seconds(duration_seconds);
        
        int frame_counter = 0;
        while (std::chrono::steady_clock::now() < test_end_time) {
            auto frame_time = std::chrono::steady_clock::now();
            
            // 为每个摄像头创建测试帧
            for (int camera_id = 1; camera_id <= 2; ++camera_id) {
                // 创建带有时间戳信息的测试帧
                cv::Mat test_frame = createTestFrame(camera_id, frame_counter);
                
                // 推送帧到录制服务
                recording_service_->pushFrame(camera_id, test_frame);
                
                // 记录时间戳用于后续分析
                stats_.frame_timestamps[camera_id].push_back(frame_time);
                stats_.total_frames_pushed++;
            }
            
            frame_counter++;
            std::this_thread::sleep_until(frame_time + frame_interval);
        }
        
        stats_.end_time = std::chrono::steady_clock::now();
        
        // 停止录制
        recording_service_->stopRecording(1);
        recording_service_->stopRecording(2);
        
        std::cout << "✅ 帧推送测试完成" << std::endl;
    }
    
    /**
     * 创建测试帧，包含时间戳和摄像头ID信息
     */
    cv::Mat createTestFrame(int camera_id, int frame_number) {
        cv::Mat frame(480, 640, CV_8UC3, cv::Scalar(50, 100, 150));
        
        // 在帧上绘制信息
        std::string info = "Cam" + std::to_string(camera_id) + " Frame:" + std::to_string(frame_number);
        cv::putText(frame, info, cv::Point(10, 30), cv::FONT_HERSHEY_SIMPLEX, 1, cv::Scalar(255, 255, 255), 2);
        
        // 添加时间戳
        auto now = std::chrono::steady_clock::now();
        auto timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count();
        std::string time_info = "T:" + std::to_string(timestamp);
        cv::putText(frame, time_info, cv::Point(10, 70), cv::FONT_HERSHEY_SIMPLEX, 0.7, cv::Scalar(255, 255, 0), 2);
        
        return frame;
    }
    
    /**
     * 分析录制结果
     */
    void analyzeResults() {
        std::cout << "\n📊 测试结果分析:" << std::endl;
        
        auto test_duration = std::chrono::duration_cast<std::chrono::milliseconds>(stats_.end_time - stats_.start_time);
        double actual_fps = static_cast<double>(stats_.total_frames_pushed) / (test_duration.count() / 1000.0);
        
        std::cout << "  - 测试持续时间: " << test_duration.count() << " ms" << std::endl;
        std::cout << "  - 总推送帧数: " << stats_.total_frames_pushed << std::endl;
        std::cout << "  - 实际推送帧率: " << actual_fps << " FPS" << std::endl;
        
        // 分析双摄像头同步情况
        analyzeStereoSync();
        
        // 检查录制状态
        auto recording_status = recording_service_->getRecordingStatus();
        std::cout << "  - 录制状态: ";
        for (const auto& [camera_id, is_recording] : recording_status) {
            std::cout << "摄像头" << camera_id << ":" << (is_recording ? "录制中" : "已停止") << " ";
        }
        std::cout << std::endl;
        
        std::cout << "  - 双目同步: " << (recording_service_->isStereoSyncEnabled() ? "启用" : "禁用") << std::endl;
    }
    
    /**
     * 分析双摄像头同步情况
     */
    void analyzeStereoSync() {
        if (stats_.frame_timestamps.size() < 2) {
            std::cout << "  - 同步分析: 数据不足" << std::endl;
            return;
        }
        
        auto& cam1_timestamps = stats_.frame_timestamps[1];
        auto& cam2_timestamps = stats_.frame_timestamps[2];
        
        size_t min_frames = std::min(cam1_timestamps.size(), cam2_timestamps.size());
        std::vector<std::chrono::milliseconds> sync_diffs;
        
        for (size_t i = 0; i < min_frames; ++i) {
            auto diff = std::chrono::duration_cast<std::chrono::milliseconds>(
                std::abs((cam1_timestamps[i] - cam2_timestamps[i]).count()));
            sync_diffs.push_back(diff);
        }
        
        if (!sync_diffs.empty()) {
            auto max_diff = *std::max_element(sync_diffs.begin(), sync_diffs.end());
            auto avg_diff = std::accumulate(sync_diffs.begin(), sync_diffs.end(), std::chrono::milliseconds{0}) / sync_diffs.size();
            
            std::cout << "  - 双摄像头同步分析:" << std::endl;
            std::cout << "    * 最大时间差: " << max_diff.count() << " ms" << std::endl;
            std::cout << "    * 平均时间差: " << avg_diff.count() << " ms" << std::endl;
            std::cout << "    * 同步质量: " << (max_diff.count() <= 10 ? "优秀" : max_diff.count() <= 20 ? "良好" : "需要改进") << std::endl;
        }
    }
    
    /**
     * 运行完整测试套件
     */
    void runFullTest() {
        std::cout << "🚀 开始录制功能完整测试" << std::endl;
        std::cout << "目标: 验证210 FPS录制和帧重复bug修复" << std::endl;
        
        // 测试1: 模拟帧推送
        simulateFramePushing(5); // 5秒测试
        
        // 等待录制完成
        std::this_thread::sleep_for(std::chrono::seconds(2));
        
        // 分析结果
        analyzeResults();
        
        std::cout << "\n✅ 测试完成！请检查录制文件以验证帧重复是否已修复。" << std::endl;
        std::cout << "录制文件位置: C:/Dev/Camera_Editor/Data/test_recordings/" << std::endl;
    }
};

int main() {
    try {
        RecordingTester tester;
        tester.runFullTest();
        
        std::cout << "\n按任意键退出..." << std::endl;
        std::cin.get();
        
    } catch (const std::exception& e) {
        std::cerr << "❌ 测试失败: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
