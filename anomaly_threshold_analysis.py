#!/usr/bin/env python3
"""
异常检测阈值优化分析脚本
基于新的高精度速度计算重新设计异常检测策略
"""

import math
import statistics

def analyze_current_thresholds():
    """
    分析当前异常检测阈值设置
    """
    print("=== 当前异常检测阈值分析 ===")
    
    current_thresholds = {
        "异常高速阈值": 50.0,  # m/s
        "旧-正常处理间隔": 200,  # ms (MAX_NORMAL_INTERVAL = 0.2s)
        "旧-可接受间隔": 1000,  # ms (MAX_ACCEPTABLE_INTERVAL = 1.0s)
        "新-正常相机间隔": 10,   # ms (MAX_NORMAL_INTERVAL = 0.010s)
        "新-可接受间隔": 50,     # ms (MAX_ACCEPTABLE_INTERVAL = 0.050s)
        "球丢失容忍时间": 1000,   # ms (BALL_LOSS_TOLERANCE = 1.0s)
    }
    
    print("当前阈值设置:")
    for name, value in current_thresholds.items():
        unit = "m/s" if "速度" in name else "ms"
        print(f"  {name}: {value} {unit}")
    
    return current_thresholds

def calculate_realistic_speed_ranges():
    """
    计算真实乒乓球速度范围和异常检测阈值
    """
    print("\n=== 乒乓球速度范围分析 ===")
    
    # 基于实际乒乓球比赛数据
    speed_ranges = {
        "休闲娱乐": (3, 8),      # m/s
        "业余比赛": (8, 15),     # m/s  
        "专业比赛": (15, 25),    # m/s
        "世界顶级": (25, 35),    # m/s
        "理论极限": (35, 45),    # m/s (考虑击球技术和物理限制)
    }
    
    print("真实速度范围:")
    for level, (min_speed, max_speed) in speed_ranges.items():
        print(f"  {level}: {min_speed}-{max_speed} m/s")
    
    # 基于物理限制的分析
    print("\n物理限制分析:")
    
    # 乒乓球空气阻力限制
    ball_mass = 0.0027  # kg (2.7g)
    ball_diameter = 0.04  # m (40mm)
    ball_area = math.pi * (ball_diameter/2)**2
    air_density = 1.225  # kg/m³
    drag_coefficient = 0.47  # 球形物体
    
    # 终端速度计算: v_terminal = sqrt(2*m*g / (ρ*A*Cd))
    terminal_velocity = math.sqrt(2 * ball_mass * 9.81 / (air_density * ball_area * drag_coefficient))
    
    print(f"  乒乓球终端速度: {terminal_velocity:.1f} m/s")
    print(f"  实际击球最大速度: ~{terminal_velocity * 0.8:.1f} m/s (考虑击球角度和技术)")
    
    # 推荐异常检测阈值
    recommended_thresholds = {
        "正常速度上限": 30.0,    # m/s，覆盖99%的正常击球
        "警告速度阈值": 35.0,    # m/s，可能的异常但不完全排除
        "异常速度阈值": 40.0,    # m/s，明确的计算错误或系统异常
        "严重异常阈值": 50.0,    # m/s，严重的系统错误
    }
    
    print("\n推荐异常检测阈值:")
    for name, threshold in recommended_thresholds.items():
        print(f"  {name}: {threshold} m/s")
    
    return recommended_thresholds

def analyze_timing_intervals():
    """
    分析基于真实相机时间戳的时间间隔异常检测
    """
    print("\n=== 时间间隔异常检测分析 ===")
    
    # 210FPS相机的时间特性
    fps = 210
    ideal_interval_ms = 1000 / fps  # 4.76ms
    
    print(f"210FPS相机理论间隔: {ideal_interval_ms:.2f} ms")
    
    # 实际系统中的时间间隔变化
    timing_analysis = {
        "理想间隔": ideal_interval_ms,
        "正常范围": (ideal_interval_ms * 0.8, ideal_interval_ms * 1.2),  # ±20%
        "可接受范围": (ideal_interval_ms * 0.5, ideal_interval_ms * 2.0),  # ±100%
        "异常范围": (0, ideal_interval_ms * 0.5),  # 过小
        "严重异常": (ideal_interval_ms * 2.0, float('inf')),  # 过大
    }
    
    print("\n时间间隔分类:")
    for category, range_val in timing_analysis.items():
        if isinstance(range_val, tuple):
            if range_val[1] == float('inf'):
                print(f"  {category}: > {range_val[0]:.2f} ms")
            else:
                print(f"  {category}: {range_val[0]:.2f} - {range_val[1]:.2f} ms")
        else:
            print(f"  {category}: {range_val:.2f} ms")
    
    # 推荐时间间隔阈值
    recommended_timing = {
        "EXPECTED_CAMERA_INTERVAL": ideal_interval_ms / 1000,  # 4.76ms
        "MAX_NORMAL_INTERVAL": ideal_interval_ms * 2.0 / 1000,  # 9.52ms
        "MAX_ACCEPTABLE_INTERVAL": ideal_interval_ms * 5.0 / 1000,  # 23.8ms  
        "MAX_PROBLEMATIC_INTERVAL": 0.1,  # 100ms，明确的处理延迟
        "BALL_LOSS_TOLERANCE": 0.5,  # 500ms，基于高精度减少容忍时间
    }
    
    print("\n推荐时间阈值 (秒):")
    for name, value in recommended_timing.items():
        print(f"  {name}: {value:.4f} s ({value*1000:.1f} ms)")
    
    return recommended_timing

def calculate_statistical_thresholds():
    """
    基于统计学原理计算动态异常检测阈值
    """
    print("\n=== 统计学异常检测策略 ===")
    
    # 模拟正常速度分布 (基于实际乒乓球数据)
    # 假设正常分布：均值15m/s，标准差8m/s
    normal_speed_mean = 15.0  # m/s
    normal_speed_std = 8.0    # m/s
    
    print(f"假设正常速度分布: μ={normal_speed_mean} m/s, σ={normal_speed_std} m/s")
    
    # 统计学阈值计算
    statistical_thresholds = {
        "1σ 阈值 (68%覆盖)": normal_speed_mean + 1 * normal_speed_std,
        "2σ 阈值 (95%覆盖)": normal_speed_mean + 2 * normal_speed_std,
        "3σ 阈值 (99.7%覆盖)": normal_speed_mean + 3 * normal_speed_std,
        "4σ 阈值 (99.99%覆盖)": normal_speed_mean + 4 * normal_speed_std,
    }
    
    print("\n统计学异常检测阈值:")
    for name, threshold in statistical_thresholds.items():
        print(f"  {name}: {threshold:.1f} m/s")
    
    # 推荐策略
    print("\n推荐统计策略:")
    print("  - 2σ阈值 (31 m/s): 警告级别，记录但继续处理")
    print("  - 3σ阈值 (39 m/s): 异常级别，触发检查但不丢弃")  
    print("  - 4σ阈值 (47 m/s): 严重异常，可能丢弃数据点")
    
    return statistical_thresholds

def analyze_measurement_noise_impact():
    """
    分析测量噪声对异常检测的影响
    """
    print("\n=== 测量噪声影响分析 ===")
    
    # 基于前面SG滤波器验证的精度数据
    sg_performance = {
        "旧参数平均误差": 0.0191,  # m/s
        "新参数平均误差": 0.0112,  # m/s
        "新参数最大误差": 0.0249,  # m/s
    }
    
    print("SG滤波器性能:")
    for name, error in sg_performance.items():
        print(f"  {name}: ±{error:.4f} m/s")
    
    # 基于测量误差的异常检测调整
    measurement_noise = sg_performance["新参数平均误差"]
    max_measurement_error = sg_performance["新参数最大误差"]
    
    # 考虑测量噪声的阈值调整
    print(f"\n基于测量噪声的阈值调整:")
    print(f"  典型测量误差: ±{measurement_noise:.4f} m/s")
    print(f"  最大测量误差: ±{max_measurement_error:.4f} m/s")
    
    # 建议在统计阈值基础上增加噪声容限
    noise_adjusted_thresholds = {
        "保守异常阈值": 35.0 + 3 * max_measurement_error,  # 基础阈值 + 3倍最大误差
        "标准异常阈值": 40.0 + 2 * max_measurement_error,  # 基础阈值 + 2倍最大误差  
        "严格异常阈值": 45.0 + 1 * max_measurement_error,  # 基础阈值 + 1倍最大误差
    }
    
    print(f"\n噪声调整后的阈值:")
    for name, threshold in noise_adjusted_thresholds.items():
        print(f"  {name}: {threshold:.3f} m/s")
    
    return noise_adjusted_thresholds

def generate_optimized_thresholds():
    """
    生成最终的优化异常检测阈值
    """
    print("\n" + "="*60)
    print("最终优化异常检测阈值建议")
    print("="*60)
    
    # 综合考虑所有因素的最终建议
    final_thresholds = {
        # 速度异常检测 (多级预警)
        "SPEED_WARNING_THRESHOLD": 30.0,    # 警告：超出常见范围
        "SPEED_ANOMALY_THRESHOLD": 35.0,    # 异常：可能的计算错误
        "SPEED_CRITICAL_THRESHOLD": 40.0,   # 严重：明确的系统错误
        
        # 时间间隔异常检测 (基于210FPS)
        "EXPECTED_CAMERA_INTERVAL": 0.00476,  # 4.76ms理论间隔
        "MAX_NORMAL_INTERVAL": 0.010,         # 10ms正常上限
        "MAX_ACCEPTABLE_INTERVAL": 0.025,     # 25ms可接受上限
        "MAX_PROBLEMATIC_INTERVAL": 0.100,    # 100ms问题间隔
        
        # 球丢失检测 (基于高精度缩短容忍时间)
        "BALL_LOSS_WARNING": 0.3,     # 300ms警告
        "BALL_LOSS_TOLERANCE": 0.5,   # 500ms容忍极限
        
        # 数据质量检测
        "MIN_POSITION_CHANGE": 0.0005,  # 0.5mm最小位置变化
        "MAX_ACCELERATION": 100.0,      # 100 m/s²最大加速度
    }
    
    print("推荐的最终阈值配置:")
    for name, value in final_thresholds.items():
        if "SPEED" in name:
            print(f"  {name}: {value} m/s")
        elif "INTERVAL" in name or "TOLERANCE" in name or "WARNING" in name:
            print(f"  {name}: {value} s ({value*1000:.1f} ms)")
        elif "CHANGE" in name:
            print(f"  {name}: {value} m ({value*1000:.1f} mm)")
        else:
            print(f"  {name}: {value}")
    
    print(f"\n实施优先级:")
    print(f"1. 立即更新: SPEED_CRITICAL_THRESHOLD 从50.0降至40.0")
    print(f"2. 时间优化: 更新所有时间间隔阈值使用真实相机特性")
    print(f"3. 多级预警: 实现分级异常检测而非简单二元判断")
    print(f"4. 自适应调整: 考虑实现基于历史数据的动态阈值")
    
    return final_thresholds

def compare_old_vs_new_thresholds():
    """
    对比旧阈值和新阈值的改进效果
    """
    print(f"\n" + "="*60)
    print("旧阈值 vs 新阈值对比")
    print("="*60)
    
    comparison = {
        "异常速度检测": {
            "旧阈值": "50.0 m/s (单一阈值)",
            "新阈值": "30/35/40 m/s (三级预警)",
            "改进": "更精细的异常分级，减少误报"
        },
        "时间间隔检测": {
            "旧阈值": "200/1000 ms (基于处理时间)",
            "新阈值": "10/25/100 ms (基于相机时间)",
            "改进": "基于真实硬件特性，提升检测精度"
        },
        "球丢失容忍": {
            "旧阈值": "1000 ms",
            "新阈值": "500 ms",
            "改进": "更快的丢失检测，提升响应性"
        },
    }
    
    for category, details in comparison.items():
        print(f"\n{category}:")
        for key, value in details.items():
            print(f"  {key}: {value}")

if __name__ == "__main__":
    print("异常检测阈值优化分析")
    print("基于新的高精度速度计算重新设计异常检测策略")
    print()
    
    # 执行所有分析
    analyze_current_thresholds()
    calculate_realistic_speed_ranges()
    analyze_timing_intervals()
    calculate_statistical_thresholds()
    analyze_measurement_noise_impact()
    final_thresholds = generate_optimized_thresholds()
    compare_old_vs_new_thresholds()
    
    print(f"\n" + "="*60)
    print("分析完成 - 建议立即实施新的异常检测阈值")
    print("="*60)