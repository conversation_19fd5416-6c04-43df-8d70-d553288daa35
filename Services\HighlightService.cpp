#include "HighlightService.hpp"
#include "../Utils/SharedData.hpp"
#include <iostream>
#include <filesystem>
#include <sstream>
#include <iomanip>
#include <algorithm>
#include <cstdio>
#include <fstream>
#include <cstdlib>

// 日志宏定义
#define LOG_INFO(msg) std::cout << "[HighlightService] " << msg << std::endl
#define LOG_ERROR(msg) std::cerr << "[HighlightService ERROR] " << msg << std::endl
#define LOG_WARNING(msg) std::cout << "[HighlightService WARNING] " << msg << std::endl
#define LOG_DEBUG(msg) std::cout << "[HighlightService DEBUG] " << msg << std::endl

// FFmpeg路径配置
const std::string HighlightService::FFMPEG_PATH = "C:\\Dev\\Camera_Editor\\Tools\\ffmpeg\\bin\\ffmpeg.exe";

HighlightService::HighlightService(std::shared_ptr<SharedData> shared_data)
    : shared_data_(shared_data)
    , should_stop_(false)
    , recording_state_(RecordingState::IDLE)
    , camera_id_(1)                        // 默认左摄像头
    , speed_threshold_(15.0)               // 默认15 m/s阈值
    , current_ball_speed_(0.0)
    , last_highlight_time_(std::chrono::steady_clock::now())
    , output_directory_("C:\\Dev\\Camera_Editor\\Data\\highlights\\")
    , highlight_interval_seconds_(1.0)     // 精彩时刻最小间隔1秒
    , clip_before_seconds_(3.0)            // 精彩时刻前3秒
    , clip_after_seconds_(2.0)             // 精彩时刻后2秒
{
    LOG_INFO("HighlightService initialized with camera_id=" << camera_id_);
    
    // 确保输出目录存在
    try {
        std::filesystem::create_directories(output_directory_);
        LOG_INFO("Output directory ensured: " << output_directory_);
    } catch (const std::exception& e) {
        LOG_ERROR("Failed to create output directory: " << e.what());
    }
}

HighlightService::~HighlightService() {
    LOG_INFO("HighlightService destructor called");

    // 确保所有线程都已停止
    should_stop_ = true;

    if (monitoring_thread_ && monitoring_thread_->joinable()) {
        monitoring_thread_->join();
    }

    if (processing_thread_ && processing_thread_->joinable()) {
        processing_thread_->join();
    }

    LOG_INFO("HighlightService destroyed");
}

bool HighlightService::startHighlightRecording(double speed_threshold, int camera_id) {
    if (recording_state_ != RecordingState::IDLE) {
        std::string error = "Cannot start recording: current state is not IDLE";
        LOG_ERROR(error);
        setError(error);
        return false;
    }

    LOG_INFO("Starting highlight recording with speed threshold: " << speed_threshold
             << " m/s, camera_id: " << camera_id);

    // 设置参数
    camera_id_ = camera_id;
    speed_threshold_ = speed_threshold;
    should_stop_ = false;
    current_ball_speed_ = 0.0;
    recording_start_time_ = std::chrono::steady_clock::now();

    // 清理之前的数据
    {
        std::lock_guard<std::mutex> lock(highlights_mutex_);
        highlight_moments_.clear();
    }

    {
        std::lock_guard<std::mutex> lock(clips_mutex_);
        generated_clips_.clear();
    }

    clearError();

    // 生成输出路径
    current_output_path_ = output_directory_ + "temp_recording_" + formatTimestamp() + ".mp4";

    // 通过消息系统启动录制
    if (!startRecordingViaMessage()) {
        setError("Failed to start recording via message system");
        return false;
    }

    // 启动监控线程
    try {
        recording_state_ = RecordingState::RECORDING;

        monitoring_thread_ = std::make_unique<std::thread>(&HighlightService::monitoringThreadMain, this);

        LOG_INFO("Highlight recording started successfully");
        return true;

    } catch (const std::exception& e) {
        std::string error = "Failed to start monitoring thread: " + std::string(e.what());
        LOG_ERROR(error);
        setError(error);
        recording_state_ = RecordingState::FAILED;
        return false;
    }
}

bool HighlightService::stopHighlightRecording() {
    if (recording_state_ != RecordingState::RECORDING) {
        std::string error = "Cannot stop recording: not currently recording";
        LOG_ERROR(error);
        setError(error);
        return false;
    }
    
    LOG_INFO("Stopping highlight recording...");
    
    // 设置停止标志
    should_stop_ = true;
    
    // 通过消息系统停止录制
    if (!stopRecordingViaMessage()) {
        LOG_ERROR("Failed to stop recording via message system");
    }

    // 等待监控线程结束
    if (monitoring_thread_ && monitoring_thread_->joinable()) {
        monitoring_thread_->join();
        monitoring_thread_.reset();
    }
    
    // 检查是否有精彩时刻
    size_t highlight_count = 0;
    {
        std::lock_guard<std::mutex> lock(highlights_mutex_);
        highlight_count = highlight_moments_.size();
    }
    
    if (highlight_count == 0) {
        LOG_INFO("No highlight moments detected. Recording completed without clips.");
        recording_state_ = RecordingState::COMPLETED;
        return true;
    }
    
    // 启动处理线程进行剪辑
    LOG_INFO("Starting processing thread for " << highlight_count << " highlight moments");
    recording_state_ = RecordingState::PROCESSING;
    
    try {
        processing_thread_ = std::make_unique<std::thread>(&HighlightService::processingThreadMain, this);
        return true;
    } catch (const std::exception& e) {
        std::string error = "Failed to start processing thread: " + std::string(e.what());
        LOG_ERROR(error);
        setError(error);
        recording_state_ = RecordingState::FAILED;
        return false;
    }
}

double HighlightService::getCurrentRecordingDuration() const {
    if (recording_state_ == RecordingState::RECORDING) {
        auto now = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(now - recording_start_time_);
        return duration.count() / 1000.0;  // 转换为秒
    }
    return 0.0;
}

size_t HighlightService::getHighlightMomentsCount() const {
    std::lock_guard<std::mutex> lock(highlights_mutex_);
    return highlight_moments_.size();
}

size_t HighlightService::getGeneratedClipsCount() const {
    std::lock_guard<std::mutex> lock(clips_mutex_);
    return generated_clips_.size();
}

void HighlightService::setSpeedThreshold(double threshold) {
    if (threshold >= 5.0 && threshold <= 50.0) {
        speed_threshold_ = threshold;
        LOG_DEBUG("Speed threshold updated to: " << threshold << " m/s");
    } else {
        LOG_ERROR("Invalid speed threshold: " << threshold << " (must be between 5.0 and 50.0)");
    }
}

void HighlightService::setClipDuration(double before_seconds, double after_seconds) {
    if (before_seconds >= 1.0 && before_seconds <= 10.0 && 
        after_seconds >= 1.0 && after_seconds <= 10.0) {
        clip_before_seconds_ = before_seconds;
        clip_after_seconds_ = after_seconds;
        LOG_DEBUG("Clip duration updated: before=" << before_seconds << "s, after=" << after_seconds << "s");
    } else {
        LOG_ERROR("Invalid clip duration parameters");
    }
}



std::string HighlightService::formatTimestamp() const {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        now.time_since_epoch()) % 1000;
    
    std::stringstream ss;
    ss << std::put_time(std::localtime(&time_t), "%Y%m%d_%H%M%S");
    ss << "_" << std::setfill('0') << std::setw(3) << ms.count();
    return ss.str();
}

void HighlightService::setError(const std::string& error) {
    std::lock_guard<std::mutex> lock(error_mutex_);
    error_message_ = error;
}






void HighlightService::monitoringThreadMain() {
    LOG_INFO("Monitoring thread started");

    try {
        while (!should_stop_ && recording_state_ == RecordingState::RECORDING) {
            checkForHighlightMoment();

            // 监控频率：每100ms检查一次
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }

    } catch (const std::exception& e) {
        LOG_ERROR("Exception in monitoring thread: " << e.what());
        setError("Monitoring thread error: " + std::string(e.what()));
        recording_state_ = RecordingState::FAILED;
    }

    LOG_INFO("Monitoring thread ended");
}

void HighlightService::checkForHighlightMoment() {
    if (!shared_data_) return;

    try {
        // 使用SharedData的批量数据获取方法获取球速和位置数据
        auto data_snapshot = shared_data_->getDataSnapshot();

        if (data_snapshot.has_data && !data_snapshot.ball_positions.empty()) {
            double current_speed = data_snapshot.ball_speed;
            cv::Point3f current_position(0, 0, 0);

            // 获取最新的3D位置
            if (!data_snapshot.ball_positions.empty()) {
                const auto& latest_pos = data_snapshot.ball_positions.back();
                current_position = cv::Point3f(
                    latest_pos.world_position.x,
                    latest_pos.world_position.y,
                    latest_pos.world_position.z
                );
            }

            // 更新当前球速
            current_ball_speed_ = current_speed;

            // 检查是否为精彩时刻
            if (isValidHighlightMoment(current_speed, current_position)) {
                auto now = std::chrono::steady_clock::now();
                auto relative_time = std::chrono::duration_cast<std::chrono::milliseconds>(
                    now - recording_start_time_);

                // 估算帧数：相对时间(ms) * 210 FPS / 1000
                int estimated_frame_count = static_cast<int>(relative_time.count() * 210 / 1000);
                HighlightMoment moment(now, relative_time, current_speed, current_position, estimated_frame_count);

                {
                    std::lock_guard<std::mutex> lock(highlights_mutex_);
                    highlight_moments_.push_back(moment);
                    last_highlight_time_ = now;
                }

                LOG_INFO("Highlight moment detected! Speed: " << current_speed
                        << " m/s at time: " << relative_time.count() << " ms");
            }
        }

    } catch (const std::exception& e) {
        LOG_ERROR("Exception in checkForHighlightMoment: " << e.what());
    }
}

bool HighlightService::isValidHighlightMoment(double speed, const cv::Point3f& position) {
    // 检查球速是否超过阈值
    if (speed < speed_threshold_) {
        return false;
    }

    // 检查是否距离上次精彩时刻足够远
    auto now = std::chrono::steady_clock::now();
    auto time_since_last = std::chrono::duration_cast<std::chrono::seconds>(now - last_highlight_time_);

    if (time_since_last.count() < highlight_interval_seconds_) {
        return false;
    }

    // 检查球的位置是否合理（可选的验证）
    // 这里可以添加更多的验证逻辑，比如球是否在合理的空间范围内

    return true;
}

void HighlightService::processingThreadMain() {
    LOG_INFO("Processing thread started");

    try {
        // 生成剪辑片段
        auto clips = generateClips();

        if (clips.empty()) {
            LOG_INFO("No highlight clips to generate");
            recording_state_ = RecordingState::COMPLETED;
            return;
        }

        LOG_INFO("Generated " << clips.size() << " clips for processing");

        // 处理每个片段
        int success_count = 0;
        for (const auto& clip : clips) {
            if (should_stop_) break;

            LOG_INFO("Processing clip: " << clip.output_filename);

            bool video_success = createClipVideo(clip);
            bool metadata_success = createMetadataFile(clip);

            if (video_success && metadata_success) {
                success_count++;

                // 添加到生成列表
                {
                    std::lock_guard<std::mutex> lock(clips_mutex_);
                    generated_clips_.push_back(clip);
                }

                LOG_INFO("Clip created successfully: " << clip.output_filename);
            } else {
                LOG_ERROR("Failed to create clip: " << clip.output_filename);
            }
        }

        LOG_INFO("Processing completed. " << success_count << "/" << clips.size() << " clips created successfully");

        // 如果有多个片段，尝试创建合并视频
        if (success_count > 1) {
            std::lock_guard<std::mutex> lock(clips_mutex_);
            if (createFinalMergedVideo(generated_clips_)) {
                LOG_INFO("Final merged video created successfully");
            } else {
                LOG_ERROR("Failed to create final merged video");
            }
        }

        recording_state_ = RecordingState::COMPLETED;

    } catch (const std::exception& e) {
        LOG_ERROR("Exception in processing thread: " << e.what());
        setError("Processing thread error: " + std::string(e.what()));
        recording_state_ = RecordingState::FAILED;
    }

    LOG_INFO("Processing thread ended");
}

std::vector<HighlightService::HighlightClip> HighlightService::generateClips() {
    std::vector<HighlightClip> clips;

    std::lock_guard<std::mutex> lock(highlights_mutex_);

    if (highlight_moments_.empty()) {
        return clips;
    }

    // 为每个精彩时刻生成一个片段
    for (const auto& moment : highlight_moments_) {
        HighlightClip clip;

        // 计算片段的开始和结束时间
        auto before_duration = std::chrono::milliseconds(static_cast<int>(clip_before_seconds_ * 1000));
        auto after_duration = std::chrono::milliseconds(static_cast<int>(clip_after_seconds_ * 1000));

        clip.start_time = std::max(std::chrono::milliseconds(0), moment.relative_time - before_duration);
        clip.end_time = moment.relative_time + after_duration;

        clip.moments.push_back(moment);
        clip.max_speed = moment.ball_speed;
        clip.output_filename = generateOutputFilename(clip);

        clips.push_back(clip);
    }

    // 合并重叠的片段
    return mergeOverlappingClips(clips);
}

std::vector<HighlightService::HighlightClip> HighlightService::mergeOverlappingClips(
    const std::vector<HighlightClip>& clips) {

    if (clips.empty()) {
        return {};
    }

    // 按开始时间排序
    auto sorted_clips = clips;
    std::sort(sorted_clips.begin(), sorted_clips.end(),
              [](const HighlightClip& a, const HighlightClip& b) {
                  return a.start_time < b.start_time;
              });

    std::vector<HighlightClip> merged_clips;
    merged_clips.push_back(sorted_clips[0]);

    for (size_t i = 1; i < sorted_clips.size(); ++i) {
        auto& current_clip = sorted_clips[i];
        auto& last_merged = merged_clips.back();

        // 检查是否重叠（包括小间隔）
        if (current_clip.start_time <= last_merged.end_time + std::chrono::milliseconds(500)) {
            // 合并片段
            last_merged.end_time = std::max(last_merged.end_time, current_clip.end_time);

            // 合并精彩时刻
            last_merged.moments.insert(last_merged.moments.end(),
                                     current_clip.moments.begin(),
                                     current_clip.moments.end());

            // 更新最大速度
            last_merged.max_speed = std::max(last_merged.max_speed, current_clip.max_speed);

            // 重新生成文件名
            last_merged.output_filename = generateOutputFilename(last_merged);

        } else {
            // 添加新的片段
            merged_clips.push_back(current_clip);
        }
    }

    LOG_INFO("Merged " << clips.size() << " clips into " << merged_clips.size() << " clips");
    return merged_clips;
}

std::string HighlightService::generateOutputFilename(const HighlightClip& clip) const {
    std::stringstream ss;
    ss << "highlight_" << formatTimestamp();
    ss << "_speed" << std::fixed << std::setprecision(1) << clip.max_speed;
    ss << "_" << clip.start_time.count() << "ms";
    ss << ".mp4";
    return ss.str();
}

bool HighlightService::createClipVideo(const HighlightClip& clip) {
    try {
        // 输出视频文件路径
        std::string output_path = output_directory_ + clip.output_filename;

        // 计算时间参数（秒）
        double start_seconds = clip.start_time.count() / 1000.0;
        double duration_seconds = (clip.end_time - clip.start_time).count() / 1000.0;

        // 构建FFmpeg命令
        std::ostringstream cmd;
        cmd << "\"" << FFMPEG_PATH << "\"";
        cmd << " -y";  // 覆盖输出文件
        cmd << " -i \"" << current_output_path_ << "\"";  // 输入文件
        cmd << " -ss " << std::fixed << std::setprecision(3) << start_seconds;  // 开始时间
        cmd << " -t " << std::fixed << std::setprecision(3) << duration_seconds;  // 持续时间
        cmd << " -c:v h264_nvenc";  // 使用NVIDIA硬件编码
        cmd << " -preset p1 -tune ll -rc vbr -cq 23";  // 高质量编码参数
        cmd << " -b:v 20M -maxrate 40M -bufsize 80M";  // 比特率设置
        cmd << " -vf \"format=yuv420p\"";  // 像素格式
        cmd << " -threads 0";  // 自动线程数
        cmd << " \"" << output_path << "\"";

        std::string command = cmd.str();
        LOG_DEBUG("FFmpeg command: " << command);

        // 执行FFmpeg命令
        int result = std::system(command.c_str());

        if (result == 0) {
            // 检查输出文件是否存在
            if (std::filesystem::exists(output_path)) {
                LOG_INFO("Clip video created: " << output_path);
                return true;
            } else {
                LOG_ERROR("FFmpeg succeeded but output file not found: " << output_path);
                return false;
            }
        } else {
            LOG_ERROR("FFmpeg command failed with code: " << result);
            return false;
        }

    } catch (const std::exception& e) {
        LOG_ERROR("Exception creating clip video: " << e.what());
        return false;
    }
}

bool HighlightService::createMetadataFile(const HighlightClip& clip) {
    try {
        std::string metadata_path = output_directory_ +
            clip.output_filename.substr(0, clip.output_filename.find_last_of('.')) + ".json";

        std::ofstream file(metadata_path);
        if (!file.is_open()) {
            LOG_ERROR("Failed to create metadata file: " << metadata_path);
            return false;
        }

        // 创建JSON元数据
        file << "{\n";
        file << "  \"filename\": \"" << clip.output_filename << "\",\n";
        file << "  \"start_time_ms\": " << clip.start_time.count() << ",\n";
        file << "  \"end_time_ms\": " << clip.end_time.count() << ",\n";
        file << "  \"duration_ms\": " << (clip.end_time - clip.start_time).count() << ",\n";
        file << "  \"max_speed\": " << clip.max_speed << ",\n";
        file << "  \"highlight_moments\": [\n";

        for (size_t i = 0; i < clip.moments.size(); ++i) {
            const auto& moment = clip.moments[i];
            file << "    {\n";
            file << "      \"time_ms\": " << moment.relative_time.count() << ",\n";
            file << "      \"speed\": " << moment.ball_speed << ",\n";
            file << "      \"position\": {\n";
            file << "        \"x\": " << moment.ball_position.x << ",\n";
            file << "        \"y\": " << moment.ball_position.y << ",\n";
            file << "        \"z\": " << moment.ball_position.z << "\n";
            file << "      },\n";
            file << "      \"frame_number\": " << moment.frame_number << "\n";
            file << "    }";
            if (i < clip.moments.size() - 1) file << ",";
            file << "\n";
        }

        file << "  ],\n";
        file << "  \"created_at\": \"" << formatTimestamp() << "\"\n";
        file << "}\n";

        file.close();
        LOG_INFO("Metadata file created: " << metadata_path);
        return true;

    } catch (const std::exception& e) {
        LOG_ERROR("Exception creating metadata file: " << e.what());
        return false;
    }
}

bool HighlightService::createFinalMergedVideo(const std::vector<HighlightClip>& clips) {
    if (clips.empty()) {
        return false;
    }

    try {
        std::string merged_filename = "highlights_merged_" + formatTimestamp() + ".mp4";
        std::string output_path = output_directory_ + merged_filename;

        // 创建文件列表用于FFmpeg concat
        std::string filelist_path = output_directory_ + "filelist_" + formatTimestamp() + ".txt";
        std::ofstream filelist(filelist_path);

        if (!filelist.is_open()) {
            LOG_ERROR("Failed to create filelist: " << filelist_path);
            return false;
        }

        for (const auto& clip : clips) {
            std::string clip_path = output_directory_ + clip.output_filename;
            if (std::filesystem::exists(clip_path)) {
                filelist << "file '" << clip_path << "'\n";
            }
        }
        filelist.close();

        // 构建FFmpeg合并命令
        std::ostringstream cmd;
        cmd << "\"" << FFMPEG_PATH << "\"";
        cmd << " -y";  // 覆盖输出文件
        cmd << " -f concat -safe 0";  // concat模式
        cmd << " -i \"" << filelist_path << "\"";  // 文件列表
        cmd << " -c:v h264_nvenc";  // 使用NVIDIA硬件编码
        cmd << " -preset p1 -tune ll -rc vbr -cq 23";  // 高质量编码参数
        cmd << " -b:v 25M -maxrate 50M -bufsize 100M";  // 比特率设置
        cmd << " \"" << output_path << "\"";

        std::string command = cmd.str();
        LOG_DEBUG("FFmpeg merge command: " << command);

        // 执行FFmpeg命令
        int result = std::system(command.c_str());

        // 清理临时文件列表
        std::filesystem::remove(filelist_path);

        if (result == 0 && std::filesystem::exists(output_path)) {
            LOG_INFO("Final merged video created: " << output_path);
            return true;
        } else {
            LOG_ERROR("Failed to create merged video");
            return false;
        }

    } catch (const std::exception& e) {
        LOG_ERROR("Exception creating merged video: " << e.what());
        return false;
    }
}

// === 新增：消息驱动录制控制实现 ===

bool HighlightService::startRecordingViaMessage() {
    if (!shared_data_) {
        LOG_ERROR("SharedData not available for recording message");
        return false;
    }

    // 生成请求ID
    current_request_id_ = "highlight_" + std::to_string(camera_id_) + "_" + formatTimestamp();

    // 创建录制消息
    RecordingMessage message(RecordingCommand::START_HIGHLIGHT, camera_id_, current_output_path_);
    message.request_id = current_request_id_;

    // 发送消息
    shared_data_->sendRecordingMessage(message);

    LOG_INFO("Sent start highlight recording message: request_id=" << current_request_id_
             << ", camera_id=" << camera_id_ << ", output_path=" << current_output_path_);

    // 等待响应
    waitForRecordingResponse(current_request_id_, RecordingStatus::IN_PROGRESS);

    return true;
}

bool HighlightService::stopRecordingViaMessage() {
    if (!shared_data_ || current_request_id_.empty()) {
        LOG_ERROR("Cannot stop recording: no active recording or SharedData not available");
        return false;
    }

    // 创建停止录制消息
    RecordingMessage message(RecordingCommand::STOP_HIGHLIGHT, camera_id_);
    message.request_id = current_request_id_;

    // 发送消息
    shared_data_->sendRecordingMessage(message);

    LOG_INFO("Sent stop highlight recording message: request_id=" << current_request_id_
             << ", camera_id=" << camera_id_);

    // 等待响应
    waitForRecordingResponse(current_request_id_, RecordingStatus::COMPLETED);

    return true;
}

void HighlightService::waitForRecordingResponse(const std::string& request_id, RecordingStatus expected_status) {
    if (!shared_data_) {
        return;
    }

    const int max_wait_seconds = 10;
    const int check_interval_ms = 100;
    int total_wait_ms = 0;

    while (total_wait_ms < max_wait_seconds * 1000) {
        auto response = shared_data_->getRecordingResponse(request_id);
        if (response.has_value()) {
            LOG_INFO("Received recording response: status=" << static_cast<int>(response->status)
                     << ", message=" << response->message);

            if (response->status == expected_status) {
                if (!response->output_file.empty()) {
                    current_output_path_ = response->output_file;
                    LOG_INFO("Updated output path: " << current_output_path_);
                }
                return;
            } else if (response->status == RecordingStatus::FAILED) {
                LOG_ERROR("Recording failed: " << response->message);
                setError("Recording failed: " + response->message);
                return;
            }
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(check_interval_ms));
        total_wait_ms += check_interval_ms;
    }

    LOG_WARNING("Timeout waiting for recording response: request_id=" << request_id);
}
