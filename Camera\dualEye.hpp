#ifndef __DUAL_EYE_HPP_
#define __DUAL_EYE_HPP_

#include "opencv2/opencv.hpp"

#include <opencv2/core/types.hpp>
#include <opencv2/core/mat.hpp>
#include <math_utils.hpp>
#include "Deploy/yolo.hpp"
#include "detect.hpp"
#include <map>

namespace DUE {
    std::vector<cv::Point2f> readChess(const std::string path);
    void writeChess(std::string path, std::vector<cv::Point2f> points);
    class C_PixData
    {
    public:
        cv::Point2f uvLeft = {};
        cv::Point2f uvRight = {};
        float confLeft = -1;
        float confRight = -1;
        C_PixData():uvLeft(cv::Point2f(0,0)), uvRight(cv::Point2f(0,0)) {}
        C_PixData(cv::Point2f left, cv::Point2f right):
            uvLeft(left),uvRight(right){}
        
        
    };
    std::map<std::string, C_PixData> classify(std::map<std::string, std::vector<Yolo::Detection>>detections, int yh);
    std::map<std::string, C_PixData> classify(
        std::map<std::string, std::vector<Yolo::Detection>>detectionsLeft,
        std::map<std::string, std::vector<Yolo::Detection>>detectionsRight
    );
    
    // 添加新的 classifyMultiple 函数，返回所有合理的左右视图匹配对
    std::vector<C_PixData> classifyMultiple(
        std::map<std::string, std::vector<Yolo::Detection>>detectionsLeft,
        std::map<std::string, std::vector<Yolo::Detection>>detectionsRight,
        const std::string& className = "red_ball",
        float yThreshold = 20.0f
    );
    
    class C_DualEye
    {
    

    private:
        std::string path_left_chess{ "C:\\Dev\\Camera_Editor\\Data\\chessLeft.csv" };
        std::string path_right_chess{ "C:\\Dev\\Camera_Editor\\Data\\chessRight.csv" };
        cv::Mat cameraMatrixLeft = (cv::Mat_<double>(3, 3) <<
            1.212461773900977e+03,0,0,
            0,1.208948173072893e+03,0,
            7.125999578992373e+02,5.582923658050460e+02,1

            );

        cv::Mat distCoeffsLeft = (cv::Mat_<double>(1, 5) <<
            - 0.121765075381230,0.194107891626906, 1.212461773900977e+03, 1.208948173072893e+03, - 0.129152658048664
            );
        cv::Mat cameraMatrixRight = (cv::Mat_<double>(3, 3) <<
            1.211146774417913e+03,0,0,
            0,1.208369255991100e+03,0,
            7.131739667255839e+02,5.533248398722095e+02,1

            );
        cv::Mat distCoeffsRight = (cv::Mat_<double>(1, 5) <<
            - 0.115864151705050,0.156361246124222, 1.211146774417913e+03, 1.208369255991100e+03, - 0.0721331747679454
            );
        cv::Mat Rrl = (cv::Mat_<double>(3, 3) <<
            0.999093499382032,- 0.0151514278178353,0.0397820779704500,
            0.0132209477212768,0.998743005284976,0.0483488979779940,
            - 0.0404646269464739,- 0.0477791128990078,0.998037960368576
            );
        cv::Mat Trl = (cv::Mat_<double>(3, 1) <<
            1.548000788623683e+02,-7.027604751460618, -6.041229571648451

            );
        

        // תͶӰ
        cv::Mat Rl, Rr, Pl, Pr, Q;
        cv::Mat Rcw, Tcw;

        //˫Ŀ
        C_PixData distortUVUV(C_PixData uvuv);
        cv::Point3f calP3inCam(C_PixData uvuv);
        std::vector<MU::Point3f> calP3inCam(std::vector<cv::Point2f> &left, std::vector<cv::Point2f> &right);

        //ı궨ǵ
        std::vector<MU::Point3f> genChessP3inWorld();
        bool detectChess(cv::Mat &left, cv::Mat &right,std::vector<cv::Point2f> &cornerLeft, std::vector<cv::Point2f> &cornerRight);
        std::vector<cv::Mat> calExternalRT(std::vector<cv::Point2f> &chessLeft, std::vector<cv::Point2f> &chessRight);
        bool checkExternalRT(std::vector<cv::Point2f>& cornerLeft, std::vector<cv::Point2f>& cornerRight, cv::Mat& rcw, cv::Mat& tcw, double errMax);
        
    public:
        cv::Size imageSize;
        bool resetExternalRT(cv::Mat &left, cv::Mat &right);
        void reloadCalibrationData(); // 新增：重新加载标定数据
        C_DualEye(int width,int height);

        MU::Point3f calP3inWorld(C_PixData data);
        std::vector<MU::Point3f> calP3inWorld(std::vector<cv::Point2f> left, std::vector<cv::Point2f> right);
        std::map<std::string,MU::Point3f> calP3inWorld(std::map<std::string, C_PixData> data);

        // 添加新的方法以支持多个匹配对的三维重建
        std::vector<MU::Point3f> calP3inWorld(const std::vector<C_PixData>& matchedPairs);
   
        // ӹзԻȡ˽гԱ
        cv::Mat getCameraMatrixLeft() const { return cameraMatrixLeft; }
        cv::Mat getDistCoeffsLeft() const { return distCoeffsLeft; }
        cv::Mat getCameraMatrixRight() const { return cameraMatrixRight; }
        cv::Mat getDistCoeffsRight() const { return distCoeffsRight; }
        cv::Mat getRcw() const { return Rcw; }
        cv::Mat getTcw() const { return Tcw; }

        // 获取标定文件路径
        std::string getLeftChessPath() const { return path_left_chess; }
        std::string getRightChessPath() const { return path_right_chess; }

        // ͶӰ
        std::pair<cv::Point2f, cv::Point2f> projectWorldPoint(const MU::Point3f& worldPoint);

        // ӻ
        void drawWorldOrigin(cv::Mat& leftImg, cv::Mat& rightImg);
    };


    
}

#endif // !__DUAL_EYE_HPP_
