#include "DynamicROIPredictor.hpp"
#include <algorithm>
#include <cmath>

DynamicROIPredictor::DynamicROIPredictor(size_t max_history)
    : m_maxHistory(max_history) {
}

void DynamicROIPredictor::updateState(const cv::Point3f& position, double dt) {
    auto now = std::chrono::high_resolution_clock::now();
    
    PredictionState state;
    state.position = position;
    state.timestamp = now;
    
    // 计算速度矢量
    if (!m_history.empty()) {
        const auto& prev = m_history.back();
        state.velocity = calculateVelocity(position, prev.position, dt);
    } else {
        state.velocity = cv::Point3f(0, 0, 0);
    }
    
    // 添加到历史记录
    m_history.push_back(state);
    
    // 保持历史记录数量在限制内
    if (m_history.size() > m_maxHistory) {
        m_history.pop_front();
    }
}

cv::Point3f DynamicROIPredictor::predictNextPosition(double dt) {
    if (!canPredict()) {
        return cv::Point3f(0, 0, 0);
    }
    
    const auto& current = m_history.back();
    
    // 使用抛物线轨迹预测（考虑重力影响）
    return applyGravityPrediction(current.position, current.velocity, dt);
}

cv::Point3f DynamicROIPredictor::getCurrentVelocity() const {
    if (m_history.empty()) {
        return cv::Point3f(0, 0, 0);
    }
    return m_history.back().velocity;
}

bool DynamicROIPredictor::canPredict() const {
    return m_history.size() >= 2;
}

void DynamicROIPredictor::reset() {
    m_history.clear();
}

size_t DynamicROIPredictor::getHistorySize() const {
    return m_history.size();
}

cv::Point3f DynamicROIPredictor::calculateVelocity(const cv::Point3f& current_pos, 
                                                  const cv::Point3f& previous_pos, 
                                                  double dt) {
    if (dt <= 0.0) {
        return cv::Point3f(0, 0, 0);
    }
    
    cv::Point3f displacement = current_pos - previous_pos;
    return cv::Point3f(
        displacement.x / static_cast<float>(dt),
        displacement.y / static_cast<float>(dt),
        displacement.z / static_cast<float>(dt)
    );
}

cv::Point3f DynamicROIPredictor::applyGravityPrediction(const cv::Point3f& initial_pos,
                                                       const cv::Point3f& initial_vel,
                                                       double dt) {
    // 线性预测：P_next = P_current + V * dt
    cv::Point3f predicted = initial_pos + initial_vel * static_cast<float>(dt);
    
    // 考虑重力影响（仅影响Z轴）
    // Z_next = Z_current + V_z * dt - 0.5 * g * dt²
    predicted.z -= 0.5f * GRAVITY * static_cast<float>(dt * dt);
    
    return predicted;
}
