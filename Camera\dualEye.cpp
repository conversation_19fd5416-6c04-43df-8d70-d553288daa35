#include "dualEye.hpp"
#include <iosfwd>
#include <iostream>
#include <map>
#include <fstream>
#include <stdexcept>
#include <string>
#include <vector>
#include <opencv2/calib3d.hpp>
#include <opencv2/core.hpp>
#include <opencv2/core/hal/interface.h>
#include <opencv2/core/mat.hpp>
#include <opencv2/core/types.hpp>
#include <opencv2/imgproc.hpp>
#include <math_utils.hpp>
#include "Deploy/yolo.hpp"

#include <chrono>
#include <filesystem>

std::map<std::string, DUE::C_PixData> DUE::classify(std::map<std::string, std::vector<Yolo::Detection>>detections, int yh)
{
    std::map<std::string, C_PixData> datas;
    for (auto it = detections.begin(); it != detections.end(); it++)//遍历类别
    {
        //box:left top right bottom
        if (it->second.size() < 2)
            continue;
        std::string name = it->first;
        bool leftFlag = false, rightFlag = false;
        C_PixData temp;
        for (auto iit = it->second.begin(); iit != it->second.end(); iit++)//遍历类别下的数据
        {
            cv::Point2f p = iit->center();
            float conf = iit->conf;
            if (p.y < yh)//left
            {
                if (conf > temp.confLeft)//选取最优值
                {
                    temp.uvLeft = p;
                    temp.confLeft = conf;
                    leftFlag = true;
                }
            }
            else//right
            {
                if (conf > temp.confRight)
                {
                    p.y -= yh;
                    temp.uvRight = p;
                    temp.confRight = conf;
                    rightFlag = true;
                }
            }
        }
        if (leftFlag && rightFlag)//类别有效，提交数据
        {
            datas[name] = temp;
        }

    }
    return datas;
}
std::map<std::string, DUE::C_PixData> DUE::classify(
    std::map<std::string, std::vector<Yolo::Detection>> detectionsLeft, 
    std::map<std::string, std::vector<Yolo::Detection>> detectionsRight)
{
    std::map<std::string, C_PixData> datas,ret;

    //数据重组
    for (auto it = detectionsLeft.begin(); it != detectionsLeft.end(); it++)//遍历类别
    {
        //box:left top right bottom
        std::string name = it->first;

        for (auto iit = it->second.begin(); iit != it->second.end(); iit++)//遍历类别下的数据
        {
            cv::Point2f p = iit->center();
            float conf = iit->conf;

            if (conf > datas[name].confLeft)//选取最优值
            {
                datas[name].uvLeft = p;
                datas[name].confLeft = conf;
            }
        }
    }
    for (auto it = detectionsRight.begin(); it != detectionsRight.end(); it++)//遍历类别
    {
        //box:left top right bottom
        std::string name = it->first;

        for (auto iit = it->second.begin(); iit != it->second.end(); iit++)//遍历类别下的数据
        {
            cv::Point2f p = iit->center();
            float conf = iit->conf;

            if (conf > datas[name].confRight)//选取最优值
            {
                datas[name].uvRight = p;
                datas[name].confRight = conf;
            }
        }
    }

    //检验
    for (auto& [name,data] : datas) {
        if (data.confLeft > 0 && data.confRight > 0) {
            ret[name] = data;
        }
    }

    return ret;
}
bool DUE::C_DualEye::resetExternalRT(cv::Mat& left, cv::Mat& right)
{
    try {
        std::cout << "开始进行标定过程..." << std::endl;
        
        std::vector<cv::Point2f> cornerLeft;
        std::vector<cv::Point2f> cornerRight;
        
        if (!this->detectChess(left, right, cornerLeft, cornerRight))
        {
            std::cout << "未能检测到棋盘格，请确保棋盘格完全可见..." << std::endl;
            std::this_thread::sleep_for(std::chrono::milliseconds(1000));
            return false;
        }
        
        std::cout << "成功检测到棋盘格，检测到 " << cornerLeft.size() << " 个角点" << std::endl;
        
        if (cornerLeft.size() < 8*11) {
            std::cout << "检测到的角点数量不足，需要至少 " << 8*11 << " 个角点" << std::endl;
            return false;
        }
        
        std::cout << "计算外部RT参数..." << std::endl;
        auto rt = this->calExternalRT(cornerLeft, cornerRight);
        
        std::cout << "验证外部RT参数..." << std::endl;
        const double errMax = 0.025; // 误差阈值
        bool calibrationSuccessful = this->checkExternalRT(cornerLeft, cornerRight, rt[0], rt[1], errMax);
        
        if (calibrationSuccessful)
        {
            std::cout << "准备写入标定数据..." << std::endl;
            
            // 在写入前打印路径，确保路径正确
            std::cout << "左棋盘格路径: " << this->path_left_chess << std::endl;
            std::cout << "右棋盘格路径: " << this->path_right_chess << std::endl;
            
            try {
                DUE::writeChess(this->path_left_chess, cornerLeft);
                DUE::writeChess(this->path_right_chess, cornerRight);

                std::cout << "标定数据写入成功，标定已达到所需精度水平" << std::endl;

                // 重新加载标定数据并更新外参矩阵
                std::cout << "重新加载标定数据..." << std::endl;
                auto leftChess = readChess(this->path_left_chess);
                auto rightChess = readChess(this->path_right_chess);
                auto ret = this->calExternalRT(leftChess, rightChess);
                ret[0].copyTo(this->Rcw);
                ret[1].copyTo(this->Tcw);

                std::cout << "新的外参矩阵已加载:" << std::endl;
                std::cout << "Rcw:" << this->Rcw << std::endl;
                std::cout << "Tcw:" << this->Tcw << std::endl;

                return true;
            }
            catch (const std::exception& e) {
                std::cerr << "写入棋盘格数据失败: " << e.what() << std::endl;
                return false;
            }
        }
        else
        {
            std::cout << "外部RT参数验证失败，请重新尝试标定" << std::endl;
            return false;
        }
    }
    catch (const std::exception& e) {
        std::cerr << "标定过程中出现异常: " << e.what() << std::endl;
        return false;
    }
}

void DUE::C_DualEye::reloadCalibrationData() {
    try {
        std::cout << "🔄 重新加载标定数据..." << std::endl;

        // 重新读取标定文件
        auto leftChess = readChess(this->path_left_chess);
        auto rightChess = readChess(this->path_right_chess);

        if (leftChess.empty() || rightChess.empty()) {
            std::cerr << "❌ 无法读取标定文件，保持原有参数" << std::endl;
            return;
        }

        // 重新计算外参矩阵
        auto ret = this->calExternalRT(leftChess, rightChess);
        ret[0].copyTo(this->Rcw);
        ret[1].copyTo(this->Tcw);

        std::cout << "✅ 标定数据重新加载完成" << std::endl;
        std::cout << "新的外参矩阵 Rcw:" << std::endl << this->Rcw << std::endl;
        std::cout << "新的外参矩阵 Tcw:" << std::endl << this->Tcw << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "❌ 重新加载标定数据失败: " << e.what() << std::endl;
    }
}

DUE::C_DualEye::C_DualEye(int width, int height)
{
    imageSize= cv::Size(width, height);
    cameraMatrixLeft = cameraMatrixLeft.t();
    cameraMatrixRight = cameraMatrixRight.t();
    this->Rrl = this->Rrl.t();
    cv::stereoRectify(cameraMatrixLeft, distCoeffsLeft, cameraMatrixRight, distCoeffsRight, imageSize, Rrl, Trl, Rl, Rr, Pl, Pr, Q);
    auto leftChess = readChess(this->path_left_chess), rightChess = readChess(this->path_right_chess);
    auto ret = this->calExternalRT(leftChess, rightChess);
    ret[0].copyTo(this->Rcw);
    ret[1].copyTo(this->Tcw);
}
DUE::C_PixData DUE::C_DualEye::distortUVUV(C_PixData uvuv)
{
    // 观察到的点坐标
    std::vector<cv::Point2f> srcPointsLeft = { uvuv.uvLeft };
    std::vector<cv::Point2f> srcPointsRight = { uvuv.uvRight };

    // 存储去畸变后的点坐标
    std::vector<cv::Point2f> dstPointsLeft;
    std::vector<cv::Point2f> dstPointsRight;

    // 调用undistortPoints函数进行去畸变处理
    cv::undistortPoints(srcPointsLeft, dstPointsLeft, this->cameraMatrixLeft, this->distCoeffsLeft, this->Rl, this->Pl);
    cv::undistortPoints(srcPointsRight, dstPointsRight, this->cameraMatrixRight, this->distCoeffsRight, this->Rr, this->Pr);

    return C_PixData(dstPointsLeft[0], dstPointsRight[0]);
}
cv::Point3f DUE::C_DualEye::calP3inCam(C_PixData uvuvDistorted)
{

    float ul = uvuvDistorted.uvLeft.x;
    float vl = uvuvDistorted.uvLeft.y;
    float ur = uvuvDistorted.uvRight.x;
    float vr = uvuvDistorted.uvRight.y;
    //printf("x:%000.3f,y:%000.3f,x:%000.3f,y:%000.3f\n", ul, vl, ur, vr);
    cv::Mat uvd1 = (cv::Mat_<double>(4, 1) << ul, vl, ul - ur, 1.0);
    cv::Mat pcdw = this->Q * uvd1;
    cv::Mat p3c = pcdw / (pcdw.at<double>(3, 0) * 1000);//#单位毫米转为米
    
    // 返回时将 Z 轴取反
    return cv::Point3f(p3c.at<double>(0, 0), p3c.at<double>(1, 0), p3c.at<double>(2, 0)); 
}

std::vector<MU::Point3f> DUE::C_DualEye::calP3inCam(std::vector<cv::Point2f>& left, std::vector<cv::Point2f>& right)
{
    if (left.size() != right.size())
    {
        throw std::invalid_argument("");
    }
    std::vector<cv::Point2f> distortLeft, distortRight;
    cv::undistortPoints(left, distortLeft, this->cameraMatrixLeft, this->distCoeffsLeft, this->Rl, this->Pl);
    cv::undistortPoints(right, distortRight, this->cameraMatrixRight, this->distCoeffsRight, this->Rr, this->Pr);

    std::vector<MU::Point3f> pcs;
    std::vector<float> dis;
    for (int i = 0; i < distortLeft.size(); i++)
    {
        pcs.push_back(MU::Point3f(this->calP3inCam(C_PixData(distortLeft[i], distortRight[i]))));
        if (i >= 1)
        {
            dis.push_back(pcs[i].distance(pcs[i - 1]));
            //std::cout << dis[i - 1] << ",";
        }
    }
    //std::cout << "\n\n";

    return pcs;
}

std::vector<cv::Mat> DUE::C_DualEye::calExternalRT(std::vector<cv::Point2f>& chessLeft, std::vector<cv::Point2f>& chessRight)
{
    if (chessLeft.size() != chessRight.size())
    {
        throw std::invalid_argument("");
    }
    int size = (int)chessLeft.size();

    std::vector<MU::Point3f> pcs = this->calP3inCam(chessLeft, chessRight);
    std::vector<MU::Point3f> pws = this->genChessP3inWorld();

    cv::Mat pcsc = cv::Mat::zeros(3, 1, CV_32F);
    cv::Mat pwsc = cv::Mat::zeros(3, 1, CV_32F);
    for (int i = 0; i < size; i++)
    {
        pcsc.at<float>(0, 0) += pcs[i].x;
        pcsc.at<float>(1, 0) += pcs[i].y;
        pcsc.at<float>(2, 0) += pcs[i].z;

        pwsc.at<float>(0, 0) += pws[i].x;
        pwsc.at<float>(1, 0) += pws[i].y;
        pwsc.at<float>(2, 0) += pws[i].z;
    }
    pcsc /= size;
    pwsc /= size;
    //std::cout << pcsc << '\n' << pwsc << '\n';

    cv::Mat H = cv::Mat::zeros(3, 3, CV_32F);
    for (int i = 0; i < size; i++)
    {
        H.at<float>(0, 0) += (pcs[i].x - pcsc.at<float>(0, 0)) * (pws[i].x - pwsc.at<float>(0, 0));
        H.at<float>(0, 1) += (pcs[i].x - pcsc.at<float>(0, 0)) * (pws[i].y - pwsc.at<float>(1, 0));
        H.at<float>(0, 2) += (pcs[i].x - pcsc.at<float>(0, 0)) * (pws[i].z - pwsc.at<float>(2, 0));

        H.at<float>(1, 0) += (pcs[i].y - pcsc.at<float>(1, 0)) * (pws[i].x - pwsc.at<float>(0, 0));
        H.at<float>(1, 1) += (pcs[i].y - pcsc.at<float>(1, 0)) * (pws[i].y - pwsc.at<float>(1, 0));
        H.at<float>(1, 2) += (pcs[i].y - pcsc.at<float>(1, 0)) * (pws[i].z - pwsc.at<float>(2, 0));

        H.at<float>(2, 0) += (pcs[i].z - pcsc.at<float>(2, 0)) * (pws[i].x - pwsc.at<float>(0, 0));
        H.at<float>(2, 1) += (pcs[i].z - pcsc.at<float>(2, 0)) * (pws[i].y - pwsc.at<float>(1, 0));
        H.at<float>(2, 2) += (pcs[i].z - pcsc.at<float>(2, 0)) * (pws[i].z - pwsc.at<float>(2, 0));
    }

    // 初始化用于存储奇异值和奇异向量的矩阵
    cv::Mat w, u, vt;
    // 执行SVD分解
    cv::SVD::compute(H, w, u, vt);
    //std::cout << H <<'\n' << w << '\n' << u << '\n' << vt << "\n";

    if (cv::determinant(H) < 0)
    {
        vt.at<float>(2, 0) *= -1;
        vt.at<float>(2, 1) *= -1;
        vt.at<float>(2, 2) *= -1;
    }
    std::vector<cv::Mat> ret;
    ret.push_back(vt.t() * u.t());
    ret.push_back(-ret[0] * pcsc + pwsc);
    std::cout << "Rcw:" << ret[0] << '\n' << "Tcw:" << ret[1] << '\n';
    return ret;
}
std::vector<MU::Point3f> DUE::C_DualEye::genChessP3inWorld()
{
    std::vector<MU::Point3f> pws;
    double a = 0.040;
    for (int x = 0; x < 11; x++)//x
    {
        for (int y = 0; y < 8; y++)//y
        {
            pws.push_back(MU::Point3f((x - 5) * a, (y) * a, 0.01));
        }
    }
    return pws;
}
bool DUE::C_DualEye::detectChess(cv::Mat& left, cv::Mat& right, std::vector<cv::Point2f>& cornerLeft, std::vector<cv::Point2f>& cornerRight)
{
    cv::Mat leftGray, rightGray;
    cv::cvtColor(left, leftGray, cv::COLOR_BGR2GRAY);
    cv::cvtColor(right, rightGray, cv::COLOR_BGR2GRAY);
    bool retl = cv::findChessboardCorners(leftGray, cv::Size(8, 11), cornerLeft);
    bool retr = cv::findChessboardCorners(rightGray, cv::Size(8, 11), cornerRight);
    if (!retl || !retr)
    {
        return false;
    }
    cv::drawChessboardCorners(left, cv::Size(8, 11), cornerLeft, retl);
    cv::drawChessboardCorners(right, cv::Size(8, 11), cornerRight, retr);
    for (int i = 0; i < 5; i++)
    {
        cv::circle(left, cornerLeft[i], 5, cv::Scalar(255, 255, 255));
        cv::circle(right, cornerRight[i], 5, cv::Scalar(255, 255, 255));
    }
    return true;
}
bool DUE::C_DualEye::checkExternalRT(std::vector<cv::Point2f>& cornerLeft, std::vector<cv::Point2f>& cornerRight, cv::Mat& rcw, cv::Mat& tcw, double errMax)
{
    rcw.copyTo(this->Rcw);
    tcw.copyTo(this->Tcw);
    //验证
    std::vector<MU::Point3f> pws = this->calP3inWorld(cornerLeft, cornerRight);
    std::vector<MU::Point3f> pws_ = this->genChessP3inWorld();
    //std::cout << "distance:";
    double meanErr = 0;

    for (int i = 0; i < pws.size(); i++)
    {
        double err = pws[i].distance(pws_[i]);
        meanErr += err;
        //std::cout << err << "\t";
    }
    meanErr /= pws.size();
    std::cout << "\nmeanErr\t" << meanErr << "\n";
    
    // 添加更多调试信息
    std::cout << "验证结果：平均误差 = " << meanErr << "，阈值 = " << errMax << std::endl;
    std::cout << "棋盘角点数量：" << cornerLeft.size() << std::endl;
    
    bool result = meanErr <= errMax;
    if (result) {
        std::cout << "标定验证成功，准备写入文件" << std::endl;
    } else {
        std::cout << "标定验证失败，误差超过阈值" << std::endl;
    }
    
    return result;
}


MU::Point3f DUE::C_DualEye::calP3inWorld(C_PixData data)
{
    MU::Point3f p3c = this->calP3inCam(this->distortUVUV(data));
    cv::Mat pc = (cv::Mat_<float>(3, 1) << p3c.x, p3c.y, p3c.z);
    cv::Mat pw = this->Rcw * pc + this->Tcw;
    MU::Point3f p3w = MU::Point3f(pw.at<float>(0, 0), pw.at<float>(1, 0), pw.at<float>(2, 0));
    return p3w;
}
std::vector<MU::Point3f> DUE::C_DualEye::calP3inWorld(std::vector<cv::Point2f> left, std::vector<cv::Point2f> right)
{
    std::vector<MU::Point3f> pws;
    std::vector<MU::Point3f> pcs = this->calP3inCam(left, right);
    for (int i = 0; i < pcs.size(); i++)
    {
        cv::Mat pc = (cv::Mat_<float>(3, 1) << pcs[i].x, pcs[i].y, pcs[i].z);
        cv::Mat pw = this->Rcw * pc + this->Tcw;
        pws.push_back(MU::Point3f(pw.at<float>(0, 0), pw.at<float>(1, 0), pw.at<float>(2, 0)));
    }
    return pws;
}
std::map<std::string, MU::Point3f> DUE::C_DualEye::calP3inWorld(std::map<std::string, C_PixData> data)
{
    std::map<std::string, MU::Point3f> ret;
    for (auto it = data.begin(); it != data.end(); it++)
    {
        ret[it->first] = this->calP3inWorld(it->second);
    }
    return ret;
}


std::vector<cv::Point2f> DUE::readChess(const std::string path)
{
    std::vector<cv::Point2f> data;
    std::ifstream file(path);
    std::string line;

    if (file.is_open()) {
        while (getline(file, line)) {
            std::stringstream lineStream(line);
            std::string cell;
            cv::Point2f pair;

            // 读取第一个float值
            getline(lineStream, cell, ',');
            std::stof(cell); // 将字符串转换为float，这里不保存结果，因为我们需要检查异常
            pair.x = std::stof(cell);

            // 读取第二个float值
            getline(lineStream, cell);
            std::stof(cell); // 同上
            pair.y = std::stof(cell);

            data.push_back(pair);
        }
        file.close();
    }
    else {
        std::cerr << "Unable to open file: " << path << std::endl;
    }

    return data;
}
void DUE::writeChess(std::string path, std::vector<cv::Point2f> points)
{
    try {
        // 提取目录路径
        std::string directory = path.substr(0, path.find_last_of("/\\"));
        
        // 检查并创建目录
        if (!std::filesystem::exists(directory)) {
            std::cout << "目录不存在，创建目录：" << directory << std::endl;
            std::filesystem::create_directories(directory);
        }
        
        // 打开一个文件流用于写入
        std::ofstream outFile(path);
        
        // 检查文件是否成功打开
        if (!outFile.is_open()) {
            std::cerr << "无法打开文件进行写入：" << path << std::endl;
            throw std::invalid_argument("无法打开文件进行写入");
        }
        
        // 遍历点集合并将它们写入文件
        for (const auto& point : points) {
            outFile << point.x << "," << point.y << std::endl;
        }
        
        // 检查写入是否成功
        if (outFile.fail()) {
            std::cerr << "写入文件时出错: " << path << std::endl;
            outFile.close();
            throw std::runtime_error("写入文件时出错");
        }
        
        // 关闭文件流
        outFile.close();
        
        std::cout << "成功写入数据到文件: " << path << "，写入了 " << points.size() << " 个点" << std::endl;
    }
    catch (const std::exception& e) {
        std::cerr << "写入棋盘格数据出错: " << e.what() << std::endl;
        throw; // 重新抛出异常
    }
}





// 添加新方法的实现：

// 修改projectWorldPoint方法，添加有效性检查
std::pair<cv::Point2f, cv::Point2f> DUE::C_DualEye::projectWorldPoint(const MU::Point3f& worldPoint) {
    try {
        // 验证输入参数
        if (this->Rcw.empty() || this->Tcw.empty()) {
            throw std::runtime_error("Extrinsic parameters not initialized");
        }

        // 世界坐标系到左相机坐标系
        cv::Mat Pw = (cv::Mat_<float>(3, 1) << worldPoint.x, worldPoint.y, worldPoint.z);
        cv::Mat Pc = this->Rcw * Pw + this->Tcw;

        // 验证投影矩阵
        if (this->Pl.empty() || this->Pr.empty()) {
            throw std::runtime_error("Projection matrices not initialized");
        }

        // 左相机投影（添加齐次坐标）
        cv::Mat pl = this->Pl * (cv::Mat_<float>(4, 1) << Pc.at<float>(0), Pc.at<float>(1), Pc.at<float>(2), 1.0f);
        cv::Point2f leftPixel(pl.at<float>(0) / pl.at<float>(2), pl.at<float>(1) / pl.at<float>(2));

        // 右相机投影（考虑基线）
        cv::Mat Pcr = this->Rrl * Pc + this->Trl;
        cv::Mat pr = this->Pr * (cv::Mat_<float>(4, 1) << Pcr.at<float>(0), Pcr.at<float>(1), Pcr.at<float>(2), 1.0f);
        cv::Point2f rightPixel(pr.at<float>(0) / pr.at<float>(2), pr.at<float>(1) / pr.at<float>(2));

        return { leftPixel, rightPixel };
    }
    catch (const cv::Exception& e) {
        std::cerr << "OpenCV Exception in projection: " << e.what() << std::endl;
        return { cv::Point2f(-1,-1), cv::Point2f(-1,-1) };
    }
}

// 修改drawWorldOrigin方法
void DUE::C_DualEye::drawWorldOrigin(cv::Mat& leftImg, cv::Mat& rightImg) {
    try {
        auto originPoints = projectWorldPoint(MU::Point3f(0, 0, 0));

        // 左图像有效性检查
        if (originPoints.first.x >= 0 && originPoints.first.x < leftImg.cols &&
            originPoints.first.y >= 0 && originPoints.first.y < leftImg.rows)
        {
            cv::drawMarker(leftImg, originPoints.first,
                cv::Scalar(0, 0, 255), cv::MARKER_CROSS, 30, 2);
            cv::putText(leftImg, "World Origin (0,0,0)",
                originPoints.first + cv::Point2f(15, -15),
                cv::FONT_HERSHEY_SIMPLEX, 0.6,
                cv::Scalar(0, 0, 255), 1);
        }
        else {
            std::cout << "Left origin out of bounds: "
                << originPoints.first << std::endl;
        }

        // 右图像有效性检查
        if (originPoints.second.x >= 0 && originPoints.second.x < rightImg.cols &&
            originPoints.second.y >= 0 && originPoints.second.y < rightImg.rows)
        {
            cv::drawMarker(rightImg, originPoints.second,
                cv::Scalar(0, 0, 255), cv::MARKER_CROSS, 30, 2);
            cv::putText(rightImg, "World Origin (0,0,0)",
                originPoints.second + cv::Point2f(15, -15),
                cv::FONT_HERSHEY_SIMPLEX, 0.6,
                cv::Scalar(0, 0, 255), 1);
        }
        else {
            std::cout << "Right origin out of bounds: "
                << originPoints.second << std::endl;
        }
    }
    catch (const cv::Exception& e) {
        std::cerr << "Drawing Error: " << e.what() << std::endl;
    }
}

// 在原有 classify 函数之后添加 classifyMultiple 函数实现
std::vector<DUE::C_PixData> DUE::classifyMultiple(
    std::map<std::string, std::vector<Yolo::Detection>> detectionsLeft,
    std::map<std::string, std::vector<Yolo::Detection>> detectionsRight,
    const std::string& className,
    float yThreshold)
{
    std::vector<C_PixData> matchedPairs;
    
    // 检查是否存在指定类别的检测结果
    if (detectionsLeft.find(className) == detectionsLeft.end() || 
        detectionsRight.find(className) == detectionsRight.end()) {
        // 如果左或右视图中没有该类别的检测结果，返回空列表
       // std::cout << "classifyMultiple: 左右视图中至少有一个没有检测到'" << className << "'类别" << std::endl;
        if (detectionsLeft.find(className) == detectionsLeft.end()) {
            //std::cout << "  左视图中缺少'" << className << "'类别" << std::endl;
        }
        if (detectionsRight.find(className) == detectionsRight.end()) {
           // std::cout << "  右视图中缺少'" << className << "'类别" << std::endl;
        }
        return matchedPairs;
    }
    
    // 获取左右视图中的球检测
    std::vector<Yolo::Detection>& ballsLeft = detectionsLeft[className];
    std::vector<Yolo::Detection>& ballsRight = detectionsRight[className];
    
    //std::cout << "classifyMultiple: 开始匹配, 左视图 " << ballsLeft.size() << " 个球, 右视图 " 
           //   << ballsRight.size() << " 个球, Y阈值: " << yThreshold << std::endl;
    
    // 创建右视图球的副本，用于跟踪哪些球已被匹配
    std::vector<Yolo::Detection> ballsRightCopy = ballsRight;
    
    // 对左视图中的每个球
    for (auto& leftBall : ballsLeft) {
        // 计算球的中心点坐标
        cv::Point2f leftCenter(leftBall.center());
        
        // 初始化最佳匹配
        int bestMatchIndex = -1;
        float minYDiff = yThreshold; // Y坐标允许的最大差异
        float minXDist = 1e6; // 最小水平距离，使用大数字而不是std::numeric_limits<float>::max()
        
        //std::cout << "  左球中心点: (" << leftCenter.x << ", " << leftCenter.y << "), 置信度: " << leftBall.conf << std::endl;
        
        // 查找右视图中Y坐标相近且水平距离最小的球
        for (int j = 0; j < ballsRightCopy.size(); j++) {
            auto& rightBall = ballsRightCopy[j];
            
            // 计算右视图球的中心点坐标
            cv::Point2f rightCenter(leftBall.center());
            
            // 计算Y坐标差异的绝对值
            float yDiff = std::abs(leftCenter.y - rightCenter.y);
            
           // std::cout << "    右球 #" << j << " 中心点: (" << rightCenter.x << ", " << rightCenter.y 
                //      << "), Y差异: " << yDiff << ", 置信度: " << rightBall.conf << std::endl;
            
            // 如果Y坐标差异在阈值内，考虑此匹配
            if (yDiff < minYDiff) {
                // 计算水平距离 (X坐标差异)
                float xDist = std::abs(leftCenter.x - rightCenter.x);
                
                //std::cout << "      Y差异在阈值内, X距离: " << xDist << std::endl;
                
                // 如果这是目前最好的匹配（水平距离最小）
                if (xDist < minXDist) {
                    minXDist = xDist;
                    bestMatchIndex = j;
                    minYDiff = yDiff; // 更新最小Y差异
                 //  std::cout << "        找到更好的匹配, 索引: " << j << std::endl;
                }
            }
        }
        
        // 如果找到匹配
        if (bestMatchIndex != -1) {
            auto& rightBall = ballsRightCopy[bestMatchIndex];
            
            // 创建新的像素数据对象
            C_PixData pixData;
            
            // 设置左右视图坐标
            pixData.uvLeft = cv::Point2f(leftBall.center());
            pixData.uvRight = cv::Point2f(rightBall.center());
            
            // 设置置信度
            pixData.confLeft = leftBall.conf;
            pixData.confRight = rightBall.conf;
            
            // 添加到匹配对列表
            matchedPairs.push_back(pixData);
            
          //  std::cout << "  成功匹配左球与右球 #" << bestMatchIndex << ", Y差异: " << minYDiff 
                //      << ", X距离: " << minXDist << std::endl;
            
            // 从右视图列表中移除已匹配的球，避免重复匹配
            ballsRightCopy.erase(ballsRightCopy.begin() + bestMatchIndex);
        } else {
            //std::cout << "  左球未找到匹配" << std::endl;
        }
    }
    
    //std::cout << "classifyMultiple: 共找到 " << matchedPairs.size() << " 对匹配" << std::endl;
    
    return matchedPairs;
}

// 在文件末尾添加新的 calP3inWorld 方法实现
std::vector<MU::Point3f> DUE::C_DualEye::calP3inWorld(const std::vector<C_PixData>& matchedPairs)
{
    std::vector<MU::Point3f> worldPoints;
    
   // std::cout << "calP3inWorld: 开始计算 " << matchedPairs.size() << " 对像素点的世界坐标" << std::endl;
    
    // 对每个匹配对进行三维重建
    for (int i = 0; i < matchedPairs.size(); i++) {
        const auto& pixData = matchedPairs[i];
        try {
          //  std::cout << "  像素对 #" << i << ": 左(" << pixData.uvLeft.x << ", " << pixData.uvLeft.y
                //     << "), 右(" << pixData.uvRight.x << ", " << pixData.uvRight.y << ")" << std::endl;
            
            // 计算世界坐标
            MU::Point3f worldPoint = this->calP3inWorld(pixData);
            
            //std::cout << "    世界坐标: (" << worldPoint.x << ", " << worldPoint.y << ", " << worldPoint.z << ")" << std::endl;
            
            // 添加到结果列表
            worldPoints.push_back(worldPoint);
        }
        catch (const std::exception& e) {
            //std::cerr << "三维重建错误: " << e.what() << std::endl;
            // 继续处理下一个匹配对
        }
    }
    
    //std::cout << "calP3inWorld: 共计算出 " << worldPoints.size() << " 个3D点" << std::endl;
    
    return worldPoints;
}