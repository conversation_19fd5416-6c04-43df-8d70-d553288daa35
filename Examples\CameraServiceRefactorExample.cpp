// === CameraService 错误处理重构示例 ===

#include "../Services/CameraService.hpp"
#include "../Utils/ErrorManager.hpp"

// 重构前的代码（存在问题）
namespace Before {
    cv::Mat CameraService::getFrame(int camera_id) {
        if (!cameras_initialized) {
            return cv::Mat(); // 静默失败，调用者无法知道失败原因
        }

        Hik::Camera* camera = nullptr;
        if (camera_id == 1) {
            camera = left_camera.get();
        } else if (camera_id == 2) {
            camera = right_camera.get();
        }

        if (camera) {
            cv::Mat frame;
            if (camera->read(frame)) {
                return frame.clone();
            }
        }
        return cv::Mat(); // 返回空Mat，无错误信息
    }
}

// 重构后的代码（使用新错误管理系统）
namespace After {
    using namespace ErrorManagement;
    
    cv::Mat CameraService::getFrame(int camera_id) {
        CAMERA_OPERATION("getFrame", this);
        
        try {
            if (!cameras_initialized) {
                throw CameraException("Cameras not initialized", LOCATION_INFO);
            }

            Hik::Camera* camera = getCameraById(camera_id);
            if (!camera) {
                throw CameraException("Invalid camera ID: " + std::to_string(camera_id), LOCATION_INFO);
            }

            cv::Mat frame;
            if (!camera->read(frame)) {
                consecutive_failures_++;
                
                // 性能优化：使用快速路径记录频繁的读取失败
                FAST_LOG_ERROR(ErrorCategory::CAMERA_ERROR, 
                              "Failed to read frame from camera " + std::to_string(camera_id));
                
                // 尝试自动恢复
                if (auto_recovery_enabled_ && consecutive_failures_ >= MAX_CONSECUTIVE_FAILURES) {
                    if (attemptCameraRecovery()) {
                        consecutive_failures_ = 0;
                        // 重试一次
                        if (camera->read(frame)) {
                            total_frames_captured_++;
                            last_success_time_ = std::chrono::steady_clock::now();
                            return frame.clone();
                        }
                    }
                }
                
                failed_frame_captures_++;
                updateCameraHealth();
                throw CameraException("Failed to capture frame from camera " + std::to_string(camera_id), LOCATION_INFO);
            }

            // 成功捕获帧
            consecutive_failures_ = 0;
            total_frames_captured_++;
            last_success_time_ = std::chrono::steady_clock::now();
            updateCameraHealth();
            
            return frame.clone();
            
        } catch (const CameraException& ex) {
            ErrorManager::getInstance().handleException(ex);
            throw; // 重新抛出，让调用者决定如何处理
        } catch (const std::exception& ex) {
            ErrorManager::getInstance().log(ErrorSeverity::ERROR, ErrorCategory::CAMERA_ERROR,
                                          "Standard exception in getFrame: " + std::string(ex.what()), LOCATION_INFO);
            throw CameraException("Unexpected error in getFrame: " + std::string(ex.what()), LOCATION_INFO);
        }
    }
    
    // 使用Result类型避免异常开销的版本（用于210FPS高频调用）
    FrameResult CameraService::getFrameSafe(int camera_id) {
        if (!cameras_initialized) {
            return FrameResult(ErrorInfo(ErrorCategory::CAMERA_ERROR, ErrorSeverity::ERROR, 
                                       "Cameras not initialized", LOCATION_INFO));
        }

        Hik::Camera* camera = getCameraById(camera_id);
        if (!camera) {
            return FrameResult(ErrorInfo(ErrorCategory::CAMERA_ERROR, ErrorSeverity::ERROR,
                                       "Invalid camera ID: " + std::to_string(camera_id), LOCATION_INFO));
        }

        cv::Mat frame;
        if (!camera->read(frame)) {
            // 快速路径错误记录，避免异常开销
            ErrorManager::getInstance().logFastPathError(ErrorCategory::CAMERA_ERROR,
                                                        "Frame read failed for camera " + std::to_string(camera_id));
            
            return FrameResult(ErrorInfo(ErrorCategory::CAMERA_ERROR, ErrorSeverity::WARNING,
                                       "Failed to read frame", LOCATION_INFO));
        }

        return FrameResult(frame.clone());
    }
}

// === InferenceService 错误处理重构示例 ===
namespace InferenceServiceRefactor {
    using namespace ErrorManagement;
    
    // 重构前：简单的try-catch
    DetectionResult InferenceService::processFrame(const cv::Mat& frame, float conf_threshold) {
        DetectionResult service_result;
        
        if (!m_yolo) {
            UTF8Utils::println("Error: Yolo object is not initialized.");
            return service_result;
        }

        // 调用YOLO推理
        std::map<std::string, std::vector<Yolo::Detection>> yolo_result = m_yolo->inference(frame, conf_threshold);
        
        // 转换结果...
        return service_result;
    }
    
    // 重构后：完整的错误处理和恢复
    DetectionResult InferenceService::processFrame(const cv::Mat& frame, float conf_threshold) {
        ErrorContext ctx("InferenceService::processFrame");
        
        try {
            if (!m_yolo) {
                throw InferenceException("YOLO model not initialized", LOCATION_INFO);
            }
            
            if (frame.empty()) {
                throw InferenceException("Input frame is empty", LOCATION_INFO);
            }
            
            // 性能监控：检查推理时间
            auto start_time = std::chrono::high_resolution_clock::now();
            
            std::map<std::string, std::vector<Yolo::Detection>> yolo_result = m_yolo->inference(frame, conf_threshold);
            
            auto end_time = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
            
            // 210FPS要求：推理时间不应超过4.76ms
            if (duration.count() > 5) {
                LOG_WARNING(ErrorCategory::INFERENCE_ERROR, 
                           "Inference took " + std::to_string(duration.count()) + "ms (>5ms threshold)");
            }
            
            DetectionResult service_result;
            // 转换结果...
            
            return service_result;
            
        } catch (const InferenceException& ex) {
            ErrorManager::getInstance().handleException(ex);
            
            // 尝试恢复：重新初始化YOLO模型
            if (attemptModelRecovery()) {
                LOG_INFO(ErrorCategory::INFERENCE_ERROR, "Model recovery successful, retrying inference");
                // 递归重试一次（需要防止无限递归）
                static thread_local int retry_count = 0;
                if (retry_count < 1) {
                    retry_count++;
                    auto result = processFrame(frame, conf_threshold);
                    retry_count--;
                    return result;
                }
            }
            
            throw; // 恢复失败，重新抛出异常
        }
    }
}

// === 性能优化的错误处理模式 ===
namespace PerformanceOptimizedErrorHandling {
    
    // 用于210FPS高频调用的轻量级错误处理
    class FastErrorHandler {
    private:
        static thread_local int error_count_;
        static thread_local std::chrono::steady_clock::time_point last_log_time_;
        
    public:
        // 快速错误记录，最小化性能影响
        static void logFastError(ErrorCategory category, const std::string& message) {
            error_count_++;
            auto now = std::chrono::steady_clock::now();
            
            // 每秒最多记录一次相同类型的错误
            if (std::chrono::duration_cast<std::chrono::seconds>(now - last_log_time_).count() >= 1) {
                if (error_count_ > 1) {
                    ErrorManager::getInstance().log(ErrorSeverity::WARNING, category,
                                                   message + " (occurred " + std::to_string(error_count_) + " times in last second)");
                } else {
                    ErrorManager::getInstance().log(ErrorSeverity::WARNING, category, message);
                }
                
                error_count_ = 0;
                last_log_time_ = now;
            }
        }
    };
    
    // 线程局部变量定义
    thread_local int FastErrorHandler::error_count_ = 0;
    thread_local std::chrono::steady_clock::time_point FastErrorHandler::last_log_time_ = std::chrono::steady_clock::now();
}

// === 错误恢复策略示例 ===
namespace RecoveryStrategies {
    
    // 相机错误恢复
    bool recoverCameraError() {
        try {
            LOG_INFO(ErrorCategory::CAMERA_ERROR, "Attempting camera recovery...");
            
            // 1. 重新初始化相机
            // 2. 检查硬件连接
            // 3. 重置相机参数
            
            LOG_INFO(ErrorCategory::CAMERA_ERROR, "Camera recovery completed successfully");
            return true;
        } catch (...) {
            LOG_ERROR("Camera recovery failed");
            return false;
        }
    }
    
    // AI推理错误恢复
    bool recoverInferenceError() {
        try {
            LOG_INFO(ErrorCategory::INFERENCE_ERROR, "Attempting inference model recovery...");
            
            // 1. 重新加载模型
            // 2. 重新初始化GPU上下文
            // 3. 验证模型完整性
            
            LOG_INFO(ErrorCategory::INFERENCE_ERROR, "Inference model recovery completed successfully");
            return true;
        } catch (...) {
            LOG_ERROR("Inference model recovery failed");
            return false;
        }
    }
}
