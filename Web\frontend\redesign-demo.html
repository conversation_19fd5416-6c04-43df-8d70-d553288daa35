<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Camera_Editor - 重新设计演示</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="redesign.css">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="app-container">
        <!-- 顶部导航栏 -->
        <header class="top-navbar">
            <div class="navbar-content">
                <div class="logo-section">
                    <i class="fas fa-video"></i>
                    <h1>Camera_Editor</h1>
                    <span class="version">v2.0</span>
                </div>
                
                <!-- 系统状态指示器 -->
                <div class="system-status-bar">
                    <div class="status-item">
                        <div class="status-dot online"></div>
                        <span>已连接</span>
                    </div>
                    <div class="status-item">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>210 FPS</span>
                    </div>
                    <div class="status-item">
                        <i class="fas fa-clock"></i>
                        <span>12:34:56</span>
                    </div>
                </div>

                <!-- 快速操作按钮 -->
                <div class="quick-actions">
                    <button class="btn btn-primary" title="启动系统">
                        <i class="fas fa-play"></i>
                    </button>
                    <button class="btn btn-danger" title="停止系统">
                        <i class="fas fa-stop"></i>
                    </button>
                    <button class="btn btn-warning" title="刷新连接">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                    <button class="btn btn-info" title="全屏模式">
                        <i class="fas fa-expand"></i>
                    </button>
                </div>
            </div>
        </header>

        <!-- 主标签页导航 -->
        <nav class="tab-navigation">
            <div class="tab-container">
                <button class="tab-button active" data-tab="dashboard">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>主控制台</span>
                </button>
                <button class="tab-button" data-tab="recording">
                    <i class="fas fa-video"></i>
                    <span>录制中心</span>
                </button>
                <button class="tab-button" data-tab="analytics">
                    <i class="fas fa-chart-line"></i>
                    <span>数据分析</span>
                </button>
                <button class="tab-button" data-tab="settings">
                    <i class="fas fa-cog"></i>
                    <span>系统设置</span>
                </button>
                <button class="tab-button" data-tab="database">
                    <i class="fas fa-database"></i>
                    <span>数据库</span>
                </button>
            </div>
        </nav>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 主控制台标签页 -->
            <div class="tab-content active" id="dashboard-tab">
                <!-- 关键指标仪表板 -->
                <section class="dashboard-metrics">
                    <h2><i class="fas fa-chart-line"></i> 实时监控仪表板</h2>
                    <div class="metrics-grid">
                        <div class="metric-card">
                            <div class="metric-icon">
                                <i class="fas fa-tachometer-alt"></i>
                            </div>
                            <div class="metric-content">
                                <div class="metric-value">15.8 <span class="unit">m/s</span></div>
                                <div class="metric-label">实时球速</div>
                            </div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-icon">
                                <i class="fas fa-table-tennis-paddle-ball"></i>
                            </div>
                            <div class="metric-content">
                                <div class="metric-value">1</div>
                                <div class="metric-label">检测目标</div>
                            </div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-icon">
                                <i class="fas fa-download"></i>
                            </div>
                            <div class="metric-content">
                                <div class="metric-value">210 FPS</div>
                                <div class="metric-label">接收帧率</div>
                            </div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="metric-content">
                                <div class="metric-value">12:34:56</div>
                                <div class="metric-label">最后更新</div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 双摄像头画面区域 -->
                <section class="camera-display">
                    <div class="camera-grid">
                        <div class="camera-item">
                            <div class="camera-header">
                                <h3><i class="fas fa-camera"></i> 左摄像头 (ID: 1)</h3>
                                <div class="camera-status status-connected">
                                    <span class="status-text">已连接</span>
                                </div>
                            </div>
                            <div class="camera-view">
                                <div style="width: 100%; height: 300px; background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%); display: flex; align-items: center; justify-content: center; color: var(--text-muted-color); font-size: 1.2rem; border-radius: 8px;">
                                    <i class="fas fa-video" style="margin-right: 8px;"></i>
                                    左摄像头画面区域
                                </div>
                            </div>
                            <div class="camera-footer">
                                <div class="camera-stats">
                                    <div class="stat-item">
                                        <i class="fas fa-microchip"></i>
                                        <span class="stat-value">210 FPS</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="camera-item">
                            <div class="camera-header">
                                <h3><i class="fas fa-camera"></i> 右摄像头 (ID: 2)</h3>
                                <div class="camera-status status-connected">
                                    <span class="status-text">已连接</span>
                                </div>
                            </div>
                            <div class="camera-view">
                                <div style="width: 100%; height: 300px; background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%); display: flex; align-items: center; justify-content: center; color: var(--text-muted-color); font-size: 1.2rem; border-radius: 8px;">
                                    <i class="fas fa-video" style="margin-right: 8px;"></i>
                                    右摄像头画面区域
                                </div>
                            </div>
                            <div class="camera-footer">
                                <div class="camera-stats">
                                    <div class="stat-item">
                                        <i class="fas fa-microchip"></i>
                                        <span class="stat-value">210 FPS</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 系统状态面板 -->
                <section class="system-status-panel">
                    <h2><i class="fas fa-heartbeat"></i> 系统状态</h2>
                    <div class="status-grid">
                        <div class="status-card">
                            <div class="status-icon"><i class="fas fa-camera"></i></div>
                            <div class="status-info">
                                <div class="status-title">相机系统</div>
                                <div class="status-value">运行中</div>
                            </div>
                        </div>
                        <div class="status-card">
                            <div class="status-icon"><i class="fas fa-satellite-dish"></i></div>
                            <div class="status-info">
                                <div class="status-title">视频流</div>
                                <div class="status-value">正常</div>
                            </div>
                        </div>
                        <div class="status-card">
                            <div class="status-icon"><i class="fas fa-record-vinyl"></i></div>
                            <div class="status-info">
                                <div class="status-title">全局录制</div>
                                <div class="status-value">未录制</div>
                            </div>
                        </div>
                        <div class="status-card">
                            <div class="status-icon"><i class="fas fa-crosshairs"></i></div>
                            <div class="status-info">
                                <div class="status-title">相机标定</div>
                                <div class="status-value">已完成</div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>

            <!-- 其他标签页内容将在这里显示 -->
            <div class="tab-content" id="recording-tab">
                <div style="text-align: center; padding: 60px; color: var(--text-muted-color);">
                    <i class="fas fa-video" style="font-size: 3rem; margin-bottom: 20px;"></i>
                    <h2>录制中心</h2>
                    <p>统一的录制控制面板将在这里显示</p>
                </div>
            </div>

            <div class="tab-content" id="analytics-tab">
                <div style="text-align: center; padding: 60px; color: var(--text-muted-color);">
                    <i class="fas fa-chart-line" style="font-size: 3rem; margin-bottom: 20px;"></i>
                    <h2>数据分析</h2>
                    <p>交互式图表和数据可视化将在这里显示</p>
                </div>
            </div>

            <div class="tab-content" id="settings-tab">
                <div style="text-align: center; padding: 60px; color: var(--text-muted-color);">
                    <i class="fas fa-cog" style="font-size: 3rem; margin-bottom: 20px;"></i>
                    <h2>系统设置</h2>
                    <p>相机标定和系统配置将在这里显示</p>
                </div>
            </div>

            <div class="tab-content" id="database-tab">
                <div style="text-align: center; padding: 60px; color: var(--text-muted-color);">
                    <i class="fas fa-database" style="font-size: 3rem; margin-bottom: 20px;"></i>
                    <h2>数据库</h2>
                    <p>SQL查询和数据浏览将在这里显示</p>
                </div>
            </div>
        </main>

        <!-- 底部状态栏 -->
        <footer class="bottom-status-bar">
            <div class="status-bar-content">
                <div class="status-info">
                    <span class="status-item">
                        <i class="fas fa-copyright"></i>
                        Camera_Editor v2.0 - 乒乓球自动裁判系统
                    </span>
                    <span class="status-item">
                        <i class="fas fa-server"></i>
                        服务器: <span>运行中</span>
                    </span>
                </div>
                <div class="performance-info">
                    <span class="status-item">
                        <i class="fas fa-memory"></i>
                        内存: <span>2.1GB</span>
                    </span>
                    <span class="status-item">
                        <i class="fas fa-microchip"></i>
                        CPU: <span>15%</span>
                    </span>
                </div>
            </div>
        </footer>
    </div>

    <script>
        // 简单的标签页切换演示
        document.addEventListener('DOMContentLoaded', function() {
            const tabButtons = document.querySelectorAll('.tab-button');
            const tabContents = document.querySelectorAll('.tab-content');
            
            tabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const targetTab = button.getAttribute('data-tab');
                    
                    // 移除所有活动状态
                    tabButtons.forEach(btn => btn.classList.remove('active'));
                    tabContents.forEach(content => content.classList.remove('active'));
                    
                    // 激活目标标签
                    button.classList.add('active');
                    document.getElementById(`${targetTab}-tab`).classList.add('active');
                });
            });
        });
    </script>
</body>
</html>
