#pragma once

#include "ErrorManagement.hpp"
#include <atomic>
#include <array>
#include <string_view>

namespace ErrorManagement {

/**
 * @brief 210FPS性能优化的错误处理系统
 * 
 * 专为高频实时处理设计，最小化错误处理开销
 */
class HighPerformanceErrorHandler {
private:
    // 预分配的错误消息缓冲区，避免动态分配
    static constexpr size_t BUFFER_SIZE = 1024;
    static constexpr size_t MAX_FAST_ERRORS = 10000;
    
    struct FastErrorEntry {
        std::chrono::steady_clock::time_point timestamp;
        ErrorCategory category;
        ErrorSeverity severity;
        char message[256]; // 固定大小，避免动态分配
        
        FastErrorEntry() = default;
        FastErrorEntry(ErrorCategory cat, ErrorSeverity sev, std::string_view msg) 
            : timestamp(std::chrono::steady_clock::now()), category(cat), severity(sev) {
            size_t copy_len = std::min(msg.length(), sizeof(message) - 1);
            std::memcpy(message, msg.data(), copy_len);
            message[copy_len] = '\0';
        }
    };
    
    // 无锁环形缓冲区
    std::array<FastErrorEntry, MAX_FAST_ERRORS> error_buffer_;
    std::atomic<size_t> write_index_{0};
    std::atomic<size_t> read_index_{0};
    
    // 性能统计
    std::atomic<uint64_t> total_errors_{0};
    std::atomic<uint64_t> dropped_errors_{0};
    std::atomic<uint64_t> processing_time_ns_{0};
    
    // 错误抑制（基于位图，超快速）
    std::atomic<uint64_t> suppression_bitmap_{0};
    std::chrono::steady_clock::time_point last_suppression_reset_;
    
public:
    HighPerformanceErrorHandler();
    
    /**
     * @brief 超快速错误记录（无锁，无动态分配）
     * 
     * @param category 错误分类
     * @param severity 错误严重程度
     * @param message 错误消息（使用string_view避免拷贝）
     * @return 是否成功记录
     */
    bool logFastError(ErrorCategory category, ErrorSeverity severity, std::string_view message);
    
    /**
     * @brief 批量处理错误（后台线程调用）
     * 
     * @param max_process_count 最大处理数量
     * @return 实际处理的错误数量
     */
    size_t processPendingErrors(size_t max_process_count = 100);
    
    /**
     * @brief 获取性能统计
     */
    struct PerformanceStats {
        uint64_t total_errors;
        uint64_t dropped_errors;
        uint64_t average_processing_time_ns;
        double drop_rate;
        size_t pending_errors;
    };
    
    PerformanceStats getPerformanceStats() const;
    
    /**
     * @brief 重置统计信息
     */
    void resetStats();
    
    /**
     * @brief 检查是否应该抑制错误（基于位图的超快速检查）
     */
    bool shouldSuppressError(ErrorCategory category) const;
    
private:
    size_t getPendingErrorCount() const;
    void updateSuppressionBitmap();
};

/**
 * @brief 210FPS优化的错误上下文管理器
 * 
 * 使用栈分配和编译时优化
 */
template<size_t MaxContextLength = 64>
class FastErrorContext {
private:
    char context_name_[MaxContextLength];
    std::chrono::steady_clock::time_point start_time_;
    ErrorCategory category_;
    
public:
    constexpr FastErrorContext(std::string_view context_name, ErrorCategory category) 
        : category_(category), start_time_(std::chrono::steady_clock::now()) {
        size_t copy_len = std::min(context_name.length(), MaxContextLength - 1);
        std::memcpy(context_name_, context_name.data(), copy_len);
        context_name_[copy_len] = '\0';
    }
    
    ~FastErrorContext() {
        auto duration = std::chrono::steady_clock::now() - start_time_;
        auto ns = std::chrono::duration_cast<std::chrono::nanoseconds>(duration).count();
        
        // 210FPS要求：单个操作不应超过4.76ms
        if (ns > 4760000) { // 4.76ms in nanoseconds
            HighPerformanceErrorHandler::getInstance().logFastError(
                category_, ErrorSeverity::WARNING,
                std::string_view(context_name_) + " exceeded 4.76ms threshold"
            );
        }
    }
};

/**
 * @brief 内存池分配的错误消息管理器
 * 
 * 避免频繁的内存分配/释放
 */
class ErrorMessagePool {
private:
    static constexpr size_t POOL_SIZE = 1024 * 1024; // 1MB pool
    static constexpr size_t MAX_MESSAGE_SIZE = 512;
    
    std::array<char, POOL_SIZE> memory_pool_;
    std::atomic<size_t> current_offset_{0};
    std::mutex reset_mutex_;
    
public:
    /**
     * @brief 从池中分配错误消息存储
     */
    char* allocateMessage(size_t size);
    
    /**
     * @brief 重置内存池（定期调用）
     */
    void resetPool();
    
    /**
     * @brief 获取池使用统计
     */
    struct PoolStats {
        size_t total_size;
        size_t used_size;
        double usage_percentage;
    };
    
    PoolStats getPoolStats() const;
};

/**
 * @brief 编译时错误分类映射
 * 
 * 使用constexpr实现零运行时开销的错误分类
 */
template<ErrorCategory Category>
struct ErrorCategoryTraits {
    static constexpr const char* name() {
        if constexpr (Category == ErrorCategory::CAMERA_ERROR) return "CAMERA";
        else if constexpr (Category == ErrorCategory::INFERENCE_ERROR) return "INFERENCE";
        else if constexpr (Category == ErrorCategory::RECONSTRUCTION_ERROR) return "RECONSTRUCTION";
        else if constexpr (Category == ErrorCategory::RECORDING_ERROR) return "RECORDING";
        else if constexpr (Category == ErrorCategory::DATABASE_ERROR) return "DATABASE";
        else if constexpr (Category == ErrorCategory::NETWORK_ERROR) return "NETWORK";
        else if constexpr (Category == ErrorCategory::CALIBRATION_ERROR) return "CALIBRATION";
        else if constexpr (Category == ErrorCategory::SYSTEM_ERROR) return "SYSTEM";
        else return "UNKNOWN";
    }
    
    static constexpr uint8_t priority() {
        if constexpr (Category == ErrorCategory::CAMERA_ERROR) return 1; // 最高优先级
        else if constexpr (Category == ErrorCategory::INFERENCE_ERROR) return 2;
        else if constexpr (Category == ErrorCategory::RECONSTRUCTION_ERROR) return 3;
        else return 5; // 默认优先级
    }
};

/**
 * @brief 高性能错误记录宏
 * 
 * 使用模板和constexpr实现编译时优化
 */
#define FAST_ERROR_CONTEXT(name, category) \
    FastErrorContext<> _fast_ctx(name, ErrorCategory::category)

#define LOG_FAST_ERROR(category, message) \
    do { \
        if (!HighPerformanceErrorHandler::getInstance().shouldSuppressError(ErrorCategory::category)) { \
            HighPerformanceErrorHandler::getInstance().logFastError( \
                ErrorCategory::category, ErrorSeverity::ERROR, message); \
        } \
    } while(0)

#define LOG_FAST_WARNING(category, message) \
    do { \
        if (!HighPerformanceErrorHandler::getInstance().shouldSuppressError(ErrorCategory::category)) { \
            HighPerformanceErrorHandler::getInstance().logFastError( \
                ErrorCategory::category, ErrorSeverity::WARNING, message); \
        } \
    } while(0)

/**
 * @brief 性能监控和告警系统
 */
class PerformanceMonitor {
private:
    struct PerformanceMetrics {
        std::atomic<uint64_t> frame_processing_time_ns{0};
        std::atomic<uint64_t> error_handling_time_ns{0};
        std::atomic<uint64_t> total_frames{0};
        std::atomic<uint64_t> dropped_frames{0};
        std::chrono::steady_clock::time_point start_time;
        
        PerformanceMetrics() : start_time(std::chrono::steady_clock::now()) {}
    };
    
    PerformanceMetrics metrics_;
    std::atomic<bool> performance_alert_active_{false};
    
    // 性能阈值
    static constexpr uint64_t MAX_FRAME_TIME_NS = 4760000; // 4.76ms
    static constexpr uint64_t MAX_ERROR_HANDLING_TIME_NS = 100000; // 0.1ms
    static constexpr double MAX_DROP_RATE = 0.01; // 1%
    
public:
    /**
     * @brief 记录帧处理时间
     */
    void recordFrameProcessingTime(uint64_t time_ns);
    
    /**
     * @brief 记录错误处理时间
     */
    void recordErrorHandlingTime(uint64_t time_ns);
    
    /**
     * @brief 记录丢帧
     */
    void recordDroppedFrame();
    
    /**
     * @brief 检查性能是否满足210FPS要求
     */
    bool isPerformanceAcceptable() const;
    
    /**
     * @brief 获取性能报告
     */
    struct PerformanceReport {
        double average_frame_time_ms;
        double average_error_handling_time_ms;
        double current_fps;
        double drop_rate;
        bool performance_alert;
        std::chrono::duration<double> uptime;
    };
    
    PerformanceReport generateReport() const;
    
    /**
     * @brief 重置性能统计
     */
    void reset();
};

} // namespace ErrorManagement
