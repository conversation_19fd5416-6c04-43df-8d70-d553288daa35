<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>轨迹修复快速验证</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1a1a2e;
            color: white;
            font-family: Arial, sans-serif;
        }
        
        .test-container {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .camera-container {
            position: relative;
            width: 320px;
            height: 240px;
            border: 2px solid #333;
            background: #000;
        }
        
        .trajectory-canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }
        
        .controls {
            margin: 20px 0;
        }
        
        .btn {
            padding: 10px 20px;
            margin: 5px;
            background: #0066cc;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        
        .btn:hover {
            background: #0088ff;
        }
        
        .btn.danger {
            background: #cc0000;
        }
        
        .btn.danger:hover {
            background: #ff0000;
        }
        
        .status {
            background: #333;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .log {
            background: #000;
            padding: 10px;
            border-radius: 5px;
            height: 150px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        
        .highlight {
            color: #00ff88;
            font-weight: bold;
        }
        
        .error {
            color: #ff4444;
            font-weight: bold;
        }
        
        .warning {
            color: #ffaa00;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>🎾 轨迹修复快速验证</h1>
    <p>此页面用于快速验证轨迹显示修复效果，特别是球消失后静止点的处理。</p>
    
    <div class="test-container">
        <div class="camera-container">
            <canvas class="trajectory-canvas" id="leftTrajectoryCanvas" width="320" height="240"></canvas>
            <div style="position: absolute; bottom: 5px; left: 5px; background: rgba(0,0,0,0.7); padding: 5px; border-radius: 3px;">
                摄像头 1
            </div>
        </div>
        
        <div class="camera-container">
            <canvas class="trajectory-canvas" id="rightTrajectoryCanvas" width="320" height="240"></canvas>
            <div style="position: absolute; bottom: 5px; left: 5px; background: rgba(0,0,0,0.7); padding: 5px; border-radius: 3px;">
                摄像头 2
            </div>
        </div>
    </div>
    
    <div class="controls">
        <h3>测试场景</h3>
        <button class="btn" onclick="testMovingBall()">1. 球移动测试</button>
        <button class="btn" onclick="testStaticBall()">2. 球静止测试</button>
        <button class="btn" onclick="testBallDisappear()">3. 球消失测试</button>
        <button class="btn danger" onclick="clearAll()">清除所有</button>
    </div>
    
    <div class="status">
        <h3>测试状态</h3>
        <div id="testStatus">点击上方按钮开始测试</div>
    </div>
    
    <div class="log" id="logContainer">
        <div class="highlight">快速验证日志:</div>
        <div>请按顺序执行测试场景，观察轨迹显示效果</div>
    </div>

    <script>
        // 简化的轨迹系统
        class QuickTrajectoryTester {
            constructor() {
                this.trajectoryData2D = {
                    1: { points: [], width: 320, height: 240 },
                    2: { points: [], width: 320, height: 240 }
                };
                
                this.trajectoryConfig = {
                    maxAge: 2000,
                    maxPoints: 200,
                    baseLineWidth: 3,
                    minOpacity: 0.1,
                    glowIntensity: 0.5,
                    pointRadius: 4,
                    pointShowDuration: 1500,
                    staticPointDuration: 3000,
                    colors: {
                        trajectory: [255, 87, 34],
                        point: [255, 255, 255],
                        staticPoint: [255, 255, 0],
                        glow: [255, 87, 34]
                    }
                };
                
                this.ballState = {
                    1: { lastPosition: null, lastUpdateTime: 0, isStatic: false, ballExists: false },
                    2: { lastPosition: null, lastUpdateTime: 0, isStatic: false, ballExists: false }
                };
                
                this.leftTrajectoryCanvas = document.getElementById('leftTrajectoryCanvas');
                this.rightTrajectoryCanvas = document.getElementById('rightTrajectoryCanvas');
                this.leftTrajectoryCtx = this.leftTrajectoryCanvas?.getContext('2d');
                this.rightTrajectoryCtx = this.rightTrajectoryCanvas?.getContext('2d');
                
                this.currentTest = null;
                this.testInterval = null;
                
                // 开始清理循环
                setInterval(() => this.cleanup2DTrajectories(), 50);
                
                this.log('快速验证系统已初始化');
            }
            
            log(message, type = 'info') {
                const logContainer = document.getElementById('logContainer');
                const timestamp = new Date().toLocaleTimeString();
                let className = '';
                
                if (type === 'error') className = 'error';
                else if (type === 'warning') className = 'warning';
                else if (type === 'success') className = 'highlight';
                
                logContainer.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
                logContainer.scrollTop = logContainer.scrollHeight;
            }
            
            updateStatus(status) {
                document.getElementById('testStatus').textContent = status;
            }
            
            // 模拟球移动
            simulateMovingBall() {
                this.log('开始模拟球移动', 'success');
                this.updateStatus('测试中：球移动');
                
                let angle = 0;
                const centerX = 160;
                const centerY = 120;
                const radius = 60;
                
                this.testInterval = setInterval(() => {
                    const x = centerX + Math.cos(angle) * radius;
                    const y = centerY + Math.sin(angle) * radius;
                    
                    // 更新球状态
                    for (const cameraId in this.ballState) {
                        this.ballState[cameraId].ballExists = true;
                        this.ballState[cameraId].isStatic = false;
                    }
                    
                    // 添加轨迹点
                    this.addTrajectoryPoint(1, x, y);
                    this.addTrajectoryPoint(2, x + 10, y + 5);
                    
                    angle += 0.1;
                }, 50);
                
                // 5秒后停止
                setTimeout(() => {
                    if (this.testInterval) {
                        clearInterval(this.testInterval);
                        this.testInterval = null;
                        this.log('球移动测试完成', 'success');
                        this.updateStatus('球移动测试完成 - 观察轨迹淡出效果');
                    }
                }, 5000);
            }
            
            // 模拟球静止
            simulateStaticBall() {
                this.log('开始模拟球静止', 'success');
                this.updateStatus('测试中：球静止');
                
                const x = 200;
                const y = 100;
                
                // 添加轨迹点
                this.addTrajectoryPoint(1, x, y);
                this.addTrajectoryPoint(2, x + 10, y + 5);
                
                // 设置球为静止状态
                setTimeout(() => {
                    for (const cameraId in this.ballState) {
                        this.ballState[cameraId].ballExists = true;
                        this.ballState[cameraId].isStatic = true;
                    }
                    
                    // 添加静止点
                    this.addStaticPoint(1, x, y);
                    this.addStaticPoint(2, x + 10, y + 5);
                    
                    this.log('已添加静止点 - 应显示黄色脉动圆点', 'success');
                    this.updateStatus('球静止测试 - 观察黄色静止点显示');
                }, 100);
            }
            
            // 模拟球消失
            simulateBallDisappear() {
                this.log('开始模拟球消失', 'warning');
                this.updateStatus('测试中：球消失');
                
                // 设置球为消失状态
                for (const cameraId in this.ballState) {
                    this.ballState[cameraId].ballExists = false;
                    this.ballState[cameraId].isStatic = false;
                }
                
                // 清理静止点
                this.clearAllStaticPoints();
                
                this.log('球已消失 - 静止点应立即清除', 'warning');
                this.updateStatus('球消失测试 - 静止点应已清除，轨迹正常淡出');
            }
            
            // 添加轨迹点
            addTrajectoryPoint(cameraId, x, y) {
                const currentTime = Date.now();
                const trajectoryInfo = this.trajectoryData2D[cameraId];
                
                if (trajectoryInfo) {
                    trajectoryInfo.points.push({
                        x: x,
                        y: y,
                        timestamp: currentTime,
                        isStatic: false
                    });
                    
                    this.drawTrajectory(cameraId);
                }
            }
            
            // 添加静止点
            addStaticPoint(cameraId, x, y) {
                const currentTime = Date.now();
                const trajectoryInfo = this.trajectoryData2D[cameraId];
                
                if (trajectoryInfo) {
                    // 清除现有静止点
                    trajectoryInfo.points = trajectoryInfo.points.filter(p => !p.isStatic);
                    
                    // 添加新静止点
                    trajectoryInfo.points.push({
                        x: x,
                        y: y,
                        timestamp: currentTime,
                        isStatic: true,
                        staticStartTime: currentTime
                    });
                    
                    this.drawTrajectory(cameraId);
                }
            }
            
            // 清除所有静止点
            clearAllStaticPoints() {
                for (const cameraId in this.trajectoryData2D) {
                    const trajectoryInfo = this.trajectoryData2D[cameraId];
                    const oldLength = trajectoryInfo.points.length;
                    
                    trajectoryInfo.points = trajectoryInfo.points.filter(p => !p.isStatic);
                    
                    const newLength = trajectoryInfo.points.length;
                    if (oldLength !== newLength) {
                        this.log(`清除摄像头${cameraId}的${oldLength - newLength}个静止点`, 'warning');
                        this.drawTrajectory(parseInt(cameraId));
                    }
                }
            }
            
            // 绘制轨迹（简化版本）
            drawTrajectory(cameraId) {
                const trajectoryInfo = this.trajectoryData2D[cameraId];
                if (!trajectoryInfo) return;

                let canvas, ctx;
                if (cameraId === 1) {
                    canvas = this.leftTrajectoryCanvas;
                    ctx = this.leftTrajectoryCtx;
                } else if (cameraId === 2) {
                    canvas = this.rightTrajectoryCanvas;
                    ctx = this.rightTrajectoryCtx;
                } else {
                    return;
                }

                if (!canvas || !ctx) return;

                ctx.clearRect(0, 0, canvas.width, canvas.height);

                const currentTime = Date.now();
                const config = this.trajectoryConfig;

                // 过滤有效轨迹点
                const validPoints = trajectoryInfo.points.filter(point => {
                    if (point.isStatic && point.staticStartTime) {
                        return currentTime - point.staticStartTime < config.staticPointDuration;
                    }
                    return currentTime - point.timestamp < config.maxAge;
                });

                if (validPoints.length === 0) return;

                ctx.lineCap = 'round';
                ctx.lineJoin = 'round';

                // 绘制轨迹线
                for (let i = 1; i < validPoints.length; i++) {
                    const prevPoint = validPoints[i - 1];
                    const currPoint = validPoints[i];

                    if (currPoint.isStatic || prevPoint.isStatic) continue;

                    const age = currentTime - currPoint.timestamp;
                    const normalizedAge = Math.min(age / config.maxAge, 1);
                    const opacity = Math.max(config.minOpacity, 1 - normalizedAge);

                    const [r, g, b] = config.colors.trajectory;
                    ctx.strokeStyle = `rgba(${r}, ${g}, ${b}, ${opacity})`;
                    ctx.lineWidth = Math.max(1, config.baseLineWidth * opacity);

                    ctx.beginPath();
                    ctx.moveTo(prevPoint.x, prevPoint.y);
                    ctx.lineTo(currPoint.x, currPoint.y);
                    ctx.stroke();
                }

                // 绘制静止点
                validPoints.forEach(point => {
                    if (point.isStatic) {
                        const staticAge = currentTime - point.staticStartTime;
                        const staticOpacity = Math.max(0.4, 1 - (staticAge / config.staticPointDuration));
                        const [sr, sg, sb] = config.colors.staticPoint;

                        const pulseScale = 1 + 0.3 * Math.sin(currentTime * 0.01);

                        // 外圈
                        ctx.fillStyle = `rgba(${sr}, ${sg}, ${sb}, ${staticOpacity * 0.4})`;
                        ctx.beginPath();
                        ctx.arc(point.x, point.y, config.pointRadius * 2 * pulseScale * staticOpacity, 0, 2 * Math.PI);
                        ctx.fill();

                        // 内圈
                        ctx.fillStyle = `rgba(${sr}, ${sg}, ${sb}, ${staticOpacity})`;
                        ctx.beginPath();
                        ctx.arc(point.x, point.y, config.pointRadius * staticOpacity, 0, 2 * Math.PI);
                        ctx.fill();
                    }
                });
            }
            
            // 清理过期轨迹点
            cleanup2DTrajectories() {
                const currentTime = Date.now();
                const config = this.trajectoryConfig;

                for (const cameraIdStr in this.trajectoryData2D) {
                    const cameraId = parseInt(cameraIdStr);
                    const trajectoryInfo = this.trajectoryData2D[cameraId];
                    
                    if (!trajectoryInfo || !trajectoryInfo.points) continue;

                    const oldLength = trajectoryInfo.points.length;

                    trajectoryInfo.points = trajectoryInfo.points.filter(point => {
                        if (!point || typeof point.timestamp !== 'number') {
                            return false;
                        }
                        
                        if (point.isStatic && point.staticStartTime) {
                            return currentTime - point.staticStartTime < config.staticPointDuration;
                        }
                        
                        return currentTime - point.timestamp < config.maxAge;
                    });

                    const newLength = trajectoryInfo.points.length;
                    if (oldLength !== newLength) {
                        this.drawTrajectory(cameraId);
                    }
                }
            }
            
            // 清除所有轨迹
            clearAll() {
                if (this.testInterval) {
                    clearInterval(this.testInterval);
                    this.testInterval = null;
                }
                
                for (const cameraId in this.trajectoryData2D) {
                    this.trajectoryData2D[cameraId].points = [];
                    this.drawTrajectory(parseInt(cameraId));
                }
                
                for (const cameraId in this.ballState) {
                    this.ballState[cameraId] = {
                        lastPosition: null,
                        lastUpdateTime: 0,
                        isStatic: false,
                        ballExists: false
                    };
                }
                
                this.log('已清除所有轨迹和状态', 'success');
                this.updateStatus('已清除所有内容');
            }
        }
        
        // 初始化测试器
        const tester = new QuickTrajectoryTester();
        
        // 测试函数
        function testMovingBall() {
            tester.clearAll();
            setTimeout(() => tester.simulateMovingBall(), 100);
        }
        
        function testStaticBall() {
            tester.clearAll();
            setTimeout(() => tester.simulateStaticBall(), 100);
        }
        
        function testBallDisappear() {
            // 先添加静止点，然后模拟消失
            tester.simulateStaticBall();
            setTimeout(() => tester.simulateBallDisappear(), 2000);
        }
        
        function clearAll() {
            tester.clearAll();
        }
    </script>
</body>
</html>
