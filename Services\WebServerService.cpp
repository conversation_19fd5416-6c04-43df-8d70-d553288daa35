#include "WebServerService.hpp"
#include "CalibrationService.hpp"
#include "HighlightService.hpp"
#include "../Utils/utf8_utils.hpp"
#include "sqlite3.h"
#include <nlohmann/json.hpp>
#include <iostream>
#include <fstream>
#include <chrono>
#include <sstream>
#include <thread>
#include <sys/stat.h> // For checking/creating directory
#include <filesystem> // For creating directory in C++17
#include <algorithm>
#include <cctype>

#ifdef _WIN32
#include <windows.h>
#include <io.h>
#include <fcntl.h>
#include <direct.h>
#define getcwd _getcwd
#else
#include <unistd.h>
#endif

const int WebServerService::JPEG_QUALITY = 80;
const int WebServerService::TARGET_FPS = 90;

WSConnection::WSConnection(crow::websocket::connection* c, const std::string& connection_id) 
    : conn(c), id(connection_id), active(true), last_activity(std::chrono::steady_clock::now()) {}

bool WebServerService::hasExtension(const std::string& filename, const std::string& ext) {
    if (filename.length() >= ext.length()) {
        return filename.compare(filename.length() - ext.length(), ext.length(), ext) == 0;
    }
    return false;
}

void WebServerService::initConsole() {
#ifdef _WIN32
    SetConsoleOutputCP(CP_UTF8);
    SetConsoleCP(CP_UTF8);
#endif
}

bool WebServerService::fileExists(const std::string& path) {
    std::ifstream file(path);
    return file.good();
}

std::string WebServerService::findFrontendPath() {
    std::vector<std::string> possiblePaths = {
        "frontend",
        "../frontend",
        "../../frontend",
        "../../../frontend",
        "Web/frontend",
        "../Web/frontend",
        "./Web/frontend",
        "../../Web/frontend",
        "../../../Web/frontend",
        "C:/Dev/Camera_Editor/Web/frontend"
    };

    char cwd[1024];
    if (getcwd(cwd, sizeof(cwd)) != nullptr) {
        UTF8Utils::println("Current working directory: " + std::string(cwd));
    }

    for (const auto& path : possiblePaths) {
        std::string testFile = path + "/index.html";
        UTF8Utils::println("Trying path: " + testFile);
        if (fileExists(testFile)) {
            UTF8Utils::println("Frontend path found: " + path);
            return path;
        }
    }

    UTF8Utils::println("Warning: Frontend path not found, trying default...");
    return "frontend";
}

void WebServerService::startVideoStream() {
    if (video_streaming) return;

    // 先确保之前的线程已完全停止
    stopVideoStream();

    video_streaming = true;
    UTF8Utils::println("📹 启动并行视频流 (" + std::to_string(TARGET_FPS) + "fps per camera)");

    for (int cam_id : {1, 2}) {
        video_stream_threads.emplace_back([this, cam_id]() {
            UTF8Utils::println("   -> 启动摄像头 " + std::to_string(cam_id) + " 的视频流线程.");
            auto frame_duration = std::chrono::microseconds(1000000 / TARGET_FPS);
            
            // 状态更新计时器，仅由摄像头1的线程使用
            auto last_status_update = std::chrono::steady_clock::now();

            while (video_streaming) {
                auto next_frame_time = std::chrono::steady_clock::now() + frame_duration;

                // 1. 核心任务：处理并广播此摄像头的视频帧
                if (!ws_connections.empty()) {
                    cv::Mat frame = shared_data_->getLatestFrame(cam_id);
                    if (!frame.empty()) {
                        captureAndBroadcastFrame(cam_id, frame);

                        // --- 新增：广播2D轨迹数据 ---
                        auto trajectories_2d = shared_data_->get2dTrajectories();
                        if (trajectories_2d.count(cam_id) && !trajectories_2d.at(cam_id).empty()) {
                            nlohmann::json msg;
                            msg["type"] = "trajectory_2d_update";
                            msg["camera_id"] = cam_id;

                            nlohmann::json points = nlohmann::json::array();
                            for (const auto& p : trajectories_2d.at(cam_id)) {
                                points.push_back({{"x", p.x}, {"y", p.y}});
                            }
                            msg["trajectory"] = points;
                            
                            // 获取原始图像尺寸并发送，用于前端缩放
                            msg["original_width"] = shared_data_->getLatestFrame(cam_id).cols;
                            msg["original_height"] = shared_data_->getLatestFrame(cam_id).rows;
                            
                            broadcastToWebSocketClients(msg.dump());
                        }
                        // --------------------------
                    }
                }

                // 2. 特殊任务：仅让摄像头1的线程负责发送公共状态数据，避免消息重复
                if (cam_id == 1) {
                    auto now = std::chrono::steady_clock::now();
                    if (std::chrono::duration_cast<std::chrono::seconds>(now - last_status_update).count() >= 1) {
                        last_status_update = now;
                        if (!ws_connections.empty()) {
                            // 发送FPS和球速等状态
                            crow::json::wvalue status;
                            status["type"] = "status_update";
                            auto fps_map = shared_data_->getActualFpsMap();
                            for(auto const& [id, fps] : fps_map) {
                                status["fps"][std::to_string(id)] = fps;
                            }
                            status["ball_speed"] = shared_data_->getBallSpeed();
                            broadcastToWebSocketClients(status.dump());

                            // 发送3D坐标数据
                            if (shared_data_->has3DData()) {
                                broadcast3DCoordinates();
                            }
                            
                            // 发送轨迹数据
                            broadcastTrajectory();

                            // 发送精彩录制状态
                            broadcastHighlightStatus();
                        }
                    }
                }
                
                std::this_thread::sleep_until(next_frame_time);
            }
            UTF8Utils::println("   -> 摄像头 " + std::to_string(cam_id) + " 的视频流线程已停止.");
        });
    }
}

void WebServerService::captureAndBroadcastFrame(int camera_id, const cv::Mat& frame) {
    cv::Mat frame_to_send = frame.clone();

    // === 新增：在画面左上角绘制实时球速信息 ===
    drawBallSpeedOverlay(frame_to_send, camera_id);

    // 根据摄像头ID获取对应的检测结果
    auto result_opt = shared_data_->getLatestDetectionResult(camera_id);

    // 如果该摄像头有检测结果，则绘制出来
    if (result_opt) {
        const auto& result = *result_opt;
        for (size_t i = 0; i < result.boxes.size(); ++i) {
            try {
                // 安全检查：确保索引在所有相关向量的边界内
                if (i >= result.confidences.size() || i >= result.class_names.size()) {
                    UTF8Utils::println("Error: Inconsistent detection data for camera " + std::to_string(camera_id) + ". Skipping box " + std::to_string(i) + ".");
                    continue; // 如果数据不一致，则跳过此检测框的绘制
                }

                // 绘制检测框
                cv::rectangle(frame_to_send, result.boxes[i], cv::Scalar(0, 255, 0), 2);
                
                // 安全检查：确保类名非空
                if (result.class_names[i].empty()) {
                    continue;
                }

                // 准备标签文本
                std::string label = result.class_names[i] + " " + std::to_string(result.confidences[i]).substr(0, 4);
                
                // 绘制标签背景
                int baseline;
                cv::Size label_size = cv::getTextSize(label, cv::FONT_HERSHEY_SIMPLEX, 0.5, 1, &baseline);
                cv::rectangle(frame_to_send, 
                            cv::Point(result.boxes[i].x, result.boxes[i].y - label_size.height - 5),
                            cv::Point(result.boxes[i].x + label_size.width, result.boxes[i].y),
                            cv::Scalar(0, 255, 0),
                            cv::FILLED);

                // 绘制标签文本
                cv::putText(frame_to_send, label, 
                            cv::Point(result.boxes[i].x, result.boxes[i].y - 5), 
                            cv::FONT_HERSHEY_SIMPLEX, 0.5, cv::Scalar(0, 0, 0), 1);
            } catch (const std::exception& e) {
                UTF8Utils::println("!!! CRITICAL: Exception caught while drawing box " + std::to_string(i) + " for camera " + std::to_string(camera_id) + ". Details: " + e.what());
            } catch (...) {
                UTF8Utils::println("!!! CRITICAL: Unknown exception caught while drawing box " + std::to_string(i) + " for camera " + std::to_string(camera_id) + ".");
            }
        }
    }

    std::vector<uchar> buffer;
    // 使用与之前相同的JPEG参数进行编码
    cv::imencode(".jpg", frame_to_send, buffer, {
        cv::IMWRITE_JPEG_QUALITY, JPEG_QUALITY,
        cv::IMWRITE_JPEG_OPTIMIZE, 1,
        cv::IMWRITE_JPEG_PROGRESSIVE, 0
    });
    
    // 获取当前时间戳
    auto now = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();
    
    // 创建WebSocket消息并广播
    std::vector<uint8_t> ws_message = createWebSocketMessage(camera_id, now, buffer);
    broadcastToWebSocketClients(ws_message);
}

std::vector<uint8_t> WebServerService::createWebSocketMessage(int camera_id, uint64_t timestamp, 
                                           const std::vector<uchar>& jpeg_data) {
    std::vector<uint8_t> message;
    
    uint32_t cam_id = static_cast<uint32_t>(camera_id);
    message.insert(message.end(), reinterpret_cast<uint8_t*>(&cam_id), 
                   reinterpret_cast<uint8_t*>(&cam_id) + 4);
    
    message.insert(message.end(), reinterpret_cast<uint8_t*>(&timestamp), 
                   reinterpret_cast<uint8_t*>(&timestamp) + 8);
    
    message.insert(message.end(), jpeg_data.begin(), jpeg_data.end());
    
    return message;
}

void WebServerService::broadcastToWebSocketClients(const std::vector<uint8_t>& message) {
    std::lock_guard<std::mutex> lock(ws_connections_mutex);
    
    auto it = ws_connections.begin();
    while (it != ws_connections.end()) {
        WSConnection* conn = *it;
        
        if (conn->active && conn->conn) {
            try {
                conn->conn->send_binary(std::string(message.begin(), message.end()));
                conn->last_activity = std::chrono::steady_clock::now();
                ++it;
            } catch (const std::exception& e) {
                UTF8Utils::println("⚠️ WebSocket发送失败，移除连接: " + conn->id);
                conn->active = false;
                delete conn;
                it = ws_connections.erase(it);
            }
        } else {
            delete conn;
            it = ws_connections.erase(it);
        }
    }
}

void WebServerService::broadcastToWebSocketClients(const std::string& message) {
    std::lock_guard<std::mutex> lock(ws_connections_mutex);
    
    auto it = ws_connections.begin();
    while (it != ws_connections.end()) {
        WSConnection* conn = *it;
        
        if (conn->active && conn->conn) {
            try {
                conn->conn->send_text(message);
                conn->last_activity = std::chrono::steady_clock::now();
                ++it;
            } catch (const std::exception& e) {
                UTF8Utils::println("⚠️ WebSocket text send failed, removing connection: " + conn->id);
                conn->active = false;
                delete conn;
                it = ws_connections.erase(it);
            }
        } else {
            delete conn;
            it = ws_connections.erase(it);
        }
    }
}

void WebServerService::broadcast3DCoordinates() {
    auto ball_positions = shared_data_->getBallPositions3D();
    if (ball_positions.empty()) return;

    crow::json::wvalue msg;
    msg["type"] = "coordinates_update";
    
    std::vector<crow::json::wvalue> balls_vec;
    balls_vec.reserve(ball_positions.size());

    for (const auto& ball : ball_positions) {
        crow::json::wvalue ball_data;
        ball_data["id"] = ball.id;
        ball_data["x"] = ball.world_position.x;
        ball_data["y"] = ball.world_position.y;
        ball_data["z"] = ball.world_position.z;
        ball_data["confidence"] = ball.confidence;
        ball_data["timestamp"] = ball.timestamp.count();
        balls_vec.push_back(std::move(ball_data));
    }
    msg["balls"] = std::move(balls_vec);
    
    // 也顺便广播最新的球速
    msg["ball_speed"] = shared_data_->getBallSpeed();

    broadcastToWebSocketClients(msg.dump());
}

void WebServerService::broadcastTrajectory() {
    auto trajectory = shared_data_->getTrajectory();
    if (trajectory.empty()) {
        return;
    }

    nlohmann::json msg;
    msg["type"] = "trajectory_update";
    
    nlohmann::json points = nlohmann::json::array();
    for (const auto& pos : trajectory) {
        nlohmann::json point;
        point["x"] = pos.world_position.x;
        point["y"] = pos.world_position.y;
        point["z"] = pos.world_position.z;
        // The timestamp is already in milliseconds, which is great for JS
        point["t"] = pos.timestamp.count(); 
        points.push_back(point);
    }
    msg["trajectory"] = points;

    broadcastToWebSocketClients(msg.dump());
}

void WebServerService::stopVideoStream() {
    if (!video_streaming && video_stream_threads.empty()) return;

    video_streaming = false;
    for (auto& t : video_stream_threads) {
        if (t.joinable()) {
            t.join();
        }
    }
    video_stream_threads.clear();
    if(video_stream_threads.empty()){
         UTF8Utils::println("🛑 所有视频流线程已成功停止和清理。");
    }
}

void WebServerService::handleWebSocketMessage(WSConnection* conn, const std::string& data) {
    if (!conn) return;

    try {
        auto json_msg = nlohmann::json::parse(data);

        if (json_msg.contains("command")) {
            std::string command = json_msg["command"];
            UTF8Utils::println("🔵 [WebSocket] 收到命令: " + command);

            if (command == "START_SYSTEM") {
                UTF8Utils::println("   -> 命令解析为: START_SYSTEM. 设置系统运行状态为 true 并启动视频流。");
                shared_data_->setSystemRunning(true);
                startVideoStream(); // 启动视频流广播
                broadcastToWebSocketClients("{\"type\": \"status_update\", \"system_status\": \"running\"}");
            } else if (command == "STOP_SYSTEM") {
                UTF8Utils::println("   -> 命令解析为: STOP_SYSTEM. 设置系统运行状态为 false 并停止视频流。");
                shared_data_->setSystemRunning(false);
                stopVideoStream(); // 停止视频流广播
                broadcastToWebSocketClients("{\"type\": \"status_update\", \"system_status\": \"stopped\"}");
            } else if (command == "START_VIDEO") {
                UTF8Utils::println("   -> 命令解析为: START_VIDEO. 启动视频流...");
                startVideoStream();
                broadcastToWebSocketClients("{\"type\": \"status_update\", \"video_stream\": \"started\"}");
            } else if (command == "STOP_VIDEO") {
                UTF8Utils::println("   -> 命令解析为: STOP_VIDEO. 停止视频流...");
                stopVideoStream();
                broadcastToWebSocketClients("{\"type\": \"status_update\", \"video_stream\": \"stopped\"}");
            } else if (command.rfind("START_RECORDING_", 0) == 0) {
                try {
                    int cam_id = std::stoi(command.substr(16));
                    UTF8Utils::println("   -> 命令解析为: START_RECORDING for camera " + std::to_string(cam_id));
                    recording_service_->startRecording(cam_id);
                    UTF8Utils::println("   -> 命令解析为: STOP_RECORDING for camera " + std::to_string(cam_id));
                    recording_service_->stopRecording(cam_id);
                } catch (const std::exception& e) {
                     UTF8Utils::println("   -> ❌ 解析停止录制命令失败: " + command);
                }
            } else if (command == "START_HIGHLIGHT_RECORDING") {
                UTF8Utils::println("   -> 命令解析为: START_HIGHLIGHT_RECORDING");
                if (highlight_service_) {
                    double speed_threshold = 15.0;  // 默认阈值
                    int camera_id = 1;  // 默认左摄像头

                    // 从JSON消息中获取参数
                    if (json_msg.contains("speed_threshold")) {
                        speed_threshold = json_msg["speed_threshold"].get<double>();
                    }
                    if (json_msg.contains("camera_id")) {
                        camera_id = json_msg["camera_id"].get<int>();
                    }

                    bool success = highlight_service_->startHighlightRecording(speed_threshold, camera_id);

                    // 发送状态更新
                    crow::json::wvalue response;
                    response["type"] = "highlight_status_update";
                    response["recording_state"] = success ? "RECORDING" : "ERROR";
                    if (!success && highlight_service_->hasError()) {
                        response["error"] = highlight_service_->getErrorMessage();
                    }
                    broadcastToWebSocketClients(response.dump());
                } else {
                    UTF8Utils::println("   -> ❌ HighlightService未设置");
                    crow::json::wvalue response;
                    response["type"] = "highlight_status_update";
                    response["recording_state"] = "ERROR";
                    response["error"] = "HighlightService not available";
                    broadcastToWebSocketClients(response.dump());
                }
            } else if (command == "STOP_HIGHLIGHT_RECORDING") {
                UTF8Utils::println("   -> 命令解析为: STOP_HIGHLIGHT_RECORDING");
                if (highlight_service_) {
                    bool success = highlight_service_->stopHighlightRecording();

                    // 发送状态更新
                    crow::json::wvalue response;
                    response["type"] = "highlight_status_update";
                    response["recording_state"] = success ? "PROCESSING" : "ERROR";
                    if (!success && highlight_service_->hasError()) {
                        response["error"] = highlight_service_->getErrorMessage();
                    }
                    broadcastToWebSocketClients(response.dump());
                } else {
                    UTF8Utils::println("   -> ❌ HighlightService未设置");
                    crow::json::wvalue response;
                    response["type"] = "highlight_status_update";
                    response["recording_state"] = "ERROR";
                    response["error"] = "HighlightService not available";
                    broadcastToWebSocketClients(response.dump());
                }
            } else if (command == "SET_DETECTION_RECORDING") {
                UTF8Utils::println("   -> 命令解析为: SET_DETECTION_RECORDING");
                if (recording_service_) {
                    bool enable = json_msg.count("enable") ? json_msg["enable"].get<bool>() : false;
                    std::string mode_str = json_msg.count("mode") ? json_msg["mode"].get<std::string>() : "RAW_MODE";

                    // 设置录制模式
                    RecordingMode mode = (mode_str == "DETECTION_MODE") ?
                        RecordingMode::DETECTION_MODE : RecordingMode::RAW_MODE;
                    recording_service_->setRecordingMode(mode);

                    // 启用/禁用检测框录制
                    recording_service_->enableDetectionRecording(enable);

                    // 发送状态更新
                    crow::json::wvalue response;
                    response["type"] = "detection_recording_status";
                    response["enabled"] = enable;
                    response["mode"] = mode_str;
                    response["message"] = "Detection recording mode updated";
                    broadcastToWebSocketClients(response.dump());
                } else {
                    UTF8Utils::println("   -> ❌ RecordingService未设置");
                    crow::json::wvalue response;
                    response["type"] = "detection_recording_status";
                    response["error"] = "RecordingService not available";
                    broadcastToWebSocketClients(response.dump());
                }
            } else {
                UTF8Utils::println("   -> ⚠️ 收到未知命令: " + command);
            }
        } else if (json_msg.contains("type")) {
            std::string type = json_msg["type"];
            UTF8Utils::println("🔵 [WebSocket] 收到类型消息: " + type);

            if (type == "start_calibration") {
                UTF8Utils::println("   -> 开始相机标定");
                if (calibration_service_) {
                    bool success = calibration_service_->startCalibration([this](const Services::CalibrationResult& result) {
                        // 标定完成回调
                        nlohmann::json msg;
                        msg["type"] = "calibration_result";
                        msg["status"] = (result.status == Services::CalibrationStatus::SUCCESS) ? "success" : "failed";
                        msg["mean_error"] = static_cast<double>(result.mean_error);
                        msg["detected_corners"] = static_cast<int>(result.detected_corners);
                        msg["completion_time"] = static_cast<int64_t>(std::chrono::duration_cast<std::chrono::milliseconds>(
                            std::chrono::system_clock::now().time_since_epoch()).count());

                        broadcastToWebSocketClients(msg.dump());
                    });

                    if (success) {
                        nlohmann::json msg;
                        msg["type"] = "calibration_status";
                        msg["status"] = "detecting_chessboard";
                        msg["message"] = "开始检测标定板...";
                        broadcastToWebSocketClients(msg.dump());
                    } else {
                        crow::json::wvalue msg;
                        msg["type"] = "calibration_status";
                        msg["status"] = "failed_no_chessboard";
                        msg["message"] = "标定启动失败";
                        broadcastToWebSocketClients(msg.dump());
                    }
                } else {
                    UTF8Utils::println("   -> ❌ 标定服务未初始化");
                }
            } else if (type == "stop_calibration") {
                UTF8Utils::println("   -> 停止相机标定");
                if (calibration_service_) {
                    calibration_service_->stopCalibration();
                    crow::json::wvalue msg;
                    msg["type"] = "calibration_status";
                    msg["status"] = "idle";
                    msg["message"] = "标定已停止";
                    broadcastToWebSocketClients(msg.dump());
                } else {
                    UTF8Utils::println("   -> ❌ 标定服务未初始化");
                }
            }
        } else if (json_msg.contains("confidence_threshold")) {
            float threshold = static_cast<float>(json_msg["confidence_threshold"].get<double>());
            UTF8Utils::println("🔵 [WebSocket] 收到阈值更新: " + std::to_string(threshold));
            shared_data_->setConfidenceThreshold(threshold);
        }
    } catch (const std::exception& e) {
        UTF8Utils::println("WebSocket 消息解析失败: " + std::string(e.what()));
    }
}

WebServerService::WebServerService(std::shared_ptr<SharedData> shared_data, std::shared_ptr<RecordingService> recording_service) 
    : shared_data_(shared_data), recording_service_(recording_service), video_streaming(false), db_path_("C:/Dev/Camera_Editor/Data/telemetry.db") {
    initConsole();
    UTF8Utils::println("WebServerService constructor called.");
    frontend_path = findFrontendPath();
    
    UTF8Utils::println("🗃️ Database path set to: " + db_path_);
    if (!fileExists(db_path_)) {
        UTF8Utils::println("⚠️ Warning: Database file not found at " + db_path_ + ". Queries will fail until it's created.");
    }
    
    setupWebSocket();
    setupRoutes();
}

WebServerService::~WebServerService() {
    UTF8Utils::println("WebServerService destructor called.");
    stopVideoStream();

    std::lock_guard<std::mutex> lock(ws_connections_mutex);
    for (auto* conn : ws_connections) {
        delete conn;
    }
    ws_connections.clear();
}

void WebServerService::setCalibrationService(Services::CalibrationService* calibration_service) {
    calibration_service_ = calibration_service;
    UTF8Utils::println("🎯 标定服务已设置到 WebServerService");
}

void WebServerService::setupWebSocket() {
    CROW_WEBSOCKET_ROUTE(app, "/ws")
        .onopen([this](crow::websocket::connection& conn) {
            std::lock_guard<std::mutex> lock(ws_connections_mutex);
            std::string id = "user" + std::to_string(++connection_counter);
            UTF8Utils::println("🤝 新的WebSocket连接: " + id);
            auto* ws_conn = new WSConnection(&conn, id);
            ws_connections.push_back(ws_conn);
            CROW_LOG_INFO << "New WebSocket connection: " << ws_conn->id;

            // 发送欢迎消息
            crow::json::wvalue welcome_msg;
            welcome_msg["type"] = "welcome";
            welcome_msg["connection_id"] = ws_conn->id;
            conn.send_text(welcome_msg.dump());
        })
        .onclose([this](crow::websocket::connection& conn, const std::string& reason, uint16_t code) {
            std::lock_guard<std::mutex> lock(ws_connections_mutex);
            auto it = ws_connections.begin();
            while (it != ws_connections.end()) {
                if ((*it)->conn == &conn) {
                    UTF8Utils::println("WebSocket closed: " + (*it)->id + " Reason: " + reason);
                    delete *it;
                    ws_connections.erase(it);
                    break;
                }
                ++it;
            }
        })
        .onmessage([this](crow::websocket::connection& conn, const std::string& data, bool is_binary) {
            if (is_binary) return;
            
            WSConnection* ws_conn = nullptr;
            {
                std::lock_guard<std::mutex> lock(ws_connections_mutex);
                for (auto* c : ws_connections) {
                    if (c->conn == &conn) {
                        ws_conn = c;
                        break;
                    }
                }
            }

            if (ws_conn) {
                handleWebSocketMessage(ws_conn, data);
            }
        });

    CROW_LOG_INFO << "WebSocket 处理器已在 /ws 初始化";
}

void WebServerService::setupRoutes() {
    // Helper to read file content
    auto getFileContent = [](const std::string& path) -> std::string {
        std::ifstream file(path, std::ios::binary);
        if (file) {
            std::ostringstream ss;
            ss << file.rdbuf();
            return ss.str();
        }
        return "";
    };

    CROW_ROUTE(app, "/").methods("GET"_method)
        ([this, &getFileContent](const crow::request& req) {
        std::string filepath = frontend_path + "/index.html";
        return crow::response(200, "text/html", fileExists(filepath) ? getFileContent(filepath) : "Error: index.html not found.");
    });

    CROW_ROUTE(app, "/<path>")
        ([this, &getFileContent](const crow::request& req, const std::string& path) {
        std::string filepath = frontend_path + "/" + path;
        if (fileExists(filepath)) {
            // Find extension
            std::string ext;
            size_t pos = path.find_last_of('.');
            if (pos != std::string::npos) {
                ext = path.substr(pos + 1);
            }
            
            std::string mime_type = "text/plain";
            if (ext == "css") mime_type = "text/css";
            else if (ext == "js") mime_type = "application/javascript";
            else if (ext == "png") mime_type = "image/png";
            else if (ext == "jpg" || ext == "jpeg") mime_type = "image/jpeg";
            else if (ext == "gif") mime_type = "image/gif";
            else if (ext == "svg") mime_type = "image/svg+xml";
            else if (ext == "ico") mime_type = "image/x-icon";
            else if (ext == "woff2") mime_type = "font/woff2";
            
            return crow::response(200, mime_type, getFileContent(filepath));
        }
        return crow::response(404);
    });

    CROW_ROUTE(app, "/api/start_all_recording").methods("POST"_method)
        ([this](const crow::request& req) {
        UTF8Utils::println("API: Received request to start all recordings.");
        if (recording_service_) {
            recording_service_->startAll();
        }
        crow::json::wvalue res;
        res["status"] = "ok";
        res["message"] = "All recordings started.";
        return crow::response(200, res);
    });

    CROW_ROUTE(app, "/api/stop_all_recording").methods("POST"_method)
        ([this](const crow::request& req) {
        UTF8Utils::println("API: Received request to stop all recordings.");
        if (recording_service_) {
            recording_service_->stopAll();
        }
        crow::json::wvalue res;
        res["status"] = "ok";
        res["message"] = "All recordings stopped.";
        return crow::response(200, res);
    });

    // === 新增：YOLO检测框录制控制API ===

    CROW_ROUTE(app, "/api/recording/detection_mode").methods("POST"_method)
        ([this](const crow::request& req) {
        UTF8Utils::println("API: Received request to set detection recording mode.");

        try {
            auto json_data = crow::json::load(req.body);
            if (!json_data) {
                crow::json::wvalue error_res;
                error_res["status"] = "error";
                error_res["message"] = "Invalid JSON format";
                return crow::response(400, error_res);
            }

            bool enable = json_data.count("enable") ? json_data["enable"].b() : false;
            std::string mode_str = json_data.count("mode") ? std::string(json_data["mode"].s()) : "RAW_MODE";

            if (recording_service_) {
                // 设置录制模式
                RecordingMode mode = (mode_str == "DETECTION_MODE") ?
                    RecordingMode::DETECTION_MODE : RecordingMode::RAW_MODE;
                recording_service_->setRecordingMode(mode);

                // 启用/禁用检测框录制
                recording_service_->enableDetectionRecording(enable);

                crow::json::wvalue res;
                res["status"] = "ok";
                res["message"] = "Detection recording mode updated";
                res["mode"] = mode_str;
                res["enabled"] = enable;
                return crow::response(200, res);
            } else {
                crow::json::wvalue error_res;
                error_res["status"] = "error";
                error_res["message"] = "Recording service not available";
                return crow::response(500, error_res);
            }
        } catch (const std::exception& e) {
            crow::json::wvalue error_res;
            error_res["status"] = "error";
            error_res["message"] = "Error processing request: " + std::string(e.what());
            return crow::response(500, error_res);
        }
    });

    CROW_ROUTE(app, "/api/recording/detection_status").methods("GET"_method)
        ([this](const crow::request& req) {
        UTF8Utils::println("API: Received request to get detection recording status.");

        if (recording_service_) {
            crow::json::wvalue res;
            res["status"] = "ok";
            res["enabled"] = recording_service_->isDetectionRecordingEnabled();
            res["mode"] = (recording_service_->getRecordingMode() == RecordingMode::DETECTION_MODE) ?
                "DETECTION_MODE" : "RAW_MODE";
            return crow::response(200, res);
        } else {
            crow::json::wvalue error_res;
            error_res["status"] = "error";
            error_res["message"] = "Recording service not available";
            return crow::response(500, error_res);
        }
    });

    // API: Database query
    CROW_ROUTE(app, "/api/db/query").methods("POST"_method)
        ([this](const crow::request& req, crow::response& res) {
        handle_db_query(req, res);
    });
}

void WebServerService::handle_db_query(const crow::request& req, crow::response& res) {
    nlohmann::json req_body;
    try {
        req_body = nlohmann::json::parse(req.body);
    }
    catch (const nlohmann::json::parse_error& e) {
        res.code = 400;
        res.set_header("Content-Type", "application/json; charset=utf-8");
        res.body = nlohmann::json{ {"error", "Invalid JSON format: " + std::string(e.what())} }.dump();
        res.end();
        return;
    }

    if (!req_body.contains("query") || !req_body["query"].is_string()) {
        res.code = 400;
        res.set_header("Content-Type", "application/json; charset=utf-8");
        res.body = nlohmann::json{ {"error", "Missing or invalid 'query' field in request body."} }.dump();
        res.end();
        return;
    }

    std::string query_str = req_body["query"];

    // Trim leading/trailing whitespace
    query_str.erase(0, query_str.find_first_not_of(" \t\n\r\f\v"));
    query_str.erase(query_str.find_last_not_of(" \t\n\r\f\v") + 1);

    std::string query_lower = query_str;
    std::transform(query_lower.begin(), query_lower.end(), query_lower.begin(),
        [](unsigned char c) { return std::tolower(c); });

    if (query_lower.rfind("select", 0) != 0) {
        res.code = 403;
        res.set_header("Content-Type", "application/json; charset=utf-8");
        res.body = nlohmann::json{ {"error", "Forbidden: Only SELECT queries are allowed."} }.dump();
        res.end();
        return;
    }

    sqlite3* db;
    if (sqlite3_open_v2(db_path_.c_str(), &db, SQLITE_OPEN_READONLY, nullptr) != SQLITE_OK) {
        res.code = 500;
        res.set_header("Content-Type", "application/json; charset=utf-8");
        res.body = nlohmann::json{ {"error", "Failed to open database: " + std::string(sqlite3_errmsg(db))} }.dump();
        sqlite3_close(db);
        res.end();
        return;
    }

    sqlite3_stmt* stmt;
    if (sqlite3_prepare_v2(db, query_str.c_str(), -1, &stmt, nullptr) != SQLITE_OK) {
        res.code = 400;
        res.set_header("Content-Type", "application/json; charset=utf-8");
        res.body = nlohmann::json{ {"error", "SQL error: " + std::string(sqlite3_errmsg(db))} }.dump();
        sqlite3_close(db);
        res.end();
        return;
    }

    nlohmann::json result_json;
    nlohmann::json headers = nlohmann::json::array();
    int col_count = sqlite3_column_count(stmt);
    for (int i = 0; i < col_count; ++i) {
        headers.push_back(sqlite3_column_name(stmt, i));
    }

    nlohmann::json rows = nlohmann::json::array();
    while (sqlite3_step(stmt) == SQLITE_ROW) {
        nlohmann::json row = nlohmann::json::array();
        for (int i = 0; i < col_count; ++i) {
            const char* text = reinterpret_cast<const char*>(sqlite3_column_text(stmt, i));
            if (text) {
                row.push_back(text);
            }
            else {
                row.push_back(nullptr); // Handle NULL values
            }
        }
        rows.push_back(row);
    }

    result_json["headers"] = headers;
    result_json["rows"] = rows;

    sqlite3_finalize(stmt);
    sqlite3_close(db);

    res.code = 200;
    res.set_header("Content-Type", "application/json; charset=utf-8");
    res.body = result_json.dump();
    res.end();
}

void WebServerService::start(int port) {
    UTF8Utils::println("-------------------------------------------------");
    UTF8Utils::println("  Starting web server on port " + std::to_string(port));
    UTF8Utils::println("  Access it at: http://localhost:" + std::to_string(port));
    UTF8Utils::println("-------------------------------------------------");
    
    // app.run() is blocking, and since we run this service in its own
    // thread from app_lifecycle, this is the correct way to run it.
    // This replaces the previous implementation that used a detached thread
    // and a `while` loop on `std::cin`.
    app.port(port).multithreaded().run();
    
    UTF8Utils::println("Web server has been stopped.");
}

void WebServerService::setHighlightService(HighlightService* highlight_service) {
    highlight_service_ = highlight_service;
    UTF8Utils::println("🌟 HighlightService reference set in WebServerService");
}

void WebServerService::broadcastHighlightStatus() {
    if (!highlight_service_) {
        return;
    }

    try {
        crow::json::wvalue status;
        status["type"] = "highlight_status_update";

        // 获取录制状态
        auto recording_state = highlight_service_->getRecordingState();
        switch (recording_state) {
            case HighlightService::RecordingState::IDLE:
                status["recording_state"] = "IDLE";
                break;
            case HighlightService::RecordingState::RECORDING:
                status["recording_state"] = "RECORDING";
                break;
            case HighlightService::RecordingState::PROCESSING:
                status["recording_state"] = "PROCESSING";
                break;
            case HighlightService::RecordingState::COMPLETED:
                status["recording_state"] = "COMPLETED";
                break;
            case HighlightService::RecordingState::FAILED:
                status["recording_state"] = "FAILED";
                break;
        }

        // 获取其他状态信息
        status["recording_duration"] = highlight_service_->getCurrentRecordingDuration();
        status["moments_count"] = static_cast<int>(highlight_service_->getHighlightMomentsCount());
        status["clips_count"] = static_cast<int>(highlight_service_->getGeneratedClipsCount());

        // 如果有错误，包含错误信息
        if (highlight_service_->hasError()) {
            status["error"] = highlight_service_->getErrorMessage();
        }

        broadcastToWebSocketClients(status.dump());

    } catch (const std::exception& e) {
        UTF8Utils::println("❌ 广播精彩录制状态时发生错误: " + std::string(e.what()));
    }
}

void WebServerService::drawBallSpeedOverlay(cv::Mat& frame, int camera_id) {
    try {
        // 获取当前球速
        double current_speed = shared_data_->getBallSpeed();

        // 获取当前时间戳
        auto now = std::chrono::steady_clock::now();
        auto time_since_epoch = now.time_since_epoch();
        auto milliseconds = std::chrono::duration_cast<std::chrono::milliseconds>(time_since_epoch).count();

        // 格式化时间显示（HH:MM:SS.mmm）
        auto time_t = std::chrono::system_clock::to_time_t(std::chrono::system_clock::now());
        auto ms = milliseconds % 1000;
        std::tm* tm_info = std::localtime(&time_t);

        char time_buffer[32];
        std::snprintf(time_buffer, sizeof(time_buffer), "%02d:%02d:%02d.%03d",
                     tm_info->tm_hour, tm_info->tm_min, tm_info->tm_sec, static_cast<int>(ms));

        // 设置文本参数
        const int font_face = cv::FONT_HERSHEY_SIMPLEX;
        const double font_scale = 0.7;
        const int thickness = 2;
        const cv::Scalar text_color(0, 255, 255);  // 黄色文字
        const cv::Scalar bg_color(0, 0, 0);        // 黑色背景
        const cv::Scalar border_color(0, 255, 255); // 黄色边框

        // 准备显示文本（使用英文和符号避免中文乱码问题）
        std::string speed_text = ">> " + std::to_string(current_speed).substr(0, 5) + " m/s";
        std::string camera_text = "CAM " + std::to_string(camera_id);
        std::string time_text = std::string(time_buffer);

        // 计算文本尺寸
        int baseline = 0;
        cv::Size speed_size = cv::getTextSize(speed_text, font_face, font_scale, thickness, &baseline);
        cv::Size camera_size = cv::getTextSize(camera_text, font_face, font_scale * 0.8, thickness, &baseline);
        cv::Size time_size = cv::getTextSize(time_text, font_face, font_scale * 0.6, thickness, &baseline);

        // 计算背景框尺寸
        int max_width = std::max({speed_size.width, camera_size.width, time_size.width});
        int total_height = speed_size.height + camera_size.height + time_size.height + 30; // 30为间距

        // 绘制半透明背景
        cv::Point bg_top_left(10, 10);
        cv::Point bg_bottom_right(bg_top_left.x + max_width + 20, bg_top_left.y + total_height + 10);

        // 创建半透明背景
        cv::Mat overlay;
        frame.copyTo(overlay);
        cv::rectangle(overlay, bg_top_left, bg_bottom_right, bg_color, -1);
        cv::rectangle(overlay, bg_top_left, bg_bottom_right, border_color, 2);

        // 混合半透明效果
        const double alpha = 0.7;
        cv::addWeighted(overlay, alpha, frame, 1.0 - alpha, 0, frame);

        // 绘制文本
        int y_offset = bg_top_left.y + 25;

        // 球速信息（主要信息，较大字体）
        cv::putText(frame, speed_text,
                   cv::Point(bg_top_left.x + 10, y_offset),
                   font_face, font_scale, text_color, thickness);
        y_offset += speed_size.height + 8;

        // 摄像头信息
        cv::putText(frame, camera_text,
                   cv::Point(bg_top_left.x + 10, y_offset),
                   font_face, font_scale * 0.8, cv::Scalar(200, 200, 200), thickness - 1);
        y_offset += camera_size.height + 8;

        // 时间戳信息
        cv::putText(frame, time_text,
                   cv::Point(bg_top_left.x + 10, y_offset),
                   font_face, font_scale * 0.6, cv::Scalar(150, 150, 150), thickness - 1);

        // 如果球速较高，添加高速警告标识
        if (current_speed > 15.0) {
            cv::circle(frame, cv::Point(bg_bottom_right.x + 15, bg_top_left.y + 20), 8,
                      cv::Scalar(0, 0, 255), -1); // 红色圆点
            cv::putText(frame, "!", cv::Point(bg_bottom_right.x + 12, bg_top_left.y + 25),
                       font_face, 0.6, cv::Scalar(255, 255, 255), 2);
        }

    } catch (const std::exception& e) {
        // 静默处理错误，避免影响视频流
        // 可以选择性地记录错误日志
        static auto last_error_time = std::chrono::steady_clock::now();
        auto now = std::chrono::steady_clock::now();
        if (std::chrono::duration_cast<std::chrono::seconds>(now - last_error_time).count() > 5) {
            UTF8Utils::println("球速叠加显示错误: " + std::string(e.what()));
            last_error_time = now;
        }
    }
}