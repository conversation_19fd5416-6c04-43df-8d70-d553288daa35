#pragma once

#include <string>
#include <memory>
#include <thread>
#include <atomic>
#include <map>
#include <mutex>

#include "../Utils/threadsafe_queue.hpp"
#include "../Utils/SharedData.hpp"
#include <opencv2/opencv.hpp>
#include <chrono>

// Define target frame rate for recordings - matching camera native FPS
const double TARGET_FPS = 210.0; // 提升到摄像头原生帧率

// 录制模式枚举
enum class RecordingMode {
    RAW_MODE,        // 录制原始帧（默认模式）
    DETECTION_MODE   // 录制带YOLO检测框的帧
};

// 带时间戳的帧结构
struct TimestampedFrame {
    cv::Mat frame;
    std::chrono::steady_clock::time_point timestamp;
};

class RecordingService {
public:
    RecordingService(const std::string& recordings_path, std::shared_ptr<SharedData> shared_data = nullptr);
    ~RecordingService();

    // The main loop calls this to provide frames for recording.
    void pushFrame(int camera_id, const cv::Mat& frame);

    // Called by WebServerService to start/stop recording for a specific camera.
    void startRecording(int camera_id);
    void stopRecording(int camera_id);
    
    // New methods to control all recordings at once.
    void startAll();
    void stopAll();

    // Methods to query the recording status.
    bool isRecording(int camera_id);
    std::map<int, bool> getRecordingStatus();

    // 同步控制方法
    void enableStereoSync(bool enable = true);
    bool isStereoSyncEnabled() const;

    // === 新增：YOLO检测框录制功能 ===

    // 设置录制模式
    void setRecordingMode(RecordingMode mode);
    RecordingMode getRecordingMode() const;

    // 启用/禁用检测框录制
    void enableDetectionRecording(bool enable = true);
    bool isDetectionRecordingEnabled() const;

    // === 新增：消息驱动录制控制 ===

    /**
     * @brief 启动消息监听线程
     */
    void startMessageListener();

    /**
     * @brief 停止消息监听线程
     */
    void stopMessageListener();

    /**
     * @brief 处理精彩片段录制请求
     * @param camera_id 摄像头ID
     * @param output_path 输出路径
     * @param request_id 请求ID
     * @return 是否成功开始录制
     */
    bool startHighlightRecording(int camera_id, const std::string& output_path, const std::string& request_id);

    /**
     * @brief 停止精彩片段录制
     * @param camera_id 摄像头ID
     * @param request_id 请求ID
     * @return 输出文件路径
     */
    std::string stopHighlightRecording(int camera_id, const std::string& request_id);

private:
    void worker(int camera_id);
    static std::string create_timestamped_filename(int camera_id, const std::string& base_path);

    // === 新增：消息处理方法 ===
    void messageListenerThread();
    void processRecordingMessage(const RecordingMessage& message);
    void sendRecordingResponse(const std::string& request_id, RecordingStatus status, const std::string& message = "", const std::string& output_file = "");

    // === 新增：YOLO检测框叠加方法 ===
    cv::Mat addDetectionOverlay(const cv::Mat& frame, int camera_id);
    void drawDetectionBoxes(cv::Mat& frame, const DetectionResult& detection_result);

    std::string recordings_path_;
    std::map<int, std::unique_ptr<ThreadSafeQueue<TimestampedFrame>>> image_queues_;
    std::map<int, std::thread> worker_threads_;
    // Use a simple boolean flag, protected by the mutex.
    std::map<int, bool> recording_active_;
    std::map<int, cv::Size> frame_sizes_; // Store frame sizes
    std::mutex service_mutex_; // Protects access to all maps.

    // 双目同步相关
    std::atomic<bool> stereo_sync_enabled_{true}; // 默认启用双目同步
    std::map<int, std::chrono::steady_clock::time_point> last_sync_timestamps_; // 每个摄像头的最后同步时间戳
    static constexpr std::chrono::milliseconds MAX_SYNC_TOLERANCE{20}; // 最大同步容差：20ms

    // === 新增：SharedData和消息监听 ===
    std::shared_ptr<SharedData> shared_data_;
    std::unique_ptr<std::thread> message_listener_thread_;
    std::atomic<bool> message_listener_running_{false};

    // 精彩片段录制状态
    std::map<int, std::string> highlight_recording_paths_; // 摄像头ID -> 输出路径
    std::map<int, std::string> highlight_request_ids_;     // 摄像头ID -> 请求ID

    // === 新增：YOLO检测框录制相关成员变量 ===
    RecordingMode recording_mode_{RecordingMode::RAW_MODE};  // 默认原始模式
    std::atomic<bool> detection_recording_enabled_{false};   // 检测框录制开关
    mutable std::mutex recording_mode_mutex_;                // 录制模式保护锁
};