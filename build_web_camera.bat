@echo off
echo ===============================================
echo 构建相机Web服务器
echo ===============================================

REM 设置环境
set CMAKE_BUILD_PARALLEL_LEVEL=8

REM 创建并进入构建目录
if not exist build (
    mkdir build
)
cd build

REM 运行CMake配置
echo 正在配置CMake...
cmake .. -G "Visual Studio 17 2022" -A x64

if %ERRORLEVEL% neq 0 (
    echo CMake配置失败！
    pause
    exit /b 1
)

REM 构建项目
echo 正在构建项目...
cmake --build . --config Release --target CameraWebServer

if %ERRORLEVEL% neq 0 (
    echo 构建失败！
    pause
    exit /b 1
)

echo ===============================================
echo 构建成功！
echo 可执行文件位置: build\Release\CameraWebServer.exe
echo ===============================================

REM 询问是否运行
set /p choice="是否立即运行Web服务器？(y/n): "
if /i "%choice%"=="y" (
    echo 启动Web服务器...
    cd Release
    CameraWebServer.exe
)

pause 