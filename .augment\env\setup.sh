#!/bin/bash

echo "=== 为Windows VS Code开发环境创建完整配置 ==="

# 创建VS Code配置目录
mkdir -p .vscode

# 创建tasks.json - 支持Windows和Linux构建
cat > .vscode/tasks.json << 'EOF'
{
    "version": "2.0.0",
    "tasks": [
        {
            "label": "Windows CMake Configure",
            "type": "shell",
            "command": "cmake",
            "args": [
                "-B", "build",
                "-S", ".",
                "-G", "Visual Studio 17 2022",
                "-A", "x64"
            ],
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared"
            },
            "problemMatcher": [],
            "options": {
                "shell": {
                    "executable": "cmd.exe",
                    "args": ["/c"]
                }
            }
        },
        {
            "label": "Windows CMake Build Debug",
            "type": "shell",
            "command": "cmake",
            "args": [
                "--build", "build",
                "--config", "Debug",
                "--parallel"
            ],
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared"
            },
            "problemMatcher": ["$msCompile"],
            "dependsOn": "Windows CMake Configure",
            "options": {
                "shell": {
                    "executable": "cmd.exe",
                    "args": ["/c"]
                }
            }
        },
        {
            "label": "Windows CMake Build Release",
            "type": "shell",
            "command": "cmake",
            "args": [
                "--build", "build",
                "--config", "Release",
                "--parallel"
            ],
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared"
            },
            "problemMatcher": ["$msCompile"],
            "dependsOn": "Windows CMake Configure",
            "options": {
                "shell": {
                    "executable": "cmd.exe",
                    "args": ["/c"]
                }
            }
        },
        {
            "label": "Clean Build Directory",
            "type": "shell",
            "command": "rmdir",
            "args": ["/s", "/q", "build"],
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared"
            },
            "options": {
                "shell": {
                    "executable": "cmd.exe",
                    "args": ["/c"]
                }
            }
        },
        {
            "label": "Run Camera_Editor Debug",
            "type": "shell",
            "command": "${workspaceFolder}/build/Debug/Camera_Editor.exe",
            "group": "test",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": true,
                "panel": "shared"
            },
            "dependsOn": "Windows CMake Build Debug"
        }
    ]
}
EOF

# 创建launch.json - 调试配置
cat > .vscode/launch.json << 'EOF'
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Debug Camera_Editor",
            "type": "cppvsdbg",
            "request": "launch",
            "program": "${workspaceFolder}/build/Debug/Camera_Editor.exe",
            "args": [],
            "stopAtEntry": false,
            "cwd": "${workspaceFolder}",
            "environment": [],
            "console": "integratedTerminal",
            "preLaunchTask": "Windows CMake Build Debug"
        },
        {
            "name": "Debug Camera_Editor (External Console)",
            "type": "cppvsdbg",
            "request": "launch",
            "program": "${workspaceFolder}/build/Debug/Camera_Editor.exe",
            "args": [],
            "stopAtEntry": false,
            "cwd": "${workspaceFolder}",
            "environment": [],
            "console": "externalTerminal",
            "preLaunchTask": "Windows CMake Build Debug"
        },
        {
            "name": "Release Camera_Editor",
            "type": "cppvsdbg",
            "request": "launch",
            "program": "${workspaceFolder}/build/Release/Camera_Editor.exe",
            "args": [],
            "stopAtEntry": false,
            "cwd": "${workspaceFolder}",
            "environment": [],
            "console": "integratedTerminal",
            "preLaunchTask": "Windows CMake Build Release"
        }
    ]
}
EOF

# 创建c_cpp_properties.json - IntelliSense配置
cat > .vscode/c_cpp_properties.json << 'EOF'
{
    "version": 4,
    "configurations": [
        {
            "name": "Win32",
            "includePath": [
                "${workspaceFolder}/**",
                "${workspaceFolder}/Camera",
                "${workspaceFolder}/Main", 
                "${workspaceFolder}/Deploy",
                "${workspaceFolder}/Utils",
                "${workspaceFolder}/Services",
                "${workspaceFolder}/third_party/sqlite",
                "${workspaceFolder}/third_party",
                "C:/Program Files (x86)/MVS/Development/Includes",
                "C:/Dev/opencv/build/include",
                "C:/Dev/TensorRT-10.5.0.18/include",
                "C:/Dev/Crow-master/include",
                "C:/Dev/asio-1.30.2/include",
                "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/*/include",
                "C:/Program Files (x86)/Windows Kits/10/Include/*/ucrt",
                "C:/Program Files (x86)/Windows Kits/10/Include/*/um",
                "C:/Program Files (x86)/Windows Kits/10/Include/*/shared"
            ],
            "defines": [
                "_DEBUG",
                "UNICODE",
                "_UNICODE",
                "WIN32",
                "_WINDOWS",
                "_MBCS",
                "API_EXPORTS",
                "_CRT_SECURE_NO_WARNINGS"
            ],
            "windowsSdkVersion": "10.0.22000.0",
            "compilerPath": "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.37.32822/bin/Hostx64/x64/cl.exe",
            "cStandard": "c17",
            "cppStandard": "c++17",
            "intelliSenseMode": "windows-msvc-x64",
            "configurationProvider": "ms-vscode.cmake-tools",
            "compileCommands": "${workspaceFolder}/build/compile_commands.json"
        }
    ]
}
EOF

# 创建settings.json - VS Code设置
cat > .vscode/settings.json << 'EOF'
{
    "cmake.buildDirectory": "${workspaceFolder}/build",
    "cmake.configureArgs": [
        "-G", "Visual Studio 17 2022",
        "-A", "x64"
    ],
    "cmake.generator": "Visual Studio 17 2022",
    "cmake.preferredGenerators": [
        "Visual Studio 17 2022",
        "Visual Studio 16 2019"
    ],
    "files.associations": {
        "*.hpp": "cpp",
        "*.cpp": "cpp",
        "*.h": "c",
        "*.cu": "cuda-cpp"
    },
    "C_Cpp.default.configurationProvider": "ms-vscode.cmake-tools",
    "C_Cpp.intelliSenseEngine": "default",
    "C_Cpp.errorSquiggles": "enabled",
    "editor.formatOnSave": true,
    "C_Cpp.clang_format_style": "{ BasedOnStyle: Google, IndentWidth: 4, ColumnLimit: 120 }",
    "cmake.buildBeforeRun": true,
    "cmake.clearOutputBeforeBuild": true,
    "files.encoding": "utf8",
    "terminal.integrated.defaultProfile.windows": "Command Prompt"
}
EOF

# 创建工作区文件
cat > Camera_Editor.code-workspace << 'EOF'
{
    "folders": [
        {
            "path": "."
        }
    ],
    "settings": {
        "cmake.buildDirectory": "${workspaceFolder}/build",
        "cmake.generator": "Visual Studio 17 2022",
        "cmake.configureArgs": [
            "-G", "Visual Studio 17 2022",
            "-A", "x64"
        ],
        "files.associations": {
            "*.hpp": "cpp",
            "*.cpp": "cpp",
            "*.h": "c",
            "*.cu": "cuda-cpp"
        },
        "C_Cpp.default.configurationProvider": "ms-vscode.cmake-tools",
        "C_Cpp.intelliSenseEngine": "default",
        "editor.formatOnSave": true,
        "files.encoding": "utf8"
    },
    "extensions": {
        "recommendations": [
            "ms-vscode.cpptools",
            "ms-vscode.cmake-tools",
            "twxs.cmake",
            "eamodio.gitlens",
            "xaver.clang-format",
            "ms-vscode.cpptools-extension-pack"
        ]
    }
}
EOF

# 创建README文件说明如何使用
cat > VS_CODE_SETUP.md << 'EOF'
# VS Code 开发环境配置说明

## 📋 前提条件

确保您的Windows系统已安装：
- Visual Studio 2022 (Community/Professional/Enterprise)
- CMake 3.18+
- Git

## 🚀 快速开始

### 1. 安装VS Code扩展
打开VS Code，安装以下推荐扩展：
- **C/C++** (Microsoft) - 必需
- **CMake Tools** (Microsoft) - 必需  
- **CMake** (twxs) - 推荐
- **GitLens** (Eric Amodio) - 推荐
- **Clang-Format** (xaver) - 推荐

### 2. 打开项目
- 方式1：直接打开 `Camera_Editor.code-workspace` 文件
- 方式2：在VS Code中打开项目文件夹

### 3. 配置和构建
1. 按 `Ctrl+Shift+P` 打开命令面板
2. 输入 `Tasks: Run Task`
3. 选择 `Windows CMake Configure` 进行配置
4. 选择 `Windows CMake Build Debug` 进行构建

### 4. 调试运行
- 按 `F5` 开始调试
- 或按 `Ctrl+F5` 运行而不调试

## 🔧 可用任务

- **Windows CMake Configure**: 配置CMake项目
- **Windows CMake Build Debug**: 构建Debug版本
- **Windows CMake Build Release**: 构建Release版本
- **Clean Build Directory**: 清理构建目录
- **Run Camera_Editor Debug**: 运行Debug版本

## 📁 项目结构

```
Camera_Editor/
├── .vscode/                    # VS Code配置文件
│   ├── tasks.json             # 构建任务
│   ├── launch.json            # 调试配置
│   ├── c_cpp_properties.json  # IntelliSense配置
│   └── settings.json          # VS Code设置
├── Camera_Editor.code-workspace # 工作区文件
├── CMakeLists.txt             # CMake配置
├── Main/                      # 主程序
├── Services/                  # 服务模块
├── Camera/                    # 相机控制
├── Deploy/                    # AI部署
├── Utils/                     # 工具类
├── Web/                       # Web界面
└── third_party/               # 第三方库
```

## 🎯 开发工作流

1. **编辑代码**: 在VS Code中编辑源文件
2. **构建项目**: `Ctrl+Shift+P` → `Tasks: Run Task` → `Windows CMake Build Debug`
3. **调试运行**: 按 `F5` 开始调试
4. **查看输出**: 在终端面板查看构建和运行输出

## 🔍 IntelliSense配置

已配置的包含路径：
- 项目所有目录
- 海康威视相机SDK
- OpenCV
- TensorRT
- Crow框架
- Asio库
- Visual Studio标准库

## 💡 提示

- 使用 `Ctrl+Shift+B` 快速构建
- 使用 `Ctrl+`` 打开集成终端
- 代码格式化已启用（保存时自动格式化）
- 支持CUDA文件语法高亮

## 🐛 故障排除

### 构建失败
1. 检查Visual Studio 2022是否正确安装
2. 确认CMake在PATH中
3. 验证所有依赖库路径是否正确

### IntelliSense不工作
1. 重新加载VS Code窗口
2. 检查c_cpp_properties.json中的路径
3. 确保CMake Tools扩展已启用

### 调试无法启动
1. 确保项目已成功构建
2. 检查可执行文件是否存在于build/Debug/目录
3. 验证调试器配置
EOF

echo ""
echo "=== ✅ VS Code开发环境配置完成！ ==="
echo ""
echo "📁 已创建的配置文件："
echo "   ✓ .vscode/tasks.json - 构建任务配置"
echo "   ✓ .vscode/launch.json - 调试配置"  
echo "   ✓ .vscode/c_cpp_properties.json - IntelliSense配置"
echo "   ✓ .vscode/settings.json - VS Code设置"
echo "   ✓ Camera_Editor.code-workspace - 工作区文件"
echo "   ✓ VS_CODE_SETUP.md - 详细使用说明"
echo ""
echo "🚀 下一步操作："
echo "   1. 在Windows上打开VS Code"
echo "   2. 安装推荐的扩展"
echo "   3. 打开 Camera_Editor.code-workspace 文件"
echo "   4. 按照 VS_CODE_SETUP.md 中的说明开始开发"
echo ""
echo "🎯 快速开始："
echo "   - Ctrl+Shift+P → Tasks: Run Task → Windows CMake Configure"
echo "   - Ctrl+Shift+P → Tasks: Run Task → Windows CMake Build Debug"  
echo "   - F5 开始调试"
echo ""