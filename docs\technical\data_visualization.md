# 数据可视化系统技术实现说明

## 📋 概述

本文档详细说明Camera_Editor项目数据可视化系统的技术实现细节，供开发者参考和后续维护使用。

## 🏗️ 系统架构

### 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端界面      │    │   Web服务器     │    │   数据库        │
│  (Chart.js)     │◄──►│  (Crow C++)     │◄──►│  (SQLite)       │
│  HTML/CSS/JS    │    │  /api/db/query  │    │  trajectory表   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 技术栈
- **前端可视化**: Chart.js v4.4.1
- **现有3D渲染**: Three.js
- **后端API**: Crow C++ Web框架
- **数据库**: SQLite
- **样式框架**: 自定义CSS (HUD风格)

## 📊 核心组件实现

### 1. 图表系统 (Chart.js集成)

#### 1.1 速度分布图 (柱状图)
```javascript
// 位置: Web/frontend/simple-video.js:initSpeedDistributionChart()
this.charts.speedDistribution = new Chart(ctx, {
    type: 'bar',
    data: {
        labels: ['0-5', '5-10', '10-15', '15-20', '20-25', '25+'],
        datasets: [{
            label: '球速分布 (m/s)',
            data: [0, 0, 0, 0, 0, 0],
            backgroundColor: 'rgba(0, 212, 255, 0.6)',
            borderColor: 'rgba(0, 212, 255, 1)',
            borderWidth: 1
        }]
    },
    // ... 配置选项
});
```

**数据处理逻辑**:
- 查询最近1000条速度记录
- 按6个速度区间统计分布
- 实时更新图表数据

#### 1.2 速度时间序列图 (折线图)
```javascript
// 位置: Web/frontend/simple-video.js:initSpeedTimeSeriesChart()
this.charts.speedTimeSeries = new Chart(ctx, {
    type: 'line',
    data: {
        datasets: [{
            label: '球速变化 (m/s)',
            data: [],
            borderColor: 'rgba(255, 99, 132, 1)',
            backgroundColor: 'rgba(255, 99, 132, 0.1)',
            tension: 0.4
        }]
    },
    options: {
        scales: {
            x: {
                type: 'time',
                time: {
                    unit: 'second'
                }
            }
        }
    }
});
```

**特性**:
- 时间轴自动缩放
- 平滑曲线显示
- 支持实时数据更新

### 2. 数据查询系统

#### 2.1 后端API接口
```cpp
// 位置: Services/WebServerService.cpp
crow::response handleDatabaseQuery(const crow::request& req) {
    try {
        auto json_data = nlohmann::json::parse(req.body);
        std::string query = json_data["query"];
        
        // 执行SQL查询
        auto results = database->executeQuery(query);
        
        // 返回JSON格式结果
        return crow::response(200, results.dump());
    } catch (const std::exception& e) {
        return crow::response(500, e.what());
    }
}
```

#### 2.2 前端数据获取
```javascript
// 位置: Web/frontend/simple-video.js:fetchDataFromDatabase()
async fetchDataFromDatabase(query) {
    try {
        const response = await fetch('/api/db/query', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ query: query })
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return await response.json();
    } catch (error) {
        console.error('Database query failed:', error);
        throw error;
    }
}
```

### 3. 实时更新机制

#### 3.1 定时刷新
```javascript
// 位置: Web/frontend/simple-video.js:startDataVisualizationUpdates()
startDataVisualizationUpdates() {
    // 每5秒更新一次图表数据
    this.dataUpdateInterval = setInterval(() => {
        this.updateAllCharts();
    }, 5000);
}

async updateAllCharts() {
    try {
        await Promise.all([
            this.updateSpeedDistributionChart(),
            this.updateSpeedTimeSeriesChart(),
            this.updateStatisticsSummary()
        ]);
    } catch (error) {
        console.error('Failed to update charts:', error);
    }
}
```

#### 3.2 数据采样优化
```javascript
// 智能数据采样，避免图表过载
function sampleData(data, maxPoints = 50) {
    if (data.length <= maxPoints) {
        return data;
    }
    
    const step = Math.floor(data.length / maxPoints);
    return data.filter((_, index) => index % step === 0);
}
```

## 🎨 用户界面设计

### 1. HUD风格样式
```css
/* 位置: Web/frontend/style.css */
.data-visualization-container {
    background: linear-gradient(135deg, 
        rgba(0, 20, 40, 0.95), 
        rgba(0, 40, 80, 0.95));
    border: 1px solid rgba(0, 212, 255, 0.3);
    border-radius: 8px;
    padding: 20px;
    margin: 10px;
    backdrop-filter: blur(10px);
}

.chart-container {
    position: relative;
    height: 300px;
    margin-bottom: 20px;
}

.chart-title {
    color: rgba(0, 212, 255, 1);
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 10px;
    text-align: center;
}
```

### 2. 响应式布局
```css
/* 适配不同屏幕尺寸 */
@media (max-width: 768px) {
    .data-visualization-container {
        padding: 10px;
        margin: 5px;
    }
    
    .chart-container {
        height: 250px;
    }
}

@media (max-width: 480px) {
    .chart-container {
        height: 200px;
    }
    
    .chart-title {
        font-size: 14px;
    }
}
```

## 🔧 性能优化

### 1. 数据查询优化
```javascript
// 使用索引优化的SQL查询
const optimizedQueries = {
    speedDistribution: `
        SELECT 
            CASE 
                WHEN speed < 5 THEN '0-5'
                WHEN speed < 10 THEN '5-10'
                WHEN speed < 15 THEN '10-15'
                WHEN speed < 20 THEN '15-20'
                WHEN speed < 25 THEN '20-25'
                ELSE '25+'
            END as speed_range,
            COUNT(*) as count
        FROM trajectory 
        WHERE timestamp_ms > (
            SELECT MAX(timestamp_ms) - 60000 FROM trajectory
        )
        GROUP BY speed_range
        ORDER BY speed_range;
    `,
    
    timeSeriesData: `
        SELECT timestamp_ms, speed 
        FROM trajectory 
        WHERE timestamp_ms > ? 
        ORDER BY timestamp_ms DESC 
        LIMIT 1000;
    `
};
```

### 2. 前端渲染优化
```javascript
// 使用requestAnimationFrame优化动画
function smoothChartUpdate(chart, newData) {
    const currentData = chart.data.datasets[0].data;
    const targetData = newData;
    
    function animate() {
        let needsUpdate = false;
        
        for (let i = 0; i < currentData.length; i++) {
            const diff = targetData[i] - currentData[i];
            if (Math.abs(diff) > 0.1) {
                currentData[i] += diff * 0.1;
                needsUpdate = true;
            }
        }
        
        if (needsUpdate) {
            chart.update('none');
            requestAnimationFrame(animate);
        }
    }
    
    requestAnimationFrame(animate);
}
```

### 3. 内存管理
```javascript
// 清理图表资源
function destroyCharts() {
    Object.values(this.charts).forEach(chart => {
        if (chart) {
            chart.destroy();
        }
    });
    this.charts = {};
    
    // 清理定时器
    if (this.dataUpdateInterval) {
        clearInterval(this.dataUpdateInterval);
        this.dataUpdateInterval = null;
    }
}
```

## 🧪 测试和验证

### 1. 功能测试
```javascript
// 位置: Web/frontend/test/visualization-test.js
describe('Data Visualization', () => {
    test('Chart initialization', () => {
        const chartManager = new DataVisualizationManager();
        chartManager.initializeCharts();
        
        expect(chartManager.charts.speedDistribution).toBeDefined();
        expect(chartManager.charts.speedTimeSeries).toBeDefined();
    });
    
    test('Data fetching', async () => {
        const data = await chartManager.fetchSpeedDistributionData();
        expect(Array.isArray(data)).toBe(true);
        expect(data.length).toBeGreaterThan(0);
    });
});
```

### 2. 性能测试
```javascript
// 性能基准测试
function benchmarkChartUpdate() {
    const startTime = performance.now();
    
    // 模拟大量数据更新
    const testData = generateTestData(1000);
    updateAllCharts(testData);
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    console.log(`Chart update took ${duration} milliseconds`);
    return duration;
}
```

## 📚 扩展指南

### 1. 添加新图表类型
```javascript
// 扩展新的图表类型
class CustomChartManager extends DataVisualizationManager {
    initializeHeatmapChart() {
        const ctx = document.getElementById('heatmapChart').getContext('2d');
        
        this.charts.heatmap = new Chart(ctx, {
            type: 'scatter',
            data: {
                datasets: [{
                    label: '运动热力图',
                    data: [],
                    backgroundColor: function(context) {
                        // 根据数据密度动态设置颜色
                        return getHeatmapColor(context.parsed.density);
                    }
                }]
            },
            options: {
                scales: {
                    x: { title: { display: true, text: 'X坐标 (m)' } },
                    y: { title: { display: true, text: 'Y坐标 (m)' } }
                }
            }
        });
    }
}
```

### 2. 自定义数据处理
```javascript
// 自定义数据处理管道
class DataProcessor {
    static processTrajectoryData(rawData) {
        return rawData
            .filter(point => point.speed > 0)  // 过滤无效数据
            .map(point => ({
                ...point,
                timestamp: new Date(point.timestamp_ms),
                position: {
                    x: point.pos_x,
                    y: point.pos_y,
                    z: point.pos_z
                }
            }))
            .sort((a, b) => a.timestamp - b.timestamp);  // 时间排序
    }
    
    static calculateMovingAverage(data, windowSize = 5) {
        const result = [];
        for (let i = windowSize - 1; i < data.length; i++) {
            const window = data.slice(i - windowSize + 1, i + 1);
            const average = window.reduce((sum, val) => sum + val.speed, 0) / windowSize;
            result.push({
                ...data[i],
                smoothedSpeed: average
            });
        }
        return result;
    }
}
```

---

**相关文档**:
- [交互式速度图表技术文档](interactive_charts.md)
- [数据可视化系统测试指南](testing_guides.md)
- [AI助手项目理解文档](../AI_PROJECT_CONTEXT.md)
