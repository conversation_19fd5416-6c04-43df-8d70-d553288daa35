#pragma once

#include "ErrorManagement.hpp"
#include "PerformanceOptimizedErrorHandling.hpp"
#include <functional>
#include <vector>
#include <unordered_map>

namespace ErrorManagement {

/**
 * @brief 实时监控和告警系统
 * 
 * 专为Camera_Editor的210FPS实时处理需求设计
 */
class RealTimeMonitor {
public:
    // 告警级别
    enum class AlertLevel {
        INFO,       // 信息性告警
        WARNING,    // 警告级告警
        CRITICAL,   // 严重告警
        EMERGENCY   // 紧急告警（需要立即处理）
    };
    
    // 告警类型
    enum class AlertType {
        PERFORMANCE_DEGRADATION,    // 性能下降
        HIGH_ERROR_RATE,           // 错误率过高
        SYSTEM_OVERLOAD,           // 系统过载
        HARDWARE_FAILURE,          // 硬件故障
        MEMORY_LEAK,               // 内存泄漏
        DISK_SPACE_LOW,            // 磁盘空间不足
        NETWORK_ISSUE,             // 网络问题
        CUSTOM_ALERT               // 自定义告警
    };
    
    // 告警信息结构
    struct Alert {
        AlertType type;
        AlertLevel level;
        std::string message;
        std::chrono::system_clock::time_point timestamp;
        std::string source_component;
        std::unordered_map<std::string, std::string> metadata;
        bool acknowledged = false;
        
        Alert(AlertType t, AlertLevel l, const std::string& msg, const std::string& source = "")
            : type(t), level(l), message(msg), source_component(source),
              timestamp(std::chrono::system_clock::now()) {}
    };
    
    // 告警处理器类型
    using AlertHandler = std::function<void(const Alert&)>;
    
private:
    // 监控指标
    struct MonitoringMetrics {
        // 性能指标
        std::atomic<double> current_fps{0.0};
        std::atomic<double> average_frame_time_ms{0.0};
        std::atomic<double> cpu_usage_percent{0.0};
        std::atomic<double> memory_usage_percent{0.0};
        std::atomic<double> gpu_usage_percent{0.0};
        
        // 错误指标
        std::atomic<uint64_t> total_errors{0};
        std::atomic<uint64_t> critical_errors{0};
        std::atomic<uint64_t> errors_per_minute{0};
        
        // 系统指标
        std::atomic<uint64_t> total_frames_processed{0};
        std::atomic<uint64_t> dropped_frames{0};
        std::atomic<double> disk_usage_percent{0.0};
        std::atomic<bool> camera_connected{false};
        std::atomic<bool> ai_model_loaded{false};
    };
    
    MonitoringMetrics metrics_;
    
    // 告警管理
    std::vector<Alert> active_alerts_;
    std::vector<Alert> alert_history_;
    std::mutex alerts_mutex_;
    
    // 告警处理器
    std::unordered_map<AlertType, std::vector<AlertHandler>> alert_handlers_;
    std::mutex handlers_mutex_;
    
    // 监控线程
    std::thread monitoring_thread_;
    std::atomic<bool> monitoring_active_{false};
    std::chrono::seconds monitoring_interval_{1}; // 1秒检查一次
    
    // 告警阈值配置
    struct AlertThresholds {
        double min_fps = 200.0;                    // 最小FPS
        double max_frame_time_ms = 5.0;            // 最大帧处理时间
        double max_cpu_usage = 80.0;               // 最大CPU使用率
        double max_memory_usage = 85.0;            // 最大内存使用率
        double max_gpu_usage = 90.0;               // 最大GPU使用率
        uint64_t max_errors_per_minute = 100;     // 每分钟最大错误数
        uint64_t max_critical_errors = 5;         // 最大严重错误数
        double max_drop_rate = 0.01;               // 最大丢帧率（1%）
        double max_disk_usage = 90.0;              // 最大磁盘使用率
    } thresholds_;
    
    // 监控逻辑
    void monitoringLoop();
    void checkPerformanceMetrics();
    void checkErrorMetrics();
    void checkSystemMetrics();
    void checkHardwareStatus();
    
    // 告警触发
    void triggerAlert(AlertType type, AlertLevel level, const std::string& message, 
                     const std::string& source = "");
    void processAlert(const Alert& alert);
    
public:
    RealTimeMonitor();
    ~RealTimeMonitor();
    
    // 监控控制
    void startMonitoring();
    void stopMonitoring();
    bool isMonitoring() const { return monitoring_active_.load(); }
    
    // 指标更新接口
    void updateFPS(double fps) { metrics_.current_fps = fps; }
    void updateFrameTime(double time_ms) { metrics_.average_frame_time_ms = time_ms; }
    void updateCPUUsage(double usage) { metrics_.cpu_usage_percent = usage; }
    void updateMemoryUsage(double usage) { metrics_.memory_usage_percent = usage; }
    void updateGPUUsage(double usage) { metrics_.gpu_usage_percent = usage; }
    void updateDiskUsage(double usage) { metrics_.disk_usage_percent = usage; }
    void updateCameraStatus(bool connected) { metrics_.camera_connected = connected; }
    void updateAIModelStatus(bool loaded) { metrics_.ai_model_loaded = loaded; }
    
    void incrementFrameCount() { metrics_.total_frames_processed++; }
    void incrementDroppedFrames() { metrics_.dropped_frames++; }
    void incrementErrorCount() { metrics_.total_errors++; }
    void incrementCriticalErrorCount() { metrics_.critical_errors++; }
    
    // 告警管理
    void registerAlertHandler(AlertType type, AlertHandler handler);
    void acknowledgeAlert(size_t alert_index);
    void clearAcknowledgedAlerts();
    
    // 获取状态信息
    std::vector<Alert> getActiveAlerts() const;
    std::vector<Alert> getAlertHistory(size_t max_count = 100) const;
    MonitoringMetrics getCurrentMetrics() const { return metrics_; }
    
    // 配置接口
    void setAlertThresholds(const AlertThresholds& thresholds) { thresholds_ = thresholds; }
    AlertThresholds getAlertThresholds() const { return thresholds_; }
    void setMonitoringInterval(std::chrono::seconds interval) { monitoring_interval_ = interval; }
    
    // 健康检查
    bool isSystemHealthy() const;
    double getSystemHealthScore() const; // 0.0-1.0
    
    // 报告生成
    struct MonitoringReport {
        std::chrono::system_clock::time_point report_time;
        MonitoringMetrics metrics;
        std::vector<Alert> recent_alerts;
        double health_score;
        bool performance_acceptable;
        std::string summary;
    };
    
    MonitoringReport generateReport() const;
    std::string formatReportAsJson(const MonitoringReport& report) const;
};

/**
 * @brief 自适应告警系统
 * 
 * 根据系统负载自动调整告警阈值，避免告警风暴
 */
class AdaptiveAlertSystem {
private:
    struct AdaptiveThresholds {
        double base_value;
        double current_value;
        double adaptation_rate;
        std::chrono::steady_clock::time_point last_update;
        
        AdaptiveThresholds(double base) 
            : base_value(base), current_value(base), adaptation_rate(0.1),
              last_update(std::chrono::steady_clock::now()) {}
    };
    
    std::unordered_map<std::string, AdaptiveThresholds> adaptive_thresholds_;
    std::mutex thresholds_mutex_;
    
    // 系统负载评估
    double current_system_load_ = 0.0;
    std::atomic<bool> high_load_mode_{false};
    
public:
    /**
     * @brief 更新系统负载信息
     */
    void updateSystemLoad(double cpu_usage, double memory_usage, double fps);
    
    /**
     * @brief 获取自适应阈值
     */
    double getAdaptiveThreshold(const std::string& metric_name, double base_threshold);
    
    /**
     * @brief 检查是否应该触发告警
     */
    bool shouldTriggerAlert(const std::string& metric_name, double current_value, double base_threshold);
    
    /**
     * @brief 重置自适应阈值
     */
    void resetAdaptiveThresholds();
    
    /**
     * @brief 获取当前系统负载状态
     */
    bool isHighLoadMode() const { return high_load_mode_.load(); }
    double getCurrentSystemLoad() const { return current_system_load_; }
};

/**
 * @brief 告警聚合器
 * 
 * 防止相似告警的重复触发，减少告警噪音
 */
class AlertAggregator {
private:
    struct AlertGroup {
        AlertType type;
        std::vector<RealTimeMonitor::Alert> alerts;
        std::chrono::steady_clock::time_point first_occurrence;
        std::chrono::steady_clock::time_point last_occurrence;
        size_t count = 0;
        
        AlertGroup(AlertType t) : type(t), 
            first_occurrence(std::chrono::steady_clock::now()),
            last_occurrence(std::chrono::steady_clock::now()) {}
    };
    
    std::unordered_map<AlertType, AlertGroup> alert_groups_;
    std::mutex groups_mutex_;
    
    // 聚合配置
    std::chrono::seconds aggregation_window_{60}; // 60秒聚合窗口
    size_t max_similar_alerts_ = 10; // 最多聚合10个相似告警
    
public:
    /**
     * @brief 添加告警到聚合器
     * 
     * @param alert 要聚合的告警
     * @return 是否应该立即发送告警
     */
    bool addAlert(const RealTimeMonitor::Alert& alert);
    
    /**
     * @brief 获取聚合后的告警摘要
     */
    std::vector<std::string> getAggregatedSummaries();
    
    /**
     * @brief 清理过期的告警组
     */
    void cleanupExpiredGroups();
    
    /**
     * @brief 配置聚合参数
     */
    void setAggregationWindow(std::chrono::seconds window) { aggregation_window_ = window; }
    void setMaxSimilarAlerts(size_t max_alerts) { max_similar_alerts_ = max_alerts; }
};

/**
 * @brief 性能基线管理器
 * 
 * 学习系统的正常性能基线，提供更准确的异常检测
 */
class PerformanceBaseline {
private:
    struct BaselineMetric {
        double mean = 0.0;
        double std_dev = 0.0;
        double min_value = std::numeric_limits<double>::max();
        double max_value = std::numeric_limits<double>::lowest();
        size_t sample_count = 0;
        std::deque<double> recent_samples;
        
        static constexpr size_t MAX_SAMPLES = 1000;
    };
    
    std::unordered_map<std::string, BaselineMetric> baselines_;
    std::mutex baselines_mutex_;
    
    bool learning_mode_ = true;
    std::chrono::hours learning_duration_{24}; // 24小时学习期
    std::chrono::steady_clock::time_point learning_start_;
    
public:
    PerformanceBaseline();
    
    /**
     * @brief 添加性能样本
     */
    void addSample(const std::string& metric_name, double value);
    
    /**
     * @brief 检查值是否异常
     */
    bool isAnomalous(const std::string& metric_name, double value, double sigma_threshold = 3.0) const;
    
    /**
     * @brief 获取基线统计信息
     */
    BaselineMetric getBaseline(const std::string& metric_name) const;
    
    /**
     * @brief 完成学习阶段
     */
    void finalizeLearning();
    
    /**
     * @brief 重置基线
     */
    void resetBaseline(const std::string& metric_name = "");
    
    /**
     * @brief 是否处于学习模式
     */
    bool isLearningMode() const { return learning_mode_; }
};

} // namespace ErrorManagement
