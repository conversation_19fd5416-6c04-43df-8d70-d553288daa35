# 🎯 动态ROI功能使用指南

## 概述

动态ROI（Region of Interest）功能已成功集成到Camera_Editor项目中，旨在提高AI推理效率和3D重建成功率。

## 功能特性

### ✅ 已实现的功能

1. **轨迹预测算法** (`DynamicROIPredictor`)
   - 基于速度矢量的线性预测
   - 考虑重力影响的抛物线轨迹
   - 历史状态管理（最多5个状态点）

2. **SharedData ROI接口**
   - 双目视觉ROI预测数据存储
   - 立体匹配状态管理
   - 线程安全的数据访问

3. **StereoReconstructionService扩展**
   - 复用DualEye的坐标转换能力
   - 自动ROI预测和分发
   - 与现有速度计算逻辑集成

4. **InferenceService ROI支持**
   - ROI推理与全画面推理自动切换
   - 安全机制和回退方案
   - 坐标转换处理

5. **性能监控系统** (`ROIPerformanceMonitor`)
   - 实时性能指标统计
   - 成功率监控
   - 性能报告生成

## 使用方法

### 1. 启用ROI功能

在StereoReconstructionService中启用ROI功能：

```cpp
// 在服务初始化时
stereoReconstructionService->initializeROIFeatures();

// 或者动态启用/禁用
stereoReconstructionService->setROIEnabled(true);
```

### 2. 使用ROI推理

在InferenceService中使用ROI推理：

```cpp
// 创建SharedData实例
auto shared_data = std::make_shared<SharedData>();

// 启用ROI处理模式
shared_data->setROIProcessingMode(true);

// 使用ROI推理
InferenceResult result = inferenceService->processFrameWithROI(
    frame, camera_id, confidence_threshold, shared_data
);
```

### 3. 监控性能

使用ROIPerformanceMonitor监控性能：

```cpp
ROIPerformanceMonitor monitor;

// 记录ROI预测结果
monitor.recordROIPrediction(left_success, right_success, stereo_success, 
                           conversion_time, generation_time);

// 记录推理类型
monitor.recordInference(is_roi_inference, inference_time);

// 打印性能报告
monitor.printPerformanceReport();
```

### 4. 调试信息

启用ROI调试信息：

```cpp
#include "Utils/DebugConfig.hpp"

// 启用ROI调试
DebugConfig::enable_roi_debug = true;

// 使用调试宏
DEBUG_ROI("ROI预测成功，区域: " + roi_info);
```

## 配置参数

### ROI预测参数

- **历史状态数量**: 5个状态点（可在DynamicROIPredictor构造函数中调整）
- **预测时间间隔**: 10ms（0.01秒）
- **ROI尺寸**: 150x150像素（可在generateROIAroundPoint中调整）
- **重力加速度**: 9.81 m/s²

### 安全机制参数

- **最大失败次数**: 10次（InferenceService中的MAX_ROI_FAILURES）
- **ROI最小面积**: 100像素²
- **置信度阈值**: 0.8（ROI预测置信度）

## 性能目标

根据技术指南，ROI功能的性能目标：

| 指标 | 当前值 | 目标值 | 预期改进 |
|------|--------|--------|----------|
| AI推理效率 | 2794次推理 | <1500次推理 | 减少46% |
| 3D重建成功率 | 0.86% | >5% | 提升6倍 |
| 快球检测率 | 低 | >70% | 显著提升 |
| 系统延迟 | 110-215ms | <100ms | 减少10-50% |

## 故障排除

### 常见问题

1. **ROI功能未启用**
   ```cpp
   // 检查ROI处理模式
   if (!shared_data->isROIProcessingEnabled()) {
       shared_data->setROIProcessingMode(true);
   }
   ```

2. **ROI预测失败**
   ```cpp
   // 检查历史数据是否足够
   if (!predictor.canPredict()) {
       // 需要至少2个历史状态点
   }
   ```

3. **立体匹配失败**
   ```cpp
   // 检查立体匹配状态
   if (!shared_data->isStereoMatchable()) {
       // 球可能不在两个摄像头的视野内
   }
   ```

### 调试步骤

1. 启用ROI调试信息
2. 检查ROI预测是否生成
3. 验证坐标转换是否正确
4. 监控性能指标
5. 检查安全机制是否触发

## 集成注意事项

1. **严格遵循服务化架构**: 所有服务通过SharedData通信，禁止直接调用
2. **线程安全**: 所有ROI相关操作都是线程安全的
3. **向后兼容**: ROI功能可以动态启用/禁用，不影响现有功能
4. **性能监控**: 建议在生产环境中启用性能监控

## 文件结构

```
Utils/
├── DynamicROIPredictor.hpp/cpp     # 轨迹预测算法
├── ROIPerformanceMonitor.hpp/cpp   # 性能监控
├── SharedData.hpp                  # 数据总线（已扩展ROI接口）
└── DebugConfig.hpp/cpp             # 调试配置（已添加ROI调试）

Services/
├── StereoReconstructionService.hpp/cpp  # 已扩展ROI预测功能
└── InferenceService.hpp/cpp             # 已集成ROI推理支持

Tools/
└── roi_test.cpp                    # ROI功能测试程序
```

## 下一步计划

1. **性能调优**: 根据实际测试结果调整参数
2. **算法优化**: 可考虑更复杂的预测算法（如卡尔曼滤波）
3. **Web界面集成**: 在Web前端显示ROI预测信息
4. **自动化测试**: 扩展测试覆盖率

---

**📞 技术支持**: <EMAIL>  
**📅 文档更新**: 2025-07-02
