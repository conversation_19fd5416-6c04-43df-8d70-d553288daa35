#pragma once

#include <memory>
#include <atomic>
#include <thread>
#include <functional>
#include <opencv2/opencv.hpp>
#include "../Camera/dualEye.hpp"
#include "../Utils/SharedData.hpp"
#include "../Utils/utf8_utils.hpp"

namespace Services {

/**
 * @brief 相机标定状态枚举
 */
enum class CalibrationStatus {
    IDLE,                    // 空闲状态
    DETECTING_CHESSBOARD,    // 正在检测标定板
    CALCULATING_PARAMETERS,  // 正在计算标定参数
    VALIDATING_RESULTS,      // 正在验证标定结果
    SUCCESS,                 // 标定成功
    FAILED_NO_CHESSBOARD,    // 失败：未检测到标定板
    FAILED_INSUFFICIENT_POINTS, // 失败：角点数量不足
    FAILED_VALIDATION,       // 失败：验证未通过
    FAILED_FILE_WRITE        // 失败：文件写入错误
};

/**
 * @brief 标定结果结构体
 */
struct CalibrationResult {
    CalibrationStatus status;
    std::string message;
    double mean_error = 0.0;
    int detected_corners = 0;
    
    CalibrationResult(CalibrationStatus s, const std::string& msg) 
        : status(s), message(msg) {}
};

/**
 * @brief 相机标定服务
 * 
 * 负责基于标定板的自动相机标定，包括：
 * 1. 检测双目摄像头画面中的棋盘格标定板
 * 2. 提取角点并计算外参矩阵
 * 3. 验证标定精度并保存结果
 * 4. 通过SharedData广播标定状态和结果
 */
class CalibrationService {
public:
    /**
     * @brief 构造函数
     * @param shared_data 共享数据总线指针
     * @param image_width 图像宽度 (默认1440)
     * @param image_height 图像高度 (默认1080)
     */
    explicit CalibrationService(
        std::shared_ptr<SharedData> shared_data,
        int image_width = 1440,
        int image_height = 1080
    );

    /**
     * @brief 析构函数
     */
    ~CalibrationService();

    /**
     * @brief 开始标定过程
     * @param callback 标定完成后的回调函数
     * @return 是否成功启动标定过程
     */
    bool startCalibration(std::function<void(const CalibrationResult&)> callback = nullptr);

    /**
     * @brief 停止当前的标定过程
     */
    void stopCalibration();

    /**
     * @brief 获取当前标定状态
     */
    CalibrationStatus getCurrentStatus() const;

    /**
     * @brief 获取最后一次标定的结果
     */
    CalibrationResult getLastResult() const;

    /**
     * @brief 检查是否正在进行标定
     */
    bool isCalibrating() const;

    /**
     * @brief 设置标定参数
     * @param max_attempts 最大尝试次数
     * @param error_threshold 误差阈值(米)
     */
    void setCalibrationParameters(int max_attempts = 10, double error_threshold = 0.025);

    /**
     * @brief 设置标定成功后的重新加载回调
     * @param callback 标定成功后调用的回调函数
     */
    void setReloadCallback(std::function<void()> callback);

private:
    /**
     * @brief 标定工作线程的主函数
     */
    void calibrationWorker();

    /**
     * @brief 执行单次标定尝试
     * @param left_frame 左摄像头图像
     * @param right_frame 右摄像头图像
     * @return 标定结果
     */
    CalibrationResult performCalibration(cv::Mat& left_frame, cv::Mat& right_frame);

    /**
     * @brief 更新标定状态并通知
     * @param status 新状态
     * @param message 状态消息
     */
    void updateStatus(CalibrationStatus status, const std::string& message = "");

    /**
     * @brief 将状态转换为字符串
     */
    std::string statusToString(CalibrationStatus status) const;

private:
    std::shared_ptr<SharedData> m_sharedData;
    std::unique_ptr<DUE::C_DualEye> m_dualEye;
    
    // 标定状态管理
    std::atomic<CalibrationStatus> m_currentStatus{CalibrationStatus::IDLE};
    std::atomic<bool> m_isCalibrating{false};
    std::atomic<bool> m_shouldStop{false};
    
    // 标定参数
    int m_maxAttempts = 10;
    double m_errorThreshold = 0.025; // 2.5cm
    
    // 线程管理
    std::unique_ptr<std::thread> m_calibrationThread;
    
    // 结果存储
    CalibrationResult m_lastResult{CalibrationStatus::IDLE, "未开始标定"};
    mutable std::mutex m_resultMutex;
    
    // 回调函数
    std::function<void(const CalibrationResult&)> m_completionCallback;
    std::function<void()> m_reloadCallback;
};

} // namespace Services
