#ifndef ROI_PERFORMANCE_MONITOR_HPP
#define ROI_PERFORMANCE_MONITOR_HPP

#include <chrono>
#include <atomic>
#include <string>
#include <mutex>
#include "utf8_utils.hpp"

/**
 * @brief ROI性能监控器
 * 
 * 监控ROI功能的性能指标，包括预测成功率、推理效率等
 */
class ROIPerformanceMonitor {
public:
    /**
     * @brief 性能指标结构体
     */
    struct PerformanceMetrics {
        int total_predictions = 0;
        int successful_left_roi = 0;
        int successful_right_roi = 0;
        int stereo_matching_success = 0;
        int roi_inference_count = 0;
        int fullframe_inference_count = 0;
        double avg_geometry_conversion_time = 0.0;
        double avg_roi_generation_time = 0.0;
        double avg_roi_inference_time = 0.0;
        
        // 计算成功率
        double getLeftROISuccessRate() const {
            return total_predictions > 0 ? (double)successful_left_roi / total_predictions * 100 : 0.0;
        }
        
        double getRightROISuccessRate() const {
            return total_predictions > 0 ? (double)successful_right_roi / total_predictions * 100 : 0.0;
        }
        
        double getStereoMatchingSuccessRate() const {
            return total_predictions > 0 ? (double)stereo_matching_success / total_predictions * 100 : 0.0;
        }
        
        double getROIInferenceRatio() const {
            int total_inferences = roi_inference_count + fullframe_inference_count;
            return total_inferences > 0 ? (double)roi_inference_count / total_inferences * 100 : 0.0;
        }
    };

    /**
     * @brief 构造函数
     */
    ROIPerformanceMonitor();

    /**
     * @brief 记录ROI预测结果
     * @param left_success 左摄像头ROI是否成功
     * @param right_success 右摄像头ROI是否成功
     * @param stereo_success 立体匹配是否成功
     * @param conversion_time 几何转换时间 (毫秒)
     * @param generation_time ROI生成时间 (毫秒)
     */
    void recordROIPrediction(bool left_success, bool right_success, bool stereo_success,
                           double conversion_time, double generation_time);

    /**
     * @brief 记录推理类型
     * @param is_roi_inference 是否为ROI推理
     * @param inference_time 推理时间 (毫秒)
     */
    void recordInference(bool is_roi_inference, double inference_time);

    /**
     * @brief 获取当前性能指标
     * @return 性能指标
     */
    PerformanceMetrics getMetrics() const;

    /**
     * @brief 打印性能报告
     */
    void printPerformanceReport() const;

    /**
     * @brief 重置统计数据
     */
    void reset();

    /**
     * @brief 检查是否达到性能目标
     * @return 是否达到目标
     */
    bool isPerformanceTargetMet() const;

private:
    mutable std::mutex m_mutex;
    PerformanceMetrics m_metrics;
    std::chrono::steady_clock::time_point m_startTime;

    // 移动平均计算
    void updateMovingAverage(double& avg, double new_value, double alpha = 0.1);
};

#endif // ROI_PERFORMANCE_MONITOR_HPP
