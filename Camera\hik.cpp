#include "Hik.hpp"
#include <thread>
#include <deque>
#include <getTimeStamp.h>
#include <CameraParams.h>


Hik::Camera::Camera(std::string id, MV_CC_DEVICE_INFO_LIST* pInfoList)
{
    memset(&mvFrame, 0, sizeof(MV_FRAME_OUT));
    memset(&imgInfo, 0, sizeof(MV_FRAME_OUT_INFO_EX));
    memset(&convert, 0, sizeof(MV_CC_PIXEL_CONVERT_PARAM));
    this->id = id;
    this->pInfoList = pInfoList;
    bind();
    initCam();
    convertInit();
}
Hik::Camera::~Camera()
{
    if (this->currentRecordingPath) {
        std::cout << "Camera " << id << " destructor: Freeing potentially dangling recording path memory." << std::endl;
        delete[] this->currentRecordingPath;
        this->currentRecordingPath = nullptr;
    }
    convertFree();
    //9.停止截流
    MV_CC_StopGrabbing(this->handle);

    //10.关闭设备
    MV_CC_CloseDevice(this->handle);

    //11.销毁相机句柄
    MV_CC_DestroyHandle(this->handle);

}



Hik::Hik()
{
    enumCams();
}
Hik::~Hik()
{

}
bool Hik::Camera::bind()
{
    bool ret = false;
    for (int i = 0; i < pInfoList->nDeviceNum; i++)
    {
        std::string id((const char*)pInfoList->pDeviceInfo[i]->SpecialInfo.stUsb3VInfo.chSerialNumber);
        std::cout << "检查相机 #" << i << ": " << id << ", 要匹配的ID: " << this->id << std::endl;
        
        if (id.compare(this->id) == 0)
        {
            printf("绑定成功:[%s]\n", id.c_str());
            this->pDeviceInfo = pInfoList->pDeviceInfo[i];
            return true; // 找到匹配的相机后立即返回
        }
    }
    
    // 如果循环结束后没有找到匹配的相机
    std::cerr << "错误：未找到相机ID " << this->id << std::endl;
    throw std::invalid_argument("camera not found!!!");
    return false; // 这行代码实际上不会执行，因为上面已经抛出异常
}
void Hik::enumCams()
{
    memset(&infoList, 0, sizeof(MV_CC_DEVICE_INFO_LIST));
    //1.获取SDK版本信息
    unsigned int sdk_V = MV_CC_GetSDKVersion();
    printf("SDK version is [0x%x]\n", sdk_V);
    //2.查找目标设备
    int ret = MV_OK;
    ret = MV_CC_EnumDevices(MV_GIGE_DEVICE | MV_USB_DEVICE, &infoList);
    if (ret != MV_OK) {
        throw std::invalid_argument("enum devices faild!");
    }
    std::cout << "cam detected num:" << infoList.nDeviceNum << std::endl;
    //3.判断设备是否可访问
    if (infoList.nDeviceNum > 0)
    {
        int accessibleCount = 0;
        for (unsigned int i = 0; i < infoList.nDeviceNum; i++)
        {
            bool access = MV_CC_IsDeviceAccessible(infoList.pDeviceInfo[i], 1);
            if (!access) {
                printf("[device %d]:could not access... nRet:[0x%x]\n", i, ret);
                printf("  设备可能被其他程序占用或权限不足\n");
            }
            else {
                printf("[device %d]:is OK... nRet:[0x%x]\n", i, ret);
                accessibleCount++;
            }
            printDeviceInfo(infoList.pDeviceInfo[i]);
        }
        
        if (accessibleCount == 0) {
            printf("警告：检测到相机但无法访问任何设备！\n");
            printf("请检查：\n");
            printf("1. 是否有其他程序正在使用相机\n");
            printf("2. 是否以管理员权限运行程序\n");
            printf("3. USB连接是否稳定\n");
            throw std::invalid_argument("No accessible cameras found");
        }
    }
    else
    {
        throw std::invalid_argument("Find No Devices!");
    }
}
bool Hik::Camera::initCam()
{
    int ret = MV_OK;
    //4.创建相机句柄
    ret = MV_CC_CreateHandle(&this->handle, this->pDeviceInfo);
    if (ret != MV_OK) {
        throw std::invalid_argument("");
    }
    //5.打开设备
    ret = MV_CC_OpenDevice(this->handle);
    if (ret != MV_OK) {
        std::cout << "设备未打开" << std::endl;
        throw std::invalid_argument("");
    }
    else {
        std::cout << "设备已经打开" << std::endl;
    }
    //6.设置触发模式为off
    ret = MV_CC_SetEnumValue(this->handle, "TriggerMode", 0);
    if (ret != MV_OK) {
        throw std::invalid_argument("");
    }
    //7.获取数据包的大小
    MVCC_INTVALUE hk_param;
    memset(&hk_param, 0, sizeof(MVCC_INTVALUE));
    ret = MV_CC_GetIntValue(this->handle, "PayloadSize", &hk_param);
    if (ret != MV_OK) {
        throw std::invalid_argument("");
    }
    this->payload_size = hk_param.nCurValue;

    memset(&hk_param, 0, sizeof(MVCC_INTVALUE));
    ret = MV_CC_GetIntValue(this->handle, "Width", &hk_param);
    if (ret != MV_OK) {
        throw std::invalid_argument("");
    }
    this->width = hk_param.nCurValue;

    memset(&hk_param, 0, sizeof(MVCC_INTVALUE));
    ret = MV_CC_GetIntValue(this->handle, "Height", &hk_param);
    if (ret != MV_OK) {
        throw std::invalid_argument("");
    }
    this->height = hk_param.nCurValue;

    // load config
    //ret = MV_CC_FeatureLoad(handle, "FeatureFile.ini");
    //if (ret != MV_OK) {
    //    cout << "loading config file faild" << endl;
    //    return -1;
    //}

    // save config
    //ret = MV_CC_FeatureSave(handle, "FeatureFile.ini");
    //if (ret != MV_OK) {
    //    return -1;
    //}

    //8.开始截流
    ret = MV_CC_StartGrabbing(this->handle);
    if (ret != MV_OK) {
        throw std::invalid_argument("grab image failed!");
    }
    
    // 初始化成功，返回true
    return true;
}

void Hik::Camera::convertInit()
{
#if USE_TIMEOUT_READ_FRAME==1
    this->convert.pSrcData = (unsigned char*)malloc(sizeof(unsigned char) * (this->payload_size));
    if (this->convert.pSrcData == NULL) {
        throw "内存申请失败";
    }
#endif // USE_TIMEOUT_READ_FRAME

    this->convert.pDstBuffer = (unsigned char*)malloc(sizeof(unsigned char) * (this->height * this->width * 3));
    if (this->convert.pDstBuffer == NULL) {
        throw "内存申请失败";
    }

    this->convert.nSrcDataLen = payload_size;
    this->convert.nDstBufferSize = height * width * 3;

    this->convert.nHeight = height;
    this->convert.nWidth = width;

    this->convert.enSrcPixelType = PixelType_Gvsp_BayerRG8;
    this->convert.enDstPixelType = PixelType_Gvsp_BGR8_Packed;
}
void Hik::Camera::convertFree()
{
#if USE_TIMEOUT_READ_FRAME==1
    free(this->convert.pSrcData);
#endif // USE_TIMEOUT_READ_FRAME
    free(this->convert.pDstBuffer);
}

bool Hik::Camera::read(cv::Mat& src_img)
{
    cv::Mat cv_img;
    int ret = MV_OK;

#if USE_TIMEOUT_READ_FRAME==1
    ret = MV_CC_GetOneFrameTimeout(this->handle, this->convert.pSrcData, this->convert.nSrcDataLen, &this->imgInfo, 1000);
    if (ret != MV_OK) {
        DBUG_SHOW(DBUG_ERROR, DATA_SAVE, "read frame fail\n");
        return false;
    }
#else
    ret = MV_CC_GetImageBuffer(this->handle, &this->mvFrame, 1000);
    if (ret != MV_OK) {
        // DBUG_SHOW(DBUG_ERROR, DATA_SAVE, "read frame fail\n");
        return false;
    }
    this->convert.pSrcData = this->mvFrame.pBufAddr;
    this->convert.nSrcDataLen = payload_size;
#endif // USE_TIMEOUT_READ_FRAME==1

    MV_CC_ConvertPixelType(this->handle, &convert);
    src_img = cv::Mat(convert.nHeight, convert.nWidth, CV_8UC3, convert.pDstBuffer);
    if (src_img.data == NULL) {
        //DBUG_SHOW(DBUG_ERROR, DATA_SAVE, "convert frame fail\n");
        return false;
    }

#if USE_TIMEOUT_READ_FRAME==0
    MV_CC_FreeImageBuffer(this->handle, &this->mvFrame);
#endif

    return true;
}


void Hik::printDeviceInfo(MV_CC_DEVICE_INFO* pDeviceInfo) {
    if (NULL == pDeviceInfo)
    {
        throw std::invalid_argument("The Pointer of hk_device is NULL!");
    }
    if (pDeviceInfo->nTLayerType == MV_GIGE_DEVICE)
    {
        int nIp1 = ((pDeviceInfo->SpecialInfo.stGigEInfo.nCurrentIp & 0xff000000) >> 24);
        int nIp2 = ((pDeviceInfo->SpecialInfo.stGigEInfo.nCurrentIp & 0x00ff0000) >> 16);
        int nIp3 = ((pDeviceInfo->SpecialInfo.stGigEInfo.nCurrentIp & 0x0000ff00) >> 8);
        int nIp4 = (pDeviceInfo->SpecialInfo.stGigEInfo.nCurrentIp & 0x000000ff);

        // print current ip and user defined name
        printf("CurrentIp: %d.%d.%d.%d\n", nIp1, nIp2, nIp3, nIp4);
        printf("UserDefinedName: %s\n\n", pDeviceInfo->SpecialInfo.stGigEInfo.chUserDefinedName);
    }
    else if (pDeviceInfo->nTLayerType == MV_USB_DEVICE)
    {
        printf("UserDefinedName: %s\n", pDeviceInfo->SpecialInfo.stUsb3VInfo.chUserDefinedName);
        printf("Serial Number: %s\n", pDeviceInfo->SpecialInfo.stUsb3VInfo.chSerialNumber);
        printf("Device Number: %d\n\n", pDeviceInfo->SpecialInfo.stUsb3VInfo.nDeviceNumber);
    }
    else
    {
        throw std::invalid_argument("Not supported.");
    }
}

void savePic(cv::Mat pic) {
	long long ts = Get_time::get_timestamp_us();
	std::string dir = "./CapCeli/";
	std::string name = std::to_string(ts) + ".png";
	std::string path = dir + name;
	if (!std::filesystem::exists(dir)) {
		std::filesystem::create_directories(dir);
	}
	cv::imwrite(path, pic);

}
void savePicCeli(cv::Mat left, cv::Mat right) {
	long long ts = Get_time::get_timestamp_us();
	std::string dir = "./CapCeli/";
	std::string name_left = std::to_string(ts) + "_left.png";
	std::string name_right = std::to_string(ts) + "_right.png";
	std::string path_left = dir + name_left;
	std::string path_right = dir + name_right;
	if (!std::filesystem::exists(dir)) {
		std::filesystem::create_directories(dir);
	}
	cv::imwrite(path_left, left);
	cv::imwrite(path_right, right);
}

float Hik::Camera::GetExposure() {
    MVCC_FLOATVALUE stFloatValue = {0};
    int nRet = MV_CC_GetFloatValue(handle, "ExposureTime", &stFloatValue);
    if (MV_OK != nRet)
    {
        printf("Get ExposureTime fail! nRet [0x%x]\n", nRet);
        return -1.0f;
    }
    return stFloatValue.fCurValue;
}

float Hik::Camera::GetGain() {
    MVCC_FLOATVALUE stFloatValue = {0};
    int nRet = MV_CC_GetFloatValue(handle, "Gain", &stFloatValue);
    if (MV_OK != nRet)
    {
        printf("Get Gain fail! nRet [0x%x]\n", nRet);
        return -1.0f;
    }
    return stFloatValue.fCurValue;
}


