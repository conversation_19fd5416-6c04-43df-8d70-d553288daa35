#include "CalibrationService.hpp"
#include <chrono>
#include <thread>

namespace Services {

CalibrationService::CalibrationService(
    std::shared_ptr<SharedData> shared_data,
    int image_width,
    int image_height
) : m_sharedData(std::move(shared_data)) {
    
    // 初始化双目视觉系统
    m_dualEye = std::make_unique<DUE::C_DualEye>(image_width, image_height);
    
    UTF8Utils::println("🎯 相机标定服务已初始化 (" + 
                      std::to_string(image_width) + "x" + std::to_string(image_height) + ")");
}

CalibrationService::~CalibrationService() {
    stopCalibration();
}

bool CalibrationService::startCalibration(std::function<void(const CalibrationResult&)> callback) {
    // 检查是否已经在标定中
    if (m_isCalibrating.load()) {
        UTF8Utils::println("⚠️ 标定已在进行中，请等待当前标定完成");
        return false;
    }
    
    // 停止之前的线程（如果存在）
    stopCalibration();
    
    // 设置回调函数
    m_completionCallback = callback;
    
    // 重置状态
    m_shouldStop.store(false);
    m_isCalibrating.store(true);
    updateStatus(CalibrationStatus::DETECTING_CHESSBOARD, "开始检测标定板...");
    
    // 启动标定线程
    m_calibrationThread = std::make_unique<std::thread>(&CalibrationService::calibrationWorker, this);
    
    UTF8Utils::println("🎯 相机标定过程已启动");
    return true;
}

void CalibrationService::stopCalibration() {
    if (m_isCalibrating.load()) {
        m_shouldStop.store(true);
        
        if (m_calibrationThread && m_calibrationThread->joinable()) {
            m_calibrationThread->join();
        }
        
        m_isCalibrating.store(false);
        updateStatus(CalibrationStatus::IDLE, "标定已停止");
        UTF8Utils::println("🛑 相机标定过程已停止");
    }
}

CalibrationStatus CalibrationService::getCurrentStatus() const {
    return m_currentStatus.load();
}

CalibrationResult CalibrationService::getLastResult() const {
    std::lock_guard<std::mutex> lock(m_resultMutex);
    return m_lastResult;
}

bool CalibrationService::isCalibrating() const {
    return m_isCalibrating.load();
}

void CalibrationService::setCalibrationParameters(int max_attempts, double error_threshold) {
    m_maxAttempts = max_attempts;
    m_errorThreshold = error_threshold;
    UTF8Utils::println("🔧 标定参数已更新: 最大尝试次数=" + std::to_string(max_attempts) +
                      ", 误差阈值=" + std::to_string(error_threshold) + "m");
}

void CalibrationService::setReloadCallback(std::function<void()> callback) {
    m_reloadCallback = callback;
    UTF8Utils::println("🔧 标定重新加载回调已设置");
}

void CalibrationService::calibrationWorker() {
    UTF8Utils::println("🔄 标定工作线程已启动");
    
    int attempt = 0;
    CalibrationResult result{CalibrationStatus::FAILED_NO_CHESSBOARD, "未找到标定板"};
    
    while (attempt < m_maxAttempts && !m_shouldStop.load()) {
        attempt++;
        
        updateStatus(CalibrationStatus::DETECTING_CHESSBOARD, 
                    "尝试 " + std::to_string(attempt) + "/" + std::to_string(m_maxAttempts) + " - 检测标定板...");
        
        // 获取最新的双目图像
        cv::Mat left_frame = m_sharedData->getLatestFrame(1);  // 左摄像头
        cv::Mat right_frame = m_sharedData->getLatestFrame(2); // 右摄像头
        
        if (left_frame.empty() || right_frame.empty()) {
            UTF8Utils::println("⚠️ 无法获取摄像头图像，等待重试...");
            std::this_thread::sleep_for(std::chrono::milliseconds(500));
            continue;
        }
        
        // 执行标定
        result = performCalibration(left_frame, right_frame);
        
        if (result.status == CalibrationStatus::SUCCESS) {
            UTF8Utils::println("✅ 标定成功完成！");

            // 调用重新加载回调，通知其他服务重新加载标定数据
            if (m_reloadCallback) {
                UTF8Utils::println("🔄 通知其他服务重新加载标定数据...");
                m_reloadCallback();
            }

            break;
        }
        
        // 如果不是因为检测不到标定板而失败，则立即退出
        if (result.status != CalibrationStatus::FAILED_NO_CHESSBOARD) {
            UTF8Utils::println("❌ 标定失败: " + result.message);
            break;
        }
        
        // 等待一段时间后重试
        std::this_thread::sleep_for(std::chrono::milliseconds(1000));
    }
    
    // 如果所有尝试都失败了
    if (result.status != CalibrationStatus::SUCCESS && attempt >= m_maxAttempts) {
        result = CalibrationResult{CalibrationStatus::FAILED_NO_CHESSBOARD, 
                                 "经过 " + std::to_string(m_maxAttempts) + " 次尝试仍未检测到标定板"};
    }
    
    // 更新最终状态
    updateStatus(result.status, result.message);
    
    {
        std::lock_guard<std::mutex> lock(m_resultMutex);
        m_lastResult = result;
    }
    
    // 调用回调函数
    if (m_completionCallback) {
        m_completionCallback(result);
    }
    
    m_isCalibrating.store(false);
    UTF8Utils::println("🏁 标定工作线程已结束");
}

CalibrationResult CalibrationService::performCalibration(cv::Mat& left_frame, cv::Mat& right_frame) {
    try {
        updateStatus(CalibrationStatus::DETECTING_CHESSBOARD, "开始相机标定...");

        // 使用 resetExternalRT 方法，它封装了完整的标定流程
        bool calibration_success = m_dualEye->resetExternalRT(left_frame, right_frame);

        if (!calibration_success) {
            return CalibrationResult{CalibrationStatus::FAILED_NO_CHESSBOARD, "标定失败，可能是未检测到标定板或标定精度不足"};
        }

        UTF8Utils::println("✅ 相机标定成功完成");

        // 创建成功结果
        CalibrationResult result{CalibrationStatus::SUCCESS, "标定成功完成"};
        result.mean_error = 0.0; // resetExternalRT 返回 bool，无法获取具体误差值
        result.detected_corners = 88; // 8x11 棋盘格的理论角点数

        return result;

    } catch (const std::exception& e) {
        return CalibrationResult{CalibrationStatus::FAILED_VALIDATION,
                               "标定过程中发生异常: " + std::string(e.what())};
    }
}

void CalibrationService::updateStatus(CalibrationStatus status, const std::string& message) {
    m_currentStatus.store(status);
    
    std::string status_str = statusToString(status);
    if (!message.empty()) {
        UTF8Utils::println("🎯 [标定状态] " + status_str + ": " + message);
    } else {
        UTF8Utils::println("🎯 [标定状态] " + status_str);
    }
    
    // TODO: 通过SharedData广播状态更新到Web界面
    // 这里可以添加WebSocket广播逻辑
}

std::string CalibrationService::statusToString(CalibrationStatus status) const {
    switch (status) {
        case CalibrationStatus::IDLE: return "空闲";
        case CalibrationStatus::DETECTING_CHESSBOARD: return "检测标定板";
        case CalibrationStatus::CALCULATING_PARAMETERS: return "计算参数";
        case CalibrationStatus::VALIDATING_RESULTS: return "验证结果";
        case CalibrationStatus::SUCCESS: return "成功";
        case CalibrationStatus::FAILED_NO_CHESSBOARD: return "未检测到标定板";
        case CalibrationStatus::FAILED_INSUFFICIENT_POINTS: return "角点数量不足";
        case CalibrationStatus::FAILED_VALIDATION: return "验证失败";
        case CalibrationStatus::FAILED_FILE_WRITE: return "文件写入失败";
        default: return "未知状态";
    }
}

} // namespace Services
