#ifndef UTF8_UTILS_HPP
#define UTF8_UTILS_HPP

#include <iostream>
#include <string>

#ifdef _WIN32
#include <windows.h>
#include <io.h>
#include <fcntl.h>
#endif

namespace UTF8Utils {
    // 初始化控制台UTF-8支持
    inline void initConsole() {
#ifdef _WIN32
        // 设置控制台代码页为UTF-8
        SetConsoleOutputCP(CP_UTF8);
        SetConsoleCP(CP_UTF8);
        
        // 启用控制台虚拟终端序列支持
        HANDLE hOut = GetStdHandle(STD_OUTPUT_HANDLE);
        if (hOut != INVALID_HANDLE_VALUE) {
            DWORD dwMode = 0;
            if (GetConsoleMode(hOut, &dwMode)) {
                dwMode |= ENABLE_VIRTUAL_TERMINAL_PROCESSING;
                SetConsoleMode(hOut, dwMode);
            }
        }
#endif
    }
    
    // 跨平台的中文输出函数
    template<typename T>
    inline void print(const T& content) {
#ifdef _WIN32
        std::cout << content;
#else
        std::cout << content;
#endif
    }
    
    template<typename T>
    inline void println(const T& content) {
#ifdef _WIN32
        std::cout << content << std::endl;
#else
        std::cout << content << std::endl;
#endif
    }
}

#endif // UTF8_UTILS_HPP 