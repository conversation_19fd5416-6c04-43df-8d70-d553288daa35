#ifndef DYNAMIC_ROI_PREDICTOR_HPP
#define DYNAMIC_ROI_PREDICTOR_HPP

#include <opencv2/opencv.hpp>
#include <deque>
#include <chrono>

/**
 * @brief 动态ROI轨迹预测器
 * 
 * 基于历史3D位置数据预测乒乓球的下一帧位置，用于生成动态ROI区域
 * 采用简化的线性预测算法，考虑重力影响的抛物线轨迹
 */
class DynamicROIPredictor {
public:
    /**
     * @brief 预测状态结构体
     */
    struct PredictionState {
        cv::Point3f position;      // 当前3D位置 (世界坐标系)
        cv::Point3f velocity;      // 速度矢量 (m/s)
        std::chrono::high_resolution_clock::time_point timestamp;  // 时间戳
        
        PredictionState() : position(0, 0, 0), velocity(0, 0, 0) {}
        PredictionState(const cv::Point3f& pos, const cv::Point3f& vel, 
                       const std::chrono::high_resolution_clock::time_point& ts)
            : position(pos), velocity(vel), timestamp(ts) {}
    };

    /**
     * @brief 构造函数
     * @param max_history 最大历史记录数量 (默认5)
     */
    explicit DynamicROIPredictor(size_t max_history = 5);

    /**
     * @brief 更新历史状态
     * @param position 新的3D位置
     * @param dt 时间间隔 (秒)
     */
    void updateState(const cv::Point3f& position, double dt);

    /**
     * @brief 预测下一帧位置
     * @param dt 预测时间间隔 (秒)
     * @return 预测的3D位置
     */
    cv::Point3f predictNextPosition(double dt);

    /**
     * @brief 获取当前速度矢量
     * @return 当前速度矢量 (m/s)
     */
    cv::Point3f getCurrentVelocity() const;

    /**
     * @brief 检查预测器是否有足够的历史数据
     * @return 是否可以进行预测
     */
    bool canPredict() const;

    /**
     * @brief 清空历史记录
     */
    void reset();

    /**
     * @brief 获取历史记录数量
     * @return 当前历史记录数量
     */
    size_t getHistorySize() const;

private:
    std::deque<PredictionState> m_history;  // 历史状态队列
    const size_t m_maxHistory;              // 最大历史记录数量
    
    // 物理常数
    static constexpr float GRAVITY = 9.81f;  // 重力加速度 (m/s²)
    
    /**
     * @brief 计算速度矢量
     * @param current_pos 当前位置
     * @param previous_pos 前一位置
     * @param dt 时间间隔
     * @return 速度矢量
     */
    cv::Point3f calculateVelocity(const cv::Point3f& current_pos, 
                                 const cv::Point3f& previous_pos, 
                                 double dt);

    /**
     * @brief 应用重力影响的抛物线轨迹预测
     * @param initial_pos 初始位置
     * @param initial_vel 初始速度
     * @param dt 时间间隔
     * @return 预测位置
     */
    cv::Point3f applyGravityPrediction(const cv::Point3f& initial_pos,
                                      const cv::Point3f& initial_vel,
                                      double dt);
};

#endif // DYNAMIC_ROI_PREDICTOR_HPP
