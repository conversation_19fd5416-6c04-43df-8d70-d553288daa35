#pragma once

#include <queue>
#include <mutex>
#include <condition_variable>
#include <memory>

template<typename T>
class ThreadSafeQueue {
public:
    ThreadSafeQueue() = default;
    ThreadSafeQueue(const ThreadSafeQueue&) = delete;
    ThreadSafeQueue& operator=(const ThreadSafeQueue&) = delete;

    void push(T new_value) {
        std::lock_guard<std::mutex> lock(mut_);
        data_queue_.push(std::move(new_value));
        data_cond_.notify_one();
    }

    bool try_pop(T& value) {
        std::lock_guard<std::mutex> lock(mut_);
        if (data_queue_.empty()) {
            return false;
        }
        value = std::move(data_queue_.front());
        data_queue_.pop();
        return true;
    }

    void wait_and_pop(T& value) {
        std::unique_lock<std::mutex> lock(mut_);
        data_cond_.wait(lock, [this] { return !data_queue_.empty(); });
        value = std::move(data_queue_.front());
        data_queue_.pop();
    }

    template<typename Rep, typename Period>
    bool wait_and_pop_timeout(T& value, const std::chrono::duration<Rep, Period>& timeout) {
        std::unique_lock<std::mutex> lock(mut_);
        if (data_cond_.wait_for(lock, timeout, [this] { return !data_queue_.empty(); })) {
            value = std::move(data_queue_.front());
            data_queue_.pop();
            return true;
        }
        return false;
    }

    bool empty() const {
        std::lock_guard<std::mutex> lock(mut_);
        return data_queue_.empty();
    }

private:
    mutable std::mutex mut_;
    std::queue<T> data_queue_;
    std::condition_variable data_cond_;
}; 